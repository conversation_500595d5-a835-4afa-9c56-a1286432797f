-- 测试 Headscale 用户删除功能的 SQL 脚本

-- 查看删除前的 Headscale 用户记录
SELECT 
    h.id,
    h.username,
    h.display_name,
    h.headscale_user_id,
    h.acs_user_id,
    h.status,
    h.created_at,
    u.name as acs_username,
    u.company as acs_company,
    u.del as acs_deleted
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 1
ORDER BY h.created_at DESC;

-- 查看被删除的 ACS 用户
SELECT 
    id,
    name,
    company,
    del,
    gmt_modify
FROM acs_user 
WHERE del > 0 
ORDER BY gmt_modify DESC 
LIMIT 10;

-- 查看被逻辑删除的 Headscale 用户记录
SELECT 
    h.id,
    h.username,
    h.display_name,
    h.headscale_user_id,
    h.acs_user_id,
    h.status,
    h.updated_at,
    u.name as acs_username,
    u.del as acs_deleted
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 0
ORDER BY h.updated_at DESC;

-- 统计 Headscale 用户记录状态
SELECT 
    status,
    COUNT(*) as count,
    CASE 
        WHEN status = 1 THEN '正常'
        WHEN status = 0 THEN '已删除'
        ELSE '未知'
    END as status_desc
FROM acs_headscale_users 
GROUP BY status;

-- 查找可能的数据不一致情况
-- 1. ACS 用户已删除但 Headscale 记录仍为正常状态
SELECT 
    h.id as headscale_record_id,
    h.username,
    h.acs_user_id,
    h.status as headscale_status,
    u.del as acs_deleted,
    'ACS用户已删除但Headscale记录未删除' as issue
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 1 AND u.del > 0;

-- 2. ACS 用户正常但 Headscale 记录已删除
SELECT 
    h.id as headscale_record_id,
    h.username,
    h.acs_user_id,
    h.status as headscale_status,
    u.del as acs_deleted,
    'ACS用户正常但Headscale记录已删除' as issue
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 0 AND u.del = 0;

-- 清理测试数据（如果需要）
-- 注意：这些操作会永久删除数据，请谨慎使用

-- 恢复被逻辑删除的 Headscale 用户记录（用于测试）
-- UPDATE acs_headscale_users SET status = 1, updated_at = NOW() WHERE status = 0 AND username LIKE 'test%';

-- 物理删除测试数据
-- DELETE FROM acs_headscale_users WHERE username LIKE 'test%';
-- DELETE FROM acs_user WHERE name LIKE 'test%';
