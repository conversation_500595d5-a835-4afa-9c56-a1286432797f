-- 测试 PreAuth Key 过期时间功能的 SQL 脚本
-- 注意：这个脚本主要用于验证数据一致性，实际的过期时间测试需要通过 API 进行

-- 查看当前时间（用于对比过期时间）
SELECT NOW() as current_time, 
       DATE_ADD(NOW(), INTERVAL 24 HOUR) as default_expiration_24h,
       DATE_ADD(NOW(), INTERVAL 48 HOUR) as custom_expiration_48h,
       DATE_ADD(NOW(), INTERVAL 168 HOUR) as weekly_expiration_168h;

-- 如果有 PreAuth Key 相关的数据表，可以查询过期情况
-- 注意：Headscale 的 PreAuth Key 数据通常存储在 Headscale 自己的数据库中
-- 这里只是示例查询，实际表结构可能不同

-- 示例：查询即将过期的 PreAuth Key（假设有相关表）
/*
SELECT 
    id,
    key_name,
    username,
    expiration_time,
    TIMESTAMPDIFF(HOUR, NOW(), expiration_time) as hours_until_expiry,
    CASE 
        WHEN expiration_time < NOW() THEN '已过期'
        WHEN TIMESTAMPDIFF(HOUR, NOW(), expiration_time) <= 1 THEN '1小时内过期'
        WHEN TIMESTAMPDIFF(HOUR, NOW(), expiration_time) <= 24 THEN '24小时内过期'
        ELSE '正常'
    END as status
FROM preauth_keys 
WHERE expiration_time IS NOT NULL
ORDER BY expiration_time ASC;
*/

-- 测试时间格式转换（ISO 8601 格式）
SELECT 
    '2024-12-31T23:59:59Z' as iso_format,
    STR_TO_DATE('2024-12-31T23:59:59Z', '%Y-%m-%dT%H:%i:%sZ') as mysql_datetime,
    UNIX_TIMESTAMP(STR_TO_DATE('2024-12-31T23:59:59Z', '%Y-%m-%dT%H:%i:%sZ')) as unix_timestamp;

-- 验证不同过期时间的计算
SELECT 
    'Default 24h' as scenario,
    NOW() as start_time,
    DATE_ADD(NOW(), INTERVAL 24 HOUR) as expiration_time,
    DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 24 HOUR), '%Y-%m-%dT%H:%i:%sZ') as iso_format
UNION ALL
SELECT 
    'Custom 48h' as scenario,
    NOW() as start_time,
    DATE_ADD(NOW(), INTERVAL 48 HOUR) as expiration_time,
    DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 48 HOUR), '%Y-%m-%dT%H:%i:%sZ') as iso_format
UNION ALL
SELECT 
    'Weekly 168h' as scenario,
    NOW() as start_time,
    DATE_ADD(NOW(), INTERVAL 168 HOUR) as expiration_time,
    DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 168 HOUR), '%Y-%m-%dT%H:%i:%sZ') as iso_format;

-- 测试边界值情况
SELECT 
    'Zero hours' as test_case,
    CASE WHEN 0 > 0 THEN 0 ELSE 24 END as effective_hours,
    DATE_ADD(NOW(), INTERVAL CASE WHEN 0 > 0 THEN 0 ELSE 24 END HOUR) as expiration_time
UNION ALL
SELECT 
    'Negative hours' as test_case,
    CASE WHEN -5 > 0 THEN -5 ELSE 24 END as effective_hours,
    DATE_ADD(NOW(), INTERVAL CASE WHEN -5 > 0 THEN -5 ELSE 24 END HOUR) as expiration_time
UNION ALL
SELECT 
    'NULL hours' as test_case,
    CASE WHEN NULL > 0 THEN NULL ELSE 24 END as effective_hours,
    DATE_ADD(NOW(), INTERVAL CASE WHEN NULL > 0 THEN NULL ELSE 24 END HOUR) as expiration_time;

-- 验证时区处理（UTC vs 本地时间）
SELECT 
    NOW() as local_time,
    UTC_TIMESTAMP() as utc_time,
    CONVERT_TZ(NOW(), @@session.time_zone, '+00:00') as local_to_utc,
    DATE_FORMAT(UTC_TIMESTAMP(), '%Y-%m-%dT%H:%i:%sZ') as utc_iso_format;

-- 模拟 API 请求参数验证
SELECT 
    'Valid parameters' as test_case,
    'testuser' as username,
    true as reusable,
    false as ephemeral,
    24 as expiration_hours,
    DATE_FORMAT(DATE_ADD(UTC_TIMESTAMP(), INTERVAL 24 HOUR), '%Y-%m-%dT%H:%i:%sZ') as calculated_expiration
UNION ALL
SELECT 
    'Default parameters' as test_case,
    'testuser2' as username,
    false as reusable,
    false as ephemeral,
    NULL as expiration_hours,
    DATE_FORMAT(DATE_ADD(UTC_TIMESTAMP(), INTERVAL 24 HOUR), '%Y-%m-%dT%H:%i:%sZ') as calculated_expiration;

-- 性能测试：批量时间计算
SELECT 
    'Performance test' as description,
    COUNT(*) as calculations,
    MIN(DATE_FORMAT(DATE_ADD(UTC_TIMESTAMP(), INTERVAL hours.hour_value HOUR), '%Y-%m-%dT%H:%i:%sZ')) as min_expiration,
    MAX(DATE_FORMAT(DATE_ADD(UTC_TIMESTAMP(), INTERVAL hours.hour_value HOUR), '%Y-%m-%dT%H:%i:%sZ')) as max_expiration
FROM (
    SELECT 1 as hour_value UNION ALL SELECT 24 UNION ALL SELECT 48 UNION ALL 
    SELECT 72 UNION ALL SELECT 168 UNION ALL SELECT 720
) as hours;
