-- 测试 PreAuth Key Redis 集成功能的 SQL 脚本
-- 这个脚本主要用于验证数据库中的用户与 Redis 中的 PreAuth Key 的关联关系

-- 查看当前的 ACS 用户列表
SELECT 
    id,
    name as username,
    company,
    role,
    status,
    gmt_create,
    del
FROM acs_user 
WHERE del = 0 AND status = 1
ORDER BY gmt_create DESC
LIMIT 10;

-- 查看 Headscale 用户记录
SELECT 
    h.id,
    h.username,
    h.display_name,
    h.headscale_user_id,
    h.acs_user_id,
    h.status,
    h.created_at,
    u.name as acs_username,
    u.company as acs_company
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 1
ORDER BY h.created_at DESC;

-- 查看最近创建的用户（可能有 PreAuth Key）
SELECT 
    u.id,
    u.name as username,
    u.company,
    u.gmt_create,
    h.username as headscale_username,
    h.display_name,
    h.created_at as headscale_created_at,
    CONCAT(u.name, '-headscale') as expected_redis_key
FROM acs_user u
LEFT JOIN acs_headscale_users h ON u.id = h.acs_user_id AND h.status = 1
WHERE u.del = 0 AND u.status = 1
ORDER BY u.gmt_create DESC
LIMIT 20;

-- 统计 Headscale 用户创建情况
SELECT 
    DATE(h.created_at) as creation_date,
    COUNT(*) as users_created,
    COUNT(CASE WHEN h.status = 1 THEN 1 END) as active_users,
    COUNT(CASE WHEN h.status = 0 THEN 1 END) as deleted_users
FROM acs_headscale_users h
GROUP BY DATE(h.created_at)
ORDER BY creation_date DESC
LIMIT 7;

-- 查找可能需要 PreAuth Key 的用户（最近创建的 Headscale 用户）
SELECT 
    h.username,
    h.display_name,
    h.created_at,
    CONCAT(h.username, '-headscale') as redis_key,
    TIMESTAMPDIFF(HOUR, h.created_at, NOW()) as hours_since_creation,
    CASE 
        WHEN TIMESTAMPDIFF(HOUR, h.created_at, NOW()) <= 24 THEN 'PreAuth Key 可能仍有效'
        ELSE 'PreAuth Key 可能已过期'
    END as preauth_key_status
FROM acs_headscale_users h
WHERE h.status = 1
ORDER BY h.created_at DESC
LIMIT 10;

-- 查找用户创建时间与 Headscale 用户创建时间的对比
SELECT 
    u.name as acs_username,
    u.gmt_create as acs_created,
    h.username as headscale_username,
    h.created_at as headscale_created,
    TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at) as creation_delay_seconds,
    CASE 
        WHEN TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at) <= 60 THEN '正常'
        WHEN TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at) <= 300 THEN '稍慢'
        ELSE '异常延迟'
    END as creation_performance
FROM acs_user u
INNER JOIN acs_headscale_users h ON u.id = h.acs_user_id
WHERE u.del = 0 AND h.status = 1
ORDER BY u.gmt_create DESC
LIMIT 15;

-- 模拟 Redis Key 查询（实际需要在 Redis 中执行）
-- 这里只是生成 Redis 命令，需要在 Redis CLI 中执行
SELECT 
    CONCAT('GET ', h.username, '-headscale') as redis_get_command,
    CONCAT('TTL ', h.username, '-headscale') as redis_ttl_command,
    CONCAT('EXISTS ', h.username, '-headscale') as redis_exists_command
FROM acs_headscale_users h
WHERE h.status = 1
ORDER BY h.created_at DESC
LIMIT 5;

-- 查找可能的数据不一致情况
-- 1. 有 ACS 用户但没有 Headscale 用户记录
SELECT 
    u.id,
    u.name as username,
    u.company,
    u.gmt_create,
    'Missing Headscale user record' as issue
FROM acs_user u
LEFT JOIN acs_headscale_users h ON u.id = h.acs_user_id AND h.status = 1
WHERE u.del = 0 AND u.status = 1 AND h.id IS NULL
ORDER BY u.gmt_create DESC
LIMIT 10;

-- 2. 有 Headscale 用户记录但 ACS 用户已删除
SELECT 
    h.id,
    h.username,
    h.acs_user_id,
    h.created_at,
    'ACS user deleted but Headscale record exists' as issue
FROM acs_headscale_users h
LEFT JOIN acs_user u ON h.acs_user_id = u.id
WHERE h.status = 1 AND (u.id IS NULL OR u.del > 0)
ORDER BY h.created_at DESC;

-- 生成清理过期 PreAuth Key 的 Redis 命令
SELECT 
    h.username,
    CONCAT('DEL ', h.username, '-headscale') as redis_delete_command,
    h.created_at,
    TIMESTAMPDIFF(HOUR, h.created_at, NOW()) as hours_old
FROM acs_headscale_users h
WHERE h.status = 1 
  AND TIMESTAMPDIFF(HOUR, h.created_at, NOW()) > 24
ORDER BY h.created_at ASC
LIMIT 10;

-- 统计信息汇总
SELECT 
    'Total ACS Users' as metric,
    COUNT(*) as count
FROM acs_user 
WHERE del = 0 AND status = 1

UNION ALL

SELECT 
    'Total Headscale Users' as metric,
    COUNT(*) as count
FROM acs_headscale_users 
WHERE status = 1

UNION ALL

SELECT 
    'Users Created Today' as metric,
    COUNT(*) as count
FROM acs_user 
WHERE del = 0 AND status = 1 AND DATE(gmt_create) = CURDATE()

UNION ALL

SELECT 
    'Headscale Users Created Today' as metric,
    COUNT(*) as count
FROM acs_headscale_users 
WHERE status = 1 AND DATE(created_at) = CURDATE()

UNION ALL

SELECT 
    'Potential Active PreAuth Keys' as metric,
    COUNT(*) as count
FROM acs_headscale_users 
WHERE status = 1 AND TIMESTAMPDIFF(HOUR, created_at, NOW()) <= 23;

-- 性能分析：用户创建的平均时间
SELECT 
    AVG(TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at)) as avg_creation_delay_seconds,
    MIN(TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at)) as min_creation_delay_seconds,
    MAX(TIMESTAMPDIFF(SECOND, u.gmt_create, h.created_at)) as max_creation_delay_seconds,
    COUNT(*) as total_users
FROM acs_user u
INNER JOIN acs_headscale_users h ON u.id = h.acs_user_id
WHERE u.del = 0 AND h.status = 1
  AND u.gmt_create >= DATE_SUB(NOW(), INTERVAL 7 DAY);
