package com.jetron.nb;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PreDestroy;
import java.util.List;

@Controller
@Configuration
@ComponentScan("com.jetron.nb")
@ServletComponentScan
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 600)
@EnableAspectJAutoProxy
@EnableScheduling
@ImportResource(locations = {"classpath:app.xml"})
public class IotPlatformApplication implements ApplicationContextAware {

	private final static Logger LOGGER = LoggerFactory.getLogger(IotPlatformApplication.class);

	private final static List<Runnable> preHaltTasks = Lists.newArrayList();

	private static ApplicationContext context;
	public static ApplicationContext context() {
		return context;
	}

	@RequestMapping("/ok.htm")
	@ResponseBody
	String ok(@RequestParam(defaultValue = "false") String down) {
//		if (Boolean.parseBoolean(down)) {
//			LOGGER.warn("prehalt initiated!");
//			for (final Runnable r : preHaltTasks) {
//				try {
//					r.run();
//				} catch (Exception e) {
//					LOGGER.error("prehalt task failed", e);
//				}
//			}
//		}
		return "ok";
	}

	@RequestMapping("/")
	String home() {
		return "index.html";
	}

	@RequestMapping("/about")
	String about() {
		return "about.html";
	}

	@PreDestroy
	void destroy() {
		LOGGER.warn("prehalt initiated!");
		for (final Runnable r : preHaltTasks) {
			try {
				r.run();
			} catch (Exception e) {
				LOGGER.error("prehalt task failed", e);
			}
		}
	}

	public static void addPreHaltTask(final Runnable runnable) {
		if (runnable != null) {
			preHaltTasks.add(runnable);
		}
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		if (IotPlatformApplication.context == null) {
			IotPlatformApplication.context = applicationContext;
		}
	}

	public static void main(String[] args) throws Exception {
		try {
			System.setProperty("spring.devtools.restart.enabled", "false");
			// 添加允许循环依赖的配置
			System.setProperty("spring.main.allow-circular-references", "true");
			SpringApplication.run(IotPlatformApplication.class, args);
		} catch (Throwable e) {
			LOGGER.error("iot-platform start error", e);
			throw e;
		}
	}

}
