package com.jetron.nb.dal.po;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AcsVpnConfig {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * vpn host
     */
    private String vpnHost;

    /**
     * vpn port
     */
    private Integer vpnPort;

    /**
     * VPN配置内容
     */
    private String vpnConfig;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
    /**
     * 是否二层
     */
    private String multilayer;
}