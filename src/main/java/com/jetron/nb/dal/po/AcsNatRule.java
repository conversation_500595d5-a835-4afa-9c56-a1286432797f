package com.jetron.nb.dal.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * nat规则参数
 */
@Data
@Accessors(chain = true)
@TableName(value = "acs_nat_rule")
public class AcsNatRule {

    @TableId(type = IdType.AUTO)
    private Integer id;
    // sn号
    private String sn;
    // 1关闭 0启用
    private Integer enable;
    // 唯一表示一条NAT规则
    private String displayName;
    // NAT规则类型 -- 动作
    private String target;
    // 协议
    private String protocol;
    // 源地址
    @TableField(value = "src_ip")
    private String srcIP;
    // 源起始端口号
    private Integer srcPortS;
    // 源终止端口号
    private Integer srcPortE;
    // 目标IP
    @TableField(value = "dest_ip")
    private String destIP;
    // 目的起始端口号
    private Integer destPortS;
    // 目的终止端口号
    private Integer destPortE;
    // 重写IP
    @TableField(value = "snat_ip")
    private String snatIP;
    // 重写起始端口号
    private Integer snatPortS;
    // 重写终止端口号
    private Integer snatPortE;
    // 网络接口
    private String netInterface;
    // 网络接口
    private Date gmtCreate;
    // 状态 0 正常 1 锁定
    private String status;

}
