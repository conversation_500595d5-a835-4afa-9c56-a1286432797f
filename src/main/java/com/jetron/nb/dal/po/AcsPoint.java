package com.jetron.nb.dal.po;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName(value = "acs_point",autoResultMap = true)
public class AcsPoint {
    @TableId(type = IdType.AUTO)
    private Integer id;
    // 公司名称
    @NotNull
    private String company;
    // 创建时间
    private Date gmtCreate;
    // 更新时间
    private Date gmtModify;
    // 备注
    private String remark;
    // 网关id
    @NotNull
    private Integer deviceId;
    // 设备名称
    @NotNull
    private String devName;
    // 点表json
    @NotNull
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject point;
}
