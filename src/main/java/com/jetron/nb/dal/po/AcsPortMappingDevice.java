package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName(value = "acs_port_mapping_device")
public class AcsPortMappingDevice {

    @TableId
    private String id;
    private String sn;
    // 0关闭 1 启用
    private Integer status;
    private String name;
    // 协议
    private String protocol;
    // 网关端口
    private Integer gatewayPort;
    // 设备IP
    private String devIp;
    // 设备端口
    private Integer devPort;
    // 创建时间
    Date gmtCreate;


}

