package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName(value = "acs_driver")
public class AcsDriver {
    @TableId(type = IdType.AUTO)
    private Integer id;
    // 公司名称
    @NotNull
    private String company;
    // 创建时间
    private Date gmtCreate;
    // 更新时间
    private Date gmtModify;
    // 备注
    private String remark;
    // 驱动名称
    @NotNull
    private String name;
    // 协议
    @NotNull
    private String protocol;
    // 文件地址
    private String path;

    @TableField(exist = false)
    private List<Integer> idList;
}
