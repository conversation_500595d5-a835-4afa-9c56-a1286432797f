package com.jetron.nb.dal.po;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 测试报告
 */
@Data
@Accessors(chain = true)
@TableName("acs_device_test_report")
public class AcsDeviceTestReport {
    /**
     * 主键
     */
    private String id;


    /**
     * 设备SN
     */
    private String sn;

    /**
     *
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject json;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern="yyyy-MM")
    @DateTimeFormat(pattern="yyyy-MM")
    private Date start;
    @TableField(exist = false)
    private Integer page;
    @TableField(exist = false)
    private Integer size;
    @TableField(exist = false)
    private String dateStart;
    @TableField(exist = false)
    private String dateEnd;
    @TableField(exist = false)
    private String alias;

}