package com.jetron.nb.dal.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class LogFilter {
    private String name;
    private String content;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date start;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date end;
    private List<Integer> userIdList;
    private Integer logType;
    private Integer offset;
    private Integer limit;
}
