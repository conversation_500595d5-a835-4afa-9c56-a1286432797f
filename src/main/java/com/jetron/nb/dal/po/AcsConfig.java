package com.jetron.nb.dal.po;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AcsConfig {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 配置名
     */
    private String name;

    /**
     * 配置型号
     */
    private String model;

    /**
     * 归宿用户id
     */
    private Integer belongTo;

    /**
     * 类型：0-管理员创建，1-自己上传
     */
    private Integer type;

    /**
     * 固件描述
     */
    private String describ;

    /**
     * 路径
     */
    private String path;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     *  公司名
     */
    private String company;
}