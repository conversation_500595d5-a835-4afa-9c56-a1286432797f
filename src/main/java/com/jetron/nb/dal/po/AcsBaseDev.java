package com.jetron.nb.dal.po;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * acs_user_device表中 base_dev字段。
 * 此类没有对应表
 */
@Data
@Accessors(chain = true)
public class AcsBaseDev {

    private Integer userDeviceId;
    private String baseName;
    private String baseDumIp;
    private String baseIp;
    private String baseDescrib;
    private String baseMfrsName;

    // 工具字段
    private boolean point;
    private Integer upStatus;


}
