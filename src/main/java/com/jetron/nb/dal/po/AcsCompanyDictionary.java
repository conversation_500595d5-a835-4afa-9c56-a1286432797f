package com.jetron.nb.dal.po;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
public class AcsCompanyDictionary {
    private Integer id;
    // 公司名称
    @NotNull
    private String company;
    // 创建时间
    private Date gmtCreate;
    // 更新时间
    private Date gmtModify;
    // 公司名称
    private String remark;
    // 字典类型
    @NotNull
    private String dictType;
    // 字典文本
    @NotNull
    private String dictLabel;
    // 字典值
    private String dictValue;
}
