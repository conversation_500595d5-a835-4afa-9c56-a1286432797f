package com.jetron.nb.dal.po;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AcsConfigUpgrade {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 配置id
     */
    private Integer deviceId;

    /**
     * 所属用户id
     */
    private Integer belongTo;

    /**
     * 升级状态：0-等待，1-下载失败，2-校验失败，3-升级成功
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
}