package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName(value = "acs_port_mapping")
public class AcsPortMapping {

    @TableId
    private String id;
    // 0关闭 1 启用
    @NotNull(message = "启用状态不能为空")
    private Integer status;
    @NotBlank(message = "名称不能为空")
    private String name;
    // 协议
    @NotBlank(message = "协议不能为空")
    private String protocol;
    // 网关端口
    @NotNull(message = "网关端口不能为空")
    private Integer gatewayPort;
    // 设备IP
    @NotBlank(message = "目的网络地址不能为空")
    private String devIp;
    // 设备端口
    @NotNull(message = "目的端口不能为空")
    private Integer devPort;
    // 创建时间
    Date gmtCreate;

    @TableField(exist = false)
    private List<String> idList;
    @TableField(exist = false)
    private List<String> nameList;
}

