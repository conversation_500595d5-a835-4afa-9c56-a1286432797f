package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 *  vpn 配置；静态IP等查看
 */
@Setter
@Getter
public class AcsVpn {
    Integer id;

    /**
     *  用户名称
     */
    String username;

    /**
     *  客户端静态IP
     */
    String staticIp;

    /**
     *  远程IP
     */
    String remoteIp;

    /**
     *  数据插入时间
     */
    Date insertDate;

    /**
     *  添加账户的人员
     */
    String insertUser;
}
