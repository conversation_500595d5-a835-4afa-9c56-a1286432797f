package com.jetron.nb.dal.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class AcsDeviceFlow extends BaseEntity{
    /**
     * 主键
     */
    private Integer id;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 上行流量  单位字节 B
     */
    private Long upFlow;

    /**
     * 下行流量 单位字节 B
     */
    private Long downFlow;

    /**
     * 日期
     */
    private String date;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    //  工具。表中无此列。 总流量。
    private Long sumFlow;
    //  工具。表中无此列。 总流量。
    private Integer userId;
}