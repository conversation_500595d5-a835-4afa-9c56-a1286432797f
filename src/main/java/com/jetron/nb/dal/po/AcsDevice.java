package com.jetron.nb.dal.po;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AcsDevice {
    /**
    * 主键
    */
    private Integer id;

    /**
    * 设备型号
    */
    private String model;

    /**
    * 设备SN
    */
    private String sn;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 更新时间
    */
    private Date gmtModify;

    /**
     *  分配的租户管理员ID
     */
    private Integer allocateUserId;
}