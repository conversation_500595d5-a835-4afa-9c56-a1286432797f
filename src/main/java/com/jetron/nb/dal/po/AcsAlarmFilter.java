package com.jetron.nb.dal.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AcsAlarmFilter extends BaseEntity{
    Integer id;

    /**
     *  序列号
     */
    String sn;

    /**
     *  公司名
     */
    String company;

    /**
     *  报警等级: 严重(4)、主要(3)、次要(2)、警告(1)
     */
    String alarmRank = "1";

    /**
     *  报警状态: 激活(1)，确认(2)，清除(3)
     */
    String alarmStatus = "1";

    /**
     *  参数项代码
     */
    String parameterCode;

    /**
     *  参数值
     */
    String parameterValue;
    /**
     *  参数项key，包括设备类型、设备SN等待
     */
    String parameterKey;

    /**
     *  参数项内容
     */
    String parameterContent;

    /**
     *  当前值
     */
    String currentValue;

    /**
     *  ip
     */
    String ip;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    Date insertTime;

    AcsUserDevice acsUserDevice;

    //   比较字段
    String snEq;
    //   比较字段 不等于
    String statusNe;

    private Integer offset;
    private Integer limit;
}
