package com.jetron.nb.dal.po;

import com.jetron.nb.common.util.ParamUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
@Setter
public class AcsCaptureData extends BaseEntity{
    int id;

    String sn;
    // 报警信息参数
    // 网关工作温度
    private String devTemp;
    // 信号强度
    private String rssi;
    // 信噪比
    private String signalQuality;
    // 系统内存
    private String ram;
    // cpu使用率
    private String cpuRation;

    Date insertTime;

    // 公司名称
    private String company;
    // 信号质量
    private String rsrq;


    public static String sigStrengthTransform(String sigStrength) {
        String rssi = "0";
        if (!StringUtils.isNotBlank(sigStrength)) {
            return rssi;
        }
        sigStrength = sigStrength.toUpperCase();
        switch (sigStrength) {
            case "UNKNOWN":
                rssi = "0";
                break;
            case "BAD":
                rssi = "1";
                break;
            case "POOR":
                rssi = "2";
                break;
            case "MODERATE":
                rssi = "3";
                break;
            case "GOOD":
                rssi = "4";
                break;
            case "GREAT":
                rssi = "5";
                break;
            default:
                // DTU 和 Jenet网关信号数据不同，JENET网关的信号强度例如“90dBm”
                rssi = ParamUtils.getNumeric(sigStrength);
        }
        return rssi;
    }
}
