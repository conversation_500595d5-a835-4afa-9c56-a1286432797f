package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class AcsDeviceHeart extends BaseEntity implements Serializable {
    int id;
    String sn;
    int status;
    Date insertTime;

    AcsUserDevice userDevice;

    public static String getHeartRedisKey(String sn) {
        return "heart_"+sn;
    }
}
