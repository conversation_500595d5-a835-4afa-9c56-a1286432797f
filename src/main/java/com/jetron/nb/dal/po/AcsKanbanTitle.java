package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *  大屏看板，标题
 */
@Getter
@Setter
@Accessors(chain = true)
public class AcsKanbanTitle extends BaseEntity{
    Integer id;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 访问者Id
     */
    private String visitorId;
    /**
     * 标题
     */
    private String title;

}
