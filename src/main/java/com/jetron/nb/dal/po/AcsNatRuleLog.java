package com.jetron.nb.dal.po;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * nat推送入参
 */
@Data
@Accessors(chain = true)
@TableName(value = "acs_nat_rule_log")
public class AcsNatRuleLog {

    @TableId
    private Integer id;

    // 设备sn号
    private String sn;

    // nat规则(对应一个sn号)
    @TableField(typeHandler =FastjsonTypeHandler.class)
    private JSONObject natRuleJson;

    // 推送类型  get、del、add
    private String pushType;

    /**
     * 创建时间
     */
    private Date gmtCreate;

}
