package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jetron.nb.common.util.ParamUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName(value = "acs_real_data")
public class AcsRealData{
    @TableId(type = IdType.AUTO)
    int id;

    String sn;
    // 报警信息参数
    // 网关工作温度
    private String devTemp;
    // 信号强度
    private String rssi;
    // 信号质量
    private String signalQuality;
    // 系统内存
    private String ram;
    // cpu使用率
    private String cpuRation;

    Date insertTime;
    Date updateTime;

    // 公司名称
    private String company;
    // 信号质量
    private String rsrq;

    // 查询参数
    @TableField(exist = false)
    private List<String> snList;



}
