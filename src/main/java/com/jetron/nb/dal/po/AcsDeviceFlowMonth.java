package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName("acs_device_flow_month")
public class AcsDeviceFlowMonth{
    /**
     * 主键
     */
    private String id;

    /**
     * 公司
     */
    private String company;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 总流量  单位字节 byte
     */
    private Long upFlow;

    /**
     * 日期
     */
    private String date;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern="yyyy-MM")
    @DateTimeFormat(pattern="yyyy-MM")
    private Date start;
    @TableField(exist = false)
    private Integer page;
    @TableField(exist = false)
    private Integer size;
    @TableField(exist = false)
    private String dateStart;
    @TableField(exist = false)
    private String dateEnd;
    @TableField(exist = false)
    private String alias;
    @TableField(exist = false)
    private String model;
    @TableField(exist = false)
    private String trueIp;


    /**
     * 过滤条件 查询sn 列表
     */
    @TableField(exist = false)
    private List<String> snList;
}