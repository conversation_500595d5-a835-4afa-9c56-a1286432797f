package com.jetron.nb.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("acs_table_display")
public class AcsTableDisplay {
    @TableId(type = IdType.AUTO)
    private Integer id;

    // 创建时间
    private Date gmtCreate;
    // 更新时间
    private Date gmtModify;
    // 表
    private String tableName;
    // 列
    @NotNull
    private String columnName;
    // 0不显示，1显示
    @NotNull
    private Integer flag;
    // 用户id
    private Integer userId;
}
