package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class UserFilter {
    private String name;
    private List<String> nameList;
    private String company;
    private Integer role;
    private Integer parentId;
    private Integer status;
    private Integer offset;
    private Integer limit;
    private List<Integer> userIds;
    private Integer belongTo;
    private boolean nameFuzzy;
    private Date  startLastLoginTime;
    // true表示查询 del不为0的数据
    private boolean  all;
}
