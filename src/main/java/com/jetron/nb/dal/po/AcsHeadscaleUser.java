package com.jetron.nb.dal.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * Headscale 用户记录实体类
 * 对应数据表：acs_headscale_users
 */
@Data
@Accessors(chain = true)
public class AcsHeadscaleUser implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * Headscale 用户名（对应 AcsUser.name）
     */
    private String username;

    /**
     * Headscale 显示名称（对应 AcsUser.company 转换后的拼音）
     */
    private String displayName;

    /**
     * Headscale 用户ID（由 Headscale 服务器返回）
     */
    private String headscaleUserId;

    /**
     * 对应的 ACS 用户ID
     */
    private Integer acsUserId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人ID（对应 currentUser.getId()）
     */
    private Integer createBy;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 状态：0-已删除，1-正常
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;
}
