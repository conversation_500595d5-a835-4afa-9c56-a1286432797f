package com.jetron.nb.dal.po;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jetron.nb.common.handler.MySqlJsonHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class AcsUserDevice {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 设备名
     */
    private String name;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 设备密码
     */
    private String devicePasswd;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 最后心跳时间
     */
    private Date lastHbTime;

    /**
     * 在线时长（网关连接时长）
     */
    private Integer onlineTime;

    /**
     * 所属客户管理员ID
     */
    private Integer belongTo;

    /**
     * 子网ip
     */
    private String subIp;

    /**
     * 虚拟ip
     */
    private String dumIp;

    /**
     * 真实ip
     */
    private String trueIp;

    /**
     * 固件版本
     */
    private String verNo;

    /**
     * 硬件版本
     */
    private String hardVerNo;

    /**
     * 移动号码
     */
    private String signalNum;

    /**
     * 5G信号强弱
     */
    private String signal5g;

    /**
     * GPS
     */
    private String gps;

    /**
     * 描述信息
     */
    private String describ;

    /**
     * 下挂设备数量
     */
    private Integer baseDevNum;

    /**
     * 下挂设备信息
     */
    private String baseDev;

    /**
     * 是否升级中
     */
    private Integer upgradeStatus;

    /**
     * 日志路径
     */
    private String path;

    /**
     * 设备host前缀
     */
    private String hostPrefix;

    /**
     * 是否删除
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 小区号
     */
    private String cellId;

    /**
     * 移动设备国际身份码
     */
    private String imei;

    /**
     *  国际移动用户识别码
     */
    private String imsi;

    /**
     * 网络类型（2G、3G、4G、5G、Wireless、Wired）
     */
    private String networkType;
    /**
     * 信号强度
     */
    private String rssi;
    /**
     * 信噪比(信号质量)
     */
    private String sinr;
    /**
     * 状态（0正常，1报警, 2 待激活）
     */
    private Integer status;
    /**
     * 自定义设备名
     */
    private String alias;
    /**
     * 位置
     */
    private String position;
    /**
     * 套餐流量
     */
    private Double flow;
    /**
     * 物理小区号
     */
    private String villageNum;
    /**
     * 物理小区号
     */
    private String bindIp;
    /**
     * iccid
     */
    private String iccid;
    // login_date
    private Date loginDate;
    // 信号质量
    private String signalQuality;
    // 网关启动时长，秒数（区别online）
    private Integer uptime;
    // lanip
//    private String lanip;
    // 1表示启用锁小区，0表示未启用
    private String lockCellEnable;
    // 1表示启用锁小区，0表示未启用
    private String lock4GCellEnable;

    // 外键
    private AcsUser user;
    private AcsPoint point;

    // 工具字段
    private Integer offLine;

    // 网关LAN口ip
    private String lanIp;
    // 精度
    private Double lon;
    // 维度
    private Double lat;

//    // 当前路由出接口 WAN、4G/5G、Wifi
    private String iface;

    private Boolean upgradeDevice;
}