package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class UserDeviceFilter {
    private Integer belongTo;
    private String model;
    private String sn;
    private String verNo;
    private String alias;
    private String position;
    private String trueIp;
    private String imei;
    private Integer online;
    private Integer userId;
    private List<String> snList;
    private List<Integer> deviceIds;
    private List<Integer> belongToList;
    private Integer upgradeStatus;
    private Integer offset;
    private Integer limit;
    private String noEqSn;
    private String snEq;
    private int havePath;
    private String iccid;
    private String orderByColumn;
    private String company;
    private String deviceStatus;
    private String snLike;
}
