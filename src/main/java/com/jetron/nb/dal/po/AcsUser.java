package com.jetron.nb.dal.po;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class AcsUser {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 密码，md5值
     */
    private String passwd;

    /**
     * 设备登录密码
     */
    private String devicePasswd;

    /**
     * 0-系统管理员，1-客户管理员，2-客户操作员
     */
    private Integer role;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 描述
     */
    private String describ;

    /**
     * 上次登录IP
     */
    private String lastLoginIp;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 上次登出时间
     */
    private Date lastLogoutTime;

    /**
     * 本次登录IP
     */
    private String currLoginIp;

    /**
     * 本次登录时间
     */
    private Date currLoginTime;

    /**
     * 账号状态0-冻结，1-正常
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Integer parentId;
    /**
     * 0-正常，大于0-被删除
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
}