package com.jetron.nb.dal.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 *  设备各项参数设置，包括预警、报警等
 */
@Getter
@Setter
public class AcsParameterConfig extends BaseEntity {
    int id;

    /**
     *  公司名
     */
    String company;

    /**
     *  参数项key，包括设备类型、设备SN等待
     */
    String parameterKey;

    /**
     *  参数项内容
     */
    String parameterContent;

    /**
     *  参数项代码
     */
    String parameterCode;

    /**
     *  参数标准值；或者 参数最小值
     */
    double parameterValue;

    /**
     *  参数最大值
     */
    double parameterValueMax;

    /**
     *  参数运算符
     */
    int parameterOperator;

    Date insertTime;
}
