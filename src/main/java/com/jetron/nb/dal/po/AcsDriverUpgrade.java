package com.jetron.nb.dal.po;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName(value = "acs_driver_upgrade")
public class AcsDriverUpgrade {
    @TableId(type = IdType.AUTO)
    private Integer id;
    // 公司名称
    @NotNull
    private String company;
    // 创建时间
    private Date gmtCreate;
    // 更新时间
    private Date gmtModify;

    // 网关id
    private Integer deviceId;
    // 设备名称
    private String devName;
    // 网关id
    private String driverId;
    private String path;
    // 状态
    private Integer status;


    // 更新字段
    @TableField(exist = false)
    private List<Integer> idList;
    @TableField(exist = false)
    private Integer statusEq;
    @TableField(exist = false)
    private Integer statusLt;
}
