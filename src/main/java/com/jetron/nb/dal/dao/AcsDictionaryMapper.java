package com.jetron.nb.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jetron.nb.dal.po.AcsDictionary;

import java.util.List;

public interface AcsDictionaryMapper {
    int insert(AcsDictionary acsDictionary);
    List<AcsDictionary> findByKey(String key);
    int deleteByPrimaryKey(Integer id);
    List<AcsDictionary> findAll();
    List<AcsDictionary> findList(AcsDictionary acsDictionary);

    List<AcsDictionary> findPage(AcsDictionary acsDictionary);
    int count(AcsDictionary acsDictionary);
}
