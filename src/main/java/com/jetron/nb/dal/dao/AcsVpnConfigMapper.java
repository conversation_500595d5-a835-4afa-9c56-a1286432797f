package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsVpnConfig;import org.apache.ibatis.annotations.Param;import java.util.List;

public interface AcsVpnConfigMapper {
    int insert(AcsVpnConfig record);

    int insertSelective(AcsVpnConfig record);

    AcsVpnConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsVpnConfig record);

    int updateByPrimaryKey(AcsVpnConfig record);

    AcsVpnConfig getByUserId(@Param("userId") Integer userId);

    List<AcsVpnConfig> getByUserIds(@Param("userIds") List<Integer> userIds);

    int deleteByUserId(@Param("userId") Integer userId);

    int undoDeleteByUserId(@Param("userId") Integer userId);
}