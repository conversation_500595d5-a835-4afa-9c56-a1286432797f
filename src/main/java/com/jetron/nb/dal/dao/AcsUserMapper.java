package com.jetron.nb.dal.dao;

import com.jetron.nb.common.vo.UserStatisticsVo;
import com.jetron.nb.dal.po.AcsUser;import com.jetron.nb.dal.po.UserFilter;import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AcsUserMapper {
    int insert(AcsUser record);

    int insertSelective(AcsUser record);

    AcsUser selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsUser record);

    int updateByPrimaryKey(AcsUser record);

    List<AcsUser> getByFilter(UserFilter filter);


    List<AcsUser> getAll(UserFilter filter);

    int countByFilter(UserFilter filter);

    int freeze(@Param("name") String name);

    List<String> getAllUserNames();

    int deleteChildren(Integer userId);

    int undoDeleteChildren(Integer userId);

    int deleteById(Integer userId);

    int undoDel(Integer userId);

    List<AcsUser> getDeletedUser(@Param("begin") Date begin,
                                 @Param("end") Date end);

    AcsUser getByUsername(@Param("username") String username);

    List<String> company();

    List<UserStatisticsVo> userStatistics(UserFilter filter);

    int countUserStatistics(UserFilter filter);
}