package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsHeadscaleUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Headscale 用户记录 Mapper 接口
 */
@Mapper
public interface AcsHeadscaleUserMapper {

    /**
     * 插入记录
     */
    int insert(AcsHeadscaleUser record);

    /**
     * 选择性插入记录
     */
    int insertSelective(AcsHeadscaleUser record);

    /**
     * 根据主键查询
     */
    AcsHeadscaleUser selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     */
    int updateByPrimaryKeySelective(AcsHeadscaleUser record);

    /**
     * 根据主键更新记录
     */
    int updateByPrimaryKey(AcsHeadscaleUser record);

    /**
     * 根据主键删除记录（逻辑删除）
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 根据 ACS 用户ID查询
     */
    AcsHeadscaleUser selectByAcsUserId(@Param("acsUserId") Integer acsUserId);

    /**
     * 根据用户名查询
     */
    AcsHeadscaleUser selectByUsername(@Param("username") String username);

    /**
     * 根据 Headscale 用户ID查询
     */
    AcsHeadscaleUser selectByHeadscaleUserId(@Param("headscaleUserId") String headscaleUserId);

    /**
     * 根据创建人查询列表
     */
    List<AcsHeadscaleUser> selectByCreateBy(@Param("createBy") Integer createBy);

    /**
     * 查询所有有效记录
     */
    List<AcsHeadscaleUser> selectAllActive();

    /**
     * 根据条件查询记录数
     */
    int countByCondition(AcsHeadscaleUser condition);

    /**
     * 根据条件查询记录列表
     */
    List<AcsHeadscaleUser> selectByCondition(AcsHeadscaleUser condition);
}
