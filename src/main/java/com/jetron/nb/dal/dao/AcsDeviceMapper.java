package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsDevice;
import com.jetron.nb.dal.po.DeviceFilter;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface AcsDeviceMapper {

    List<AcsDevice> getBySnList(@Param("snList") List<String> sn);

    int bachInsert(@Param("deviceList") List<AcsDevice> devices);

    int deleteBySn(@Param("sn") String sn);

    List<AcsDevice> getByFilter(DeviceFilter filter);

    int countByFilter(DeviceFilter filter);

    int deleteByPrimaryKey(Integer id);

    int insert(AcsDevice record);

    int insertSelective(AcsDevice record);

    AcsDevice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsDevice record);

    int updateByPrimaryKey(AcsDevice record);

    List<AcsDevice> selectByPrimaryKeys(@Param("idList") List<Integer> idList);

    List<AcsDevice> getUnregisteredDeviceInUse(@Param("idList") List<Integer> idList);

    int updateByPrimaryKeys(@Param("idList") List<Integer> idList, @Param("allocateUserId")  Integer allocateUserId);

    List<AcsDevice> findDevicesWithAllocatedUsers();

    List<AcsDevice> findDevicesByUserIds(@Param("userIds") List<Integer> userIds);
}