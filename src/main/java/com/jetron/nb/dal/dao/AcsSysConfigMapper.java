package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsSysConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AcsSysConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsSysConfig record);

    int insertSelective(AcsSysConfig record);

    AcsSysConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsSysConfig record);

    int updateByPrimaryKey(AcsSysConfig record);

    AcsSysConfig getByName(@Param("name") String name);

    List<AcsSysConfig> getAll();
}