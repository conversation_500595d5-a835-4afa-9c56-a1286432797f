package com.jetron.nb.dal.dao;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.dal.po.AcsUserDevice;import com.jetron.nb.dal.po.UserDeviceFilter;import org.apache.ibatis.annotations.Param;import java.util.Date;import java.util.List;

public interface AcsUserDeviceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsUserDevice record);

    int insertSelective(AcsUserDevice record);

    AcsUserDevice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsUserDevice record);

    int updateByPrimaryKey(AcsUserDevice record);

    List<AcsUserDevice> getByFilter(UserDeviceFilter filter);


    List<AcsUserDevice> findListWithUser(UserDeviceFilter filter);

    List<AcsUserDevice> findListWithPoint(UserDeviceFilter filter);

    int countByFilter(UserDeviceFilter filter);

    List<AcsUserDevice> getByDeviceIds(@Param("deviceIds") List<Integer> deviceIds);

    AcsUserDevice getBySn(@Param("sn") String sn);

    List<AcsUserDevice> getByUserId(@Param("userId") Integer userId);

    AcsUserDevice getByHostPrefix(@Param("prefix") String prefix);

    String getBaseDev(@Param("id") Integer id);

    int setUpgradeStatus(@Param("deviceIds") List<Integer> deviceIds,
                         @Param("status") Integer status);

    List<String> getSnList(@Param("offset") Integer offset,
                           @Param("limit") Integer limit);

    List<String> getVernoList();

    int deleteByUserId(@Param("userId") Integer userId);

    int undoDeleteByUserId(@Param("userId") Integer userId);

    List<AcsUserDevice> getIncreasedDevice(@Param("begin") Date begin,
                                           @Param("end") Date end);

    List<AcsUserDevice> getDeletedDevice(@Param("begin") Date begin,
                                         @Param("end") Date end);

    int releaseUpgradeStatus(@Param("deviceIds") List<Integer> deviceIds);

    List<AcsUserDevice> selectAllNotDelete();

    List<JSONObject> selectModelGroup(UserDeviceFilter filter);
}