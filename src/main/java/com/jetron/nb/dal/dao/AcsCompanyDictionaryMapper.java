package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsCompanyDictionary;
import com.jetron.nb.dal.po.AcsDictionary;

import java.util.List;

public interface AcsCompanyDictionaryMapper {

    int insertList(List<AcsCompanyDictionary> dictList);

    int delete(List<Integer> list);

    int update(AcsCompanyDictionary acsDictionary);

    List<AcsCompanyDictionary> findList(AcsCompanyDictionary param);

    int findCount(AcsCompanyDictionary param);
}
