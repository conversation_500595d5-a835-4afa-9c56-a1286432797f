package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsDeviceHeart;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;

import java.util.Date;
import java.util.List;

public interface AcsDeviceHeartMapper {
    int insert(AcsDeviceHeart deviceHeart);

    int insertList(List<AcsDeviceHeart> list);

    List<AcsDeviceHeart> findList(AcsDeviceHeart deviceHeart);

    List<AcsDeviceHeart> findListWithUserDevice(AcsDeviceHeart deviceHeart);

    int findCount(AcsDeviceHeart deviceHeart);

    AcsDeviceHeart findLatest(String sn, Date start);

    List<AcsDeviceHeart> findAllLatest(Date start);
}
