package com.jetron.nb.dal.dao.mqtt;

import com.jetron.nb.dal.po.AcsDeviceHeart;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;

import java.util.Date;
import java.util.List;

public interface AcsMqttOnlineMapper {
    int insert(AcsMqttOnline acsMqttOnline);

    List<AcsMqttOnline> findList(AcsMqttOnline acsMqttOnline);

    List<AcsMqttOnline> findPage(AcsMqttOnline acsMqttOnline);

    AcsMqttOnline findLatest(String sn, Date start);


    int findCount(AcsMqttOnline acsMqttOnline);

    void delete(AcsMqttOnline acsMqttOnline);
}
