package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsParameterConfig;

import java.util.List;

public interface AcsParameterConfigMapper {
    List<AcsParameterConfig> findAll();

    AcsParameterConfig findById(Integer id);

    List<AcsParameterConfig> findByCompany(String company);

    List<AcsParameterConfig> findList(AcsParameterConfig acsParameterConfig);

    List<AcsParameterConfig> findGroupByCompanyModel(AcsParameterConfig acsParameterConfig);


    int findCount(AcsParameterConfig acsParameterConfig);

    int deleteByPrimaryKey(int id);

    int insert(AcsParameterConfig acsParameterConfig);

    int updateByPrimaryKey(AcsParameterConfig acsParameterConfig);

    AcsParameterConfig findByParameterKey(String key);
}
