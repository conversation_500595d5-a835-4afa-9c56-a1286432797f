package com.jetron.nb.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jetron.nb.dal.po.AcsCompanyDictionary;
import com.jetron.nb.dal.po.AcsTableDisplay;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AcsTableDisplayMapper extends BaseMapper<AcsTableDisplay> {

    @Insert("<script>" +
            "insert into acs_table_display (gmt_create,table_name,column_name,flag, user_id) values " +
            "<foreach collection=\"list\" separator=\",\" item=\"data\"> " +
            "  (#{data.gmtCreate,jdbcType=TIMESTAMP},#{data.tableName,jdbcType=VARCHAR},\n" +
            "            #{data.columnName,jdbcType=VARCHAR},#{data.flag,jdbcType=INTEGER}, #{data.userId,jdbcType=INTEGER})" +
            "</foreach>" +
            "</script>")
    int insertList(@Param("list") List<AcsTableDisplay> dictList);

  /*  int insertList(List<AcsTableDisplay> dictList);

    int delete(List<Integer> list);*/

    /*List<AcsTableDisplay> findList(AcsTableDisplay param);

    int findCount(AcsCompanyDictionary param);*/
}
