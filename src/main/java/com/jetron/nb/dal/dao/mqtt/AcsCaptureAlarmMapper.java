package com.jetron.nb.dal.dao.mqtt;

import com.jetron.nb.dal.po.AcsAlarmFilter;
import com.jetron.nb.dal.po.AcsCaptureAlarm;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AcsCaptureAlarmMapper {
    int insert(AcsCaptureAlarm acsCaptureAlarm);

    int insertList(List<AcsCaptureAlarm> acsCaptureAlarm);

    List<AcsCaptureAlarm> findList(AcsAlarmFilter acsAlarmFilter);

    int findCount(AcsAlarmFilter acsAlarmFilter);

    List<AcsCaptureAlarm> findWithDeviceList(AcsAlarmFilter acsAlarmFilter);

    int findWithDeviceCount(AcsAlarmFilter acsAlarmFilter);

    List<AcsCaptureAlarm> findBySnAndDate(String sn, Date beginDate, Date endDate);

    List<AcsCaptureAlarm>  findCurrentActiveAlarmBySn(AcsAlarmFilter acsCaptureAlarm);

    List<AcsCaptureAlarm>  findCurrentActiveAlarm(AcsAlarmFilter acsCaptureAlarm);

    List<AcsCaptureAlarm> findByDate(Date beginDate, Date endDate);

    List<AcsCaptureAlarm> findByDateAndCompany(Date beginDate, Date endDate, String company);

    List<AcsCaptureAlarm> findActiveAlarm(AcsCaptureAlarm alarm);

    int updateAlarmStatus(AcsCaptureAlarm alarm);

    int updateStatusList(@Param("status")String status, @Param("list") List<Integer> list);

    int delList(List<Integer> list);

    int delByDate(Date startDate ,Date endDate);

    int clearAlarm(@Param("sn")String sn);
}
