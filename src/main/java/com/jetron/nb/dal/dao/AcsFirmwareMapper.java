package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsFirmware;import com.jetron.nb.dal.po.FirmwareFilter;import java.util.List;

public interface AcsFirmwareMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsFirmware record);

    int insertSelective(AcsFirmware record);

    AcsFirmware selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsFirmware record);

    int updateByPrimaryKey(AcsFirmware record);

    List<AcsFirmware> getByFilter(FirmwareFilter filter);

    int countByFilter(FirmwareFilter filter);
}