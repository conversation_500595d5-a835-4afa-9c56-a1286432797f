package com.jetron.nb.dal.dao.mqtt;

import com.jetron.nb.dal.po.mqtt.MqttUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MqttUserMapper {
    int insert(MqttUser mqttUser);
    MqttUser findByUsername(@Param("username") String username);
    int updateById(MqttUser mqttUser);

    int delByUsername(List<String> list);

    int updateUsernameById(MqttUser mqttUser);
}
