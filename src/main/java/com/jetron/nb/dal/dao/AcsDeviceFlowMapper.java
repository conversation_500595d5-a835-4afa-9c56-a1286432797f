package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsDeviceFlow;
import com.jetron.nb.dal.po.AcsDeviceFlowMonth;
import com.jetron.nb.dal.po.UserFlow;import org.apache.ibatis.annotations.Param;import java.util.Date;import java.util.List;
import java.util.Map;

public interface AcsDeviceFlowMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsDeviceFlow record);

    int insertSelective(AcsDeviceFlow record);

    AcsDeviceFlow selectByPrimaryKey(Integer id);

    List<AcsDeviceFlow> selectWeeklyFlow(@Param("snList")List<String> snList, @Param("date") String monday);

    int updateByPrimaryKeySelective(AcsDeviceFlow record);

    int updateByPrimaryKey(AcsDeviceFlow record);

    AcsDeviceFlow getTodayRecord(@Param("sn") String sn,
                                 @Param("date") String date);

    List<AcsDeviceFlow> getDeviceFlow(AcsDeviceFlow flow);

    List<UserFlow> getUserFlow(@Param("userId") Integer userId,
                               @Param("start") Date start,
                               @Param("end") Date end);

    List<AcsDeviceFlow> getUserFlowWithDate(@Param("userId") Integer userId,@Param("start") Date start,@Param("end") Date end);

    List<AcsDeviceFlow> findLastFlow(AcsDeviceFlow flow);

    int insertMonthList(List<AcsDeviceFlowMonth> list);
}