package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsDeviceFlow;
import com.jetron.nb.dal.po.UserFlow;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AcsDeviceFlowHourMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsDeviceFlow record);

    int insertSelective(AcsDeviceFlow record);

    AcsDeviceFlow selectByPrimaryKey(Integer id);

    List<AcsDeviceFlow> selectWeeklyFlow(@Param("snList")List<String> snList, @Param("date") String monday);

    int updateByPrimaryKeySelective(AcsDeviceFlow record);

    int updateByPrimaryKey(AcsDeviceFlow record);

    AcsDeviceFlow getRecord(@Param("sn") String sn,
                                 @Param("date") String date);

    List<AcsDeviceFlow> getDeviceFlow(@Param("sn") String sn,
                                      @Param("start") Date start,
                                      @Param("end") Date end);

    List<UserFlow> getUserFlow(@Param("userId") Integer userId,
                               @Param("start") Date start,
                               @Param("end") Date end);

    List<AcsDeviceFlow> getUserFlowWithDate(@Param("userId") Integer userId,@Param("start") Date start,@Param("end") Date end);

    List<AcsDeviceFlow> findLastFlow(AcsDeviceFlow flow);
}