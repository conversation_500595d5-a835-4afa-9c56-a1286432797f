package com.jetron.nb.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jetron.nb.dal.po.AcsDeviceFlowMonth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AcsDeviceFlowMonthMapper extends BaseMapper<AcsDeviceFlowMonth> {

    IPage<AcsDeviceFlowMonth> findList(IPage page,@Param(value="param") AcsDeviceFlowMonth param);
}
