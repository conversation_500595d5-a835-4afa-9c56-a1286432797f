package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsConfigUpgrade;import com.jetron.nb.dal.po.UpgradeFilter;import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AcsConfigUpgradeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsConfigUpgrade record);

    int insertSelective(AcsConfigUpgrade record);

    AcsConfigUpgrade selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsConfigUpgrade record);

    int updateByPrimaryKey(AcsConfigUpgrade record);

    List<AcsConfigUpgrade> getByFilter(UpgradeFilter filter);

    int count(UpgradeFilter filter);

    int batchInsert(@Param("upgradeList") List<AcsConfigUpgrade> records);

    int deleteConfigId(@Param("configId") Integer configId);

    AcsConfigUpgrade getLatestUpgrade(@Param("deviceId") Integer deviceId);

    List<AcsConfigUpgrade> getTimeout(@Param("deadline") Date deadline);

    int timeout(@Param("deviceIds") List<Integer> deviceIds);
}