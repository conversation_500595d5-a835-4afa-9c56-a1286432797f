package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsLog;import com.jetron.nb.dal.po.LogFilter;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AcsLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsLog record);

    int insertSelective(AcsLog record);

    AcsLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsLog record);

    int updateByPrimaryKey(AcsLog record);

    List<AcsLog> getByFilter(LogFilter filter);

    int countByFilter(LogFilter filter);

    int appendLog(@Param("logId") Integer logId,
                  @Param("logVersion") Integer logVersion,
                  @Param("logContent") String logContent);

    int delList(List<Integer> list);

    int delByDate(Date startDate ,Date endDate);
}