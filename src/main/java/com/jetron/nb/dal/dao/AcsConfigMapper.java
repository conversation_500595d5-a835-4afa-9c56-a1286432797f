package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsConfig;import com.jetron.nb.dal.po.ConfigFilter;import java.util.List;

public interface AcsConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsConfig record);

    int insertSelective(AcsConfig record);

    AcsConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsConfig record);

    int updateByPrimaryKey(AcsConfig record);

    List<AcsConfig> getByFilter(ConfigFilter filter);

    int countByFilter(ConfigFilter filter);

    List<AcsConfig> selectByIdList(List<Integer> ids);
}