package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsFirmwareUpgrade;import com.jetron.nb.dal.po.UpgradeFilter;import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AcsFirmwareUpgradeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AcsFirmwareUpgrade record);

    int insertSelective(AcsFirmwareUpgrade record);

    AcsFirmwareUpgrade selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AcsFirmwareUpgrade record);

    int updateByPrimaryKey(AcsFirmwareUpgrade record);

    List<AcsFirmwareUpgrade> getByFilter(UpgradeFilter filter);

    int count(UpgradeFilter filter);

    int batchInsert(@Param("upgradeList") List<AcsFirmwareUpgrade> records);

    int deleteByFirmwareId(@Param("firmwareId") Integer firmwareId);

    AcsFirmwareUpgrade getLatestUpgrade(@Param("deviceId") Integer deviceId);

    List<AcsFirmwareUpgrade> getTimeout(@Param("deadline") Date deadline);

    int timeout(@Param("deviceIds") List<Integer> deviceIds);
}