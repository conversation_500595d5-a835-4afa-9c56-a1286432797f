package com.jetron.nb.dal.dao;

import com.jetron.nb.dal.po.AcsCaptureData;

import java.util.Date;
import java.util.List;

public interface AcsCaptureDataMapper {
    int insert(AcsCaptureData acsCaptureData);

    int insertList(List<AcsCaptureData> acsCaptureData);

    int findCount(AcsCaptureData acsCaptureData);

    int delByDate(Date start, Date end);

    List<AcsCaptureData> findList(AcsCaptureData acsCaptureData);

    List<AcsCaptureData> findLatestData(AcsCaptureData acsCaptureData);

    List<AcsCaptureData> findBySnAndDate(AcsCaptureData acsCaptureData);

    List<AcsCaptureData> findChartData(String company,Date begin, Date end);
}
