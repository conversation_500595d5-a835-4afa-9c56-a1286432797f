package com.jetron.nb.dal.vo.headscale;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Headscale Namespace DTO
 */
@Data
public class HeadscaleNamespace {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "created_at")
    private String createdAt;

    @JSONField(name = "updated_at")
    private String updatedAt;
}
