package com.jetron.nb.dal.vo.headscale;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * Headscale PreAuth Key DTO
 */
@Data
public class HeadscalePreAuthKey {

    @JSONField(name = "id")
    private String id;

    @JSO<PERSON>ield(name = "key")
    private String key;

    @JSONField(name = "user")
    private HeadscaleUser user;

    @JSONField(name = "reusable")
    private Boolean reusable;

    @JSONField(name = "ephemeral")
    private Boolean ephemeral;

    @JSONField(name = "used")
    private Boolean used;

    @JSONField(name = "expiration")
    private String expiration;

    @JSONField(name = "created_at")
    private String createdAt;

    @JSO<PERSON>ield(name = "updated_at")
    private String updatedAt;

    @JSONField(name = "acl_tags")
    private List<String> aclTags;
}
