package com.jetron.nb.dal.vo.headscale;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Headscale ACL DTO
 */
@Data
public class HeadscaleACL {

    @J<PERSON>NField(name = "groups")
    private Map<String, List<String>> groups;

    @J<PERSON>NField(name = "hosts")
    private Map<String, String> hosts;

    @J<PERSON><PERSON>ield(name = "tagOwners")
    private Map<String, List<String>> tagOwners;

    @JSONField(name = "acls")
    private List<ACLRule> acls;

    @J<PERSON>NField(name = "tests")
    private List<ACLTest> tests;

    @Data
    public static class ACLRule {
        @J<PERSON>NField(name = "action")
        private String action;

        @J<PERSON>NField(name = "src")
        private List<String> src;

        @JSONField(name = "dst")
        private List<String> dst;

        @JSONField(name = "proto")
        private String proto;
    }

    @Data
    public static class ACLTest {
        @J<PERSON>NField(name = "src")
        private String src;

        @J<PERSON><PERSON>ield(name = "accept")
        private List<String> accept;

        @J<PERSON><PERSON>ield(name = "deny")
        private List<String> deny;
    }
}
