package com.jetron.nb.dal.vo;

import com.alibaba.fastjson.JSONArray;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Bremote下发数据
 */
@Data
public class UserDeviceToBremoteVo implements Serializable {
    private static final long serialVersionUID = 905646556255547123L;
    /**
     * 自定义设备名
     */
    private String alias;

    /**
     * 虚拟ip
     */
    private String dumIp;
    /**
     * 硬件版本
     */
    private String hardVerNo;
    /**
     * 位置
     */
    private String location;
    // login_date
    private Date loginDate;
    /**
     * 设备名
     */
    private String name;
    /**
     * 设备型号
     */
    private String no;
    /**
     * 5G信号强弱
     */
    private String signal5g;
    /**
     * 移动号码
     */
    private String signalNum;
    /**
     * 设备SN
     */
    private String sn;
    /**
     * 真实ip
     */
    private String trueIp;
    /**
     * 下挂设备数量
     */
    private Integer baseDevNum;
    /**
     * 下挂设备信息
     */
    private JSONArray baseDev;
    /**
     * 固件版本
     */
    private String verNo;
    // 网关LAN口ip
    private String lanIp;

    public UserDeviceToBremoteVo(AcsUserDevice userDevice) {
        this.setNo(userDevice.getModel());
        this.setAlias(userDevice.getAlias());
        this.setDumIp(userDevice.getDumIp());
        this.setSignal5g(userDevice.getSignal5g());
        this.setTrueIp(userDevice.getTrueIp());
        this.setSignalNum(userDevice.getSignalNum());
        this.setHardVerNo(userDevice.getHardVerNo());
        this.setLocation(userDevice.getPosition());
        this.setLoginDate(userDevice.getLoginDate());
        this.setName(userDevice.getName());
        this.setSn(userDevice.getSn());
        this.setBaseDev(JSONArray.parseArray(userDevice.getBaseDev()));
        this.setBaseDevNum(userDevice.getBaseDevNum());
        this.setVerNo(userDevice.getVerNo());
        this.setLanIp(userDevice.getLanIp());
    }
}