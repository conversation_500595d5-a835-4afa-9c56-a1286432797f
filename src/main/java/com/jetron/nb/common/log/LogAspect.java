package com.jetron.nb.common.log;

import com.jetron.nb.biz.service.AcsLogService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.biz.service.ThreadPoolService;
import com.jetron.nb.biz.task.AcsLogRunner;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsLog;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.web.common.util.ThreadContextUtils;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Component
@Aspect
@Slf4j
public class LogAspect {
    private static final ExpressionParser parser = new SpelExpressionParser();

    @Autowired
    private AcsUserService acsUserService;

    @Pointcut("@annotation(com.jetron.nb.common.log.OperateLog)")
    public void logPointCut() {

    }

    @AfterReturning(pointcut = "logPointCut()", returning = "resultValue")
    public Object around(JoinPoint point, Object resultValue) throws Throwable {
        long beginTime = System.currentTimeMillis();
        Object result = resultValue;
        long time = System.currentTimeMillis() - beginTime;
        try {
            saveLog(point, result, time);
        } catch (Exception e) {
            log.error("{}", e);
        }
        return result;
    }

    private void saveLog(JoinPoint joinPoint, Object retVal, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperateLog operateLog = method.getAnnotation(OperateLog.class);

        String expression = operateLog.remark();
        if (StringUtils.isBlank(expression)) {
            return;
        }

        Object[] args = joinPoint.getArgs();
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < args.length; i++) {
            context.setVariable(String.format("p%s", (i + 1)), args[i]);
        }

        String remark = (String) parser.parseExpression(expression).getValue(context);
        if (retVal instanceof Result) {
            Result result = (Result) retVal;
            remark = String.format("操作: (%s, %s-%s)\n\n", remark, result.getCode(), result.getMessage());
        }

        String detailLog = MdcUtils.getDetailLog();
        if (StringUtils.isNoneBlank(detailLog)) {
            remark += "," + MdcUtils.getDetailLog();
        }

        AcsLog log = new AcsLog();
        log.setContent(remark);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        AcsLogRunner acsLogRunner = new AcsLogRunner();
        if (currentUser != null) {
            log.setName(currentUser.getName());
            log.setLoginIp(currentUser.getCurrLoginIp());
            log.setUserId(currentUser.getId());
            log.setLogType(MdcUtils.getLogType());
            acsLogRunner.setLog(log);
        } else if (joinPoint.getSignature().getName().equalsIgnoreCase("activate")) {
            HttpServletRequest request = (HttpServletRequest) joinPoint.getArgs()[0];
            String username = (String) joinPoint.getArgs()[1];
            AcsUser user = acsUserService.getByUsername(username);
            if (user != null) {
                log.setName(user.getName());
                log.setLoginIp(ThreadContextUtils.getRemoteIp());
                log.setLogType(MdcUtils.getLogType());
                acsLogRunner.setLog(log);
            }
        } else {
            // 一些接口无需登入访问，也需记录
            log.setName("anonymous");
            log.setLoginIp(ThreadContextUtils.getRemoteIp());
            log.setUserId(0);
            log.setLogType(MdcUtils.getLogType());
            acsLogRunner.setLog(log);
        }
        ThreadPoolService.getInstance().execute(acsLogRunner);
    }
}
