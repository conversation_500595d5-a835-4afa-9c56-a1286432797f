package com.jetron.nb.common.exception;

import com.jetron.nb.common.constant.ApiCode;
import lombok.Getter;

@Getter
public class IotException extends RuntimeException {
    private final ApiCode apiCode;
    private Object data = null;

    public IotException(ApiCode apiCode) {
        this(apiCode, null);
    }

    public IotException(ApiCode apiCode, Object data) {
        this.apiCode = apiCode;
        this.data = data;
    }
}
