package com.jetron.nb.common.exception;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.vo.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 *
 * Date: 2019-12-12
 * Version: 1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    public Object processException(MaxUploadSizeExceededException ex) {
         return Result.toResult(ApiCode.UPLOAD_FILE_EXCEED_MAX);
    }
}
