package com.jetron.nb.common.constant;

/**
 *  参数比较运算符
 */
public enum OperatorEnum {
    /**
     *  参数信息，其中需一个符合要求
     */
    OR(0,"0"),
    /**
     *  所有参数均需满足
     */
    AND(1,"1"),
    /**
     *   实际值大于参数值异常
     */
    BIGGER(2,"2"),
    /**
     *  实际值小于参数值异常
     */
    LESS(3,"3"),
    /**
     *  实际值等于参数值，异常
     */
    EQUAL(4,"4"),
    /**
     *  实际值不等于参数值，异常
     */
    NOT_EQUAL(5,"5"),

    /**
     *  实际值不在范围内，异常
     */
    BETWEEN(6,"6");

    private final int key;
    private final String value;

    OperatorEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public OperatorEnum getEnumByKey(int key) {
        if (key < 0) {
            return null;
        }
        for (OperatorEnum temp : OperatorEnum.values()) {
            if (temp.getKey() == key) {
                return temp;
            }
        }
        return null;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
