package com.jetron.nb.common.constant;

public enum ConfigUpgradeStatus {
    UNKOWN(-1, "未知"),
    WAITING(0, "等待升级"),
    DOWNLOAD_ERROR(1, "下载失败"),
    VERIFY_ERROR(2, "校验失败"),
    SUCCESS(3, "升级成功"),
    TIMEOUT(4, "升级超时");

    ConfigUpgradeStatus(int status, String describ) {
        this.status = status;
        this.describ = describ;
    }

    public final int status;
    public final String describ;

    public static ConfigUpgradeStatus getByStatus(int status) {
        ConfigUpgradeStatus[] enums = ConfigUpgradeStatus.class.getEnumConstants();
        for (ConfigUpgradeStatus tmp : enums) {
            if (tmp.status == status) {
                return tmp;
            }
        }

        return UNKOWN;
    }
}
