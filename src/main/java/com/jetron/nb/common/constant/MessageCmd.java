package com.jetron.nb.common.constant;

public enum MessageCmd {
    FIRMWARE_UPGRADE(1, "固件升级"),
    FIRMWARE_UPGRADE_RESULT(2, "固件升级结果"),
    DEVICE_ACTIVATE(3, "设备激活"),
    DEVICE_ACTIVATE_RESULT(4, "设备激活结果"),
    DEVICE_HEARTBEAT(5, "心跳"),
    DEVICE_HEARTBEAT_RESULT(6, "心跳回复"),
    DEVICE_LOG_UPLOAD(7, "日志上传"),
    DEVICE_UPLOAD_RESULT(8, "日志上传结果"),
    DEVICE_CONFIG_UPGRADE(9, "设备配置升级"),
    DEVICE_CONFIG_UPGRADE_RESULT(10, "设备配置升级结果"),
    DEVICE_FLOW_UPLOAD(11, "设备流量上报"),
    DEVICE_INFO_UPLOAD(12, "设备信息上报"),
    VPN_CONFIG_REQUEST(13, "VPN配置请求"),
    VPN_CONFIG_DISPATCH(14, "VPN配置下发"),
    RESTART_DEVICE(15, "重启"),
    FIND_ALL_GATEWAY(15, "Bremote获取账户下全部网关信息"),
    RESET_DEVICE(16, "恢复出厂设置"),
    FIND_ALL_GATEWAY_RESULT(16, "Bremote获取账户下全部网关信息结果"),
    THRESHOLD_ALARM(17, "阈值信息报警"),
    SET_GATEWAY_ALIAS(177, "Bremote设备网关别名"), // 原CMD=17，临时处理，后续需要修改
    IP_PASS(18, "IP放行"),
    SET_GATEWAY_ALIAS_RESULT(18, "Bremote设备网关别名结果"),
    NTP_SERVER(19, "NTP服务器配置"),
    GET_CURRENT_GATEWAY(19, "Bremote获取当前网关信息"),
    NET_CONFIG(20, "5G SA&NSA 配置"),
    GET_CURRENT_GATEWAY_RESULT(20, "Bremote获取当前网关信息结果"),
    APN_CONFIG(21, "APN配置"),
    GET_OPENVPN_CONFIG(21, "Bremote获取openvpn"),
    BIND_IP(22, "IP绑定"),
    PUSH_OPENVPN_CONFIG(22, "推送给Bremote相关openvpn"),
    DEVICE_INFO_ALIAS(23, "设备别名、位置描述"),
    NETWORK_MONITOR(24, "网络监控"),
    NETWORK_MONITOR_S(25, "网络监控配置订阅"),
    NTP_SERVER_S(26, "NTP服务器配置订阅"),
    BIND_IP_S(27, "IP绑定订阅"),
    LAN_IP_S(28, "下发LAN口IP"),
    LAN_IP_R(29, "下发LAN口IP结果"),
    PORT_MAPPING_DOWN(30, "端口映射规则配置"),
    PORT_MAPPING_UP(31, "端口映射规则配置结果"),
    NAT_RULE_DELIVERY(32, "NAT规则下行"),
    NAT_RULE_CALLBACK(33, "NAT规则上行"),
    DRIVER_UPGRADE(100, "驱动升级"),
    DRIVER_UPGRADE_RECEIVED(101, "驱动下载状态"),
    TEMP(-1, "IP绑定订阅"),
    TEMP2(-2, "IP绑定订阅"),

    DELETE_CHILDREN_DEVICE(32,"删除下挂设备"),
    GET_TIME(34,"获取的时间"),
    PUSH_TIME(35,"推送获取的时间"),

    UPPER_LAYER_PUSH(106,"北向下发推送"),
    UPPER_LAYER_PUSH_RESULT(107,"北向数据上行接受"),
    OPCUA_PUSH(108,"北向OPCUA下发推送"),
    OPCUA_PUSH_RESULT(109,"北向OPCUA上行接受"),
    IMPORT_CERTIFICATE(112,"向网关导入证书"),
    IMPORT_CERTIFICATE_RESULT(113,"向网关导入证书结果"),
    REQUEST_PUSH_AGREEMENT(110,"请求网关获取北向下发协议"),
    GET_PUSH_AGREEMENT(111,"网关返回北向下发协议"),


    GET_HEADSCALE_PREAUTHKEY(200,"获取Headscale预授权密钥"),
    PUSH_HEADSCALE_PREAUTHKEY_RESULT(201,"下发Headscale预授权密钥结果"),
    ENABLE_HEADSCALE_ROUTER(202,"启用Headscale路由"),
    ENABLE_HEADSCALE_ROUTER_RESULT(203,"启用Headscale路由结果"),

    LOGOFF_HEADSCALE(333, "退出Headscale程序，logoff"),
    ;


    MessageCmd(int cmd, String desc) {
        this.cmd = cmd;
        this.desc = desc;
    }

    public final int cmd;
    public final String desc;
}
