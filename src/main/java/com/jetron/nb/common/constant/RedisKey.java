package com.jetron.nb.common.constant;

public enum RedisKey {
    LOGIN_FAILT_COUNT("login_failed_%s", 24 * 3600, "登录失败次数"),
    ALARM_STATUS("alarm_status_", 1000 * 3600, "设备报警状态"),
    ALARM_TODAY("alarm_today", 24 * 3600, "今日报警次数"),
    ALL_SN_SET("allSn", 48 * 3600, "保存所有sn"),
    ALL_USER_SET("allUser", 48 * 3600, "保存所有用户");

    RedisKey(String key, long expire, String describ) {
        this.key = key;
        this.expire = expire;
        this.describ = describ;
    }

    public final String key;
    public final long expire;
    public final String describ;
}
