package com.jetron.nb.common.constant;

public enum Topic {
    DEVICE_TOPIC("MQTT/%s/SN/%s", "设备信息"),
    ACS_SYNC_TOPIC("MQTT/ACS/SYNC", "ACS交互"),
    USER_TOPIC_PUBLISH("MQTT/%s/config", "用户VPN配置"),

    DEVICE_TOPIC_UPGRADE("MQTT/%s/SN/%s/%s", "新版本设备信息");

    Topic(String name, String describ) {
        this.name = name;
        this.describ = describ;
    }

    public final String name;
    public final String describ;
}
