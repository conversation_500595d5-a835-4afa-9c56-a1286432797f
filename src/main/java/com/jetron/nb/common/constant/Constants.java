package com.jetron.nb.common.constant;

public class Constants {
    // captureAlarm
    // 激活
    public static final String ALARM_STATUS1 = "1";
    // 确认
    public static final String ALARM_STATUS2 = "2";
    // 清除
    public static final String ALARM_STATUS3 = "3";

    public static final String IP_ALARM_AUTO_DICT = "IP_ALARM_AUTO";


    public static final String YES = "1";

    public static final String NO = "0";

    public static final int YES_INT = 1;

    public static final int NO_INT = 0;

    public static class AlarmCode {
        public static final String IP = "ip";
    }


    //
    public static final String MQTT_ONLINE_SUB_PREFIX = "idSub";
    public static final String MQTT_ONLINE_PUB_PREFIX = "idPub";

    public static final String MQTT_DOCKER_NAME = "docker_emqx";

    public static final String VPN_DOCKER_NAME = "docker_openvpn";

    // NAT推送方式
    public static final String NAT_TYPE_GET = "get";
    public static final String NAT_TYPE_DEL = "del";
    public static final String NAT_TYPE_ADD = "add";

    // NAT状态
    public static final String NAT_RLUE_STATUS_NORMAL = "0";
    public static final String NAT_RLUE_STATUS_LOCK = "1";

    public static final String MULTILAYER_TWO = "1"; //  vpn是否是二层
    public static class ContainerErrorCode {
        public static final String ERR_CODE_1 = "1";
        public static final String ERR_CODE_2 = "2";
        public static final String ERR_CODE_3 = "3";
        public static final String ERR_CODE_4 = "4";
        public static final String ERR_CODE_5 = "5";
        public static final String ERR_CODE_6 = "6";
        public static final String ERR_CODE_7 = "7";
    }

    public static class PamameterCode {
        public static final String CPU = "CpuRation";
        public static final String RAM = "ram";
        public static final String TEMPTURE = "DevTemp";
        public static final String RSSI = "rssi";
        public static final String QUALITY = "SignalQuality";
        public static final String OFFLINE = "offline";
        public static final String TCP_CONNECTIONS = "tcpConnections";
        public static final String UDP_CONNECTIONS = "udpConnections";
    }

    public static class CompanyDict {
        public static String DICT_TYPE_FLOW = "companyFlow";
        public static String DICT_lABEL_FLOW = "流量计算起始时间";
    }

    public static class TableNames {
        public static String USER_DEVICE = "acs_user_device";
        public static String DICT_lABEL_FLOW = "流量计算起始时间";
    }

    public static interface DriverUpgradeStatus{
        // NMS通知网关升级
        Integer INT = -1;
        // 成功
        Integer zero = 1;
        // cmd=100 数据格式不正确
        Integer one = 1;
        // 文件Md5校验失败
        Integer two = 2;
        // default_config.json文件确实
        Integer three = 3;
        // 驱动文件缺失
        Integer four = 4;
        // default_config.json文件格式不正确。
        Integer five = 5;
        // 强制完成
        Integer complete = 100;
    }

}
