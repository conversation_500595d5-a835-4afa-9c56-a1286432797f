package com.jetron.nb.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 具体参数详见：https://www.emqx.io/docs/zh/v4.0/advanced/http-api.html#api-endpoints
 */
@Getter
@Setter
public class MqttClientInfo {
    Integer code;
    List<Meta> meta;
    List<Data> data;

    class Meta {
        Integer page;
        Integer limit;
        Integer count;
        boolean hasnext;
    }

    public class Data {
        String username;
        String connected_at;
        String created_at;
        String disconnected_at;
        Integer send_cnt;
        Integer recv_cnt;
        Integer send_pkt;
        Integer send_msg;
        Integer recv_pkt;
        /**
         *  接收的 PUBLISH 报文数量
         */
        Integer recv_msg;
        /**
         *  此客户端已建立的订阅数量
         */
        Integer subscriptions_cnt;
    }
}
