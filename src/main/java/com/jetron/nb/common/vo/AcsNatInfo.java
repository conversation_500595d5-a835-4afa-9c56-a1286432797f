package com.jetron.nb.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AcsNatInfo {

    private String sn;
    // 1关闭 0启用
    private Integer enable;
    // 唯一表示一条NAT规则
    private String displayName;
    // NAT规则类型 -- 动作
    private String target;
    // 协议
    private String protocol;
    // 源地址
    private String srcIP;
    // 源起始端口号
    private Integer srcPortS;
    // 源终止端口号
    private Integer srcPortE;
    // 目标IP
    private String destIP;
    // 目的起始端口号
    private Integer destPortS;
    // 目的终止端口号
    private Integer destPortE;
    // 重写IP
    private String snatIP;
    // 重写起始端口号
    private Integer snatPortS;
    // 重写终止端口号
    private Integer snatPortE;
    // 网络接口
    private String netInterface;

}

