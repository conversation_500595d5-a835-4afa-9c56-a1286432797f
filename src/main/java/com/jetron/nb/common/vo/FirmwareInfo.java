package com.jetron.nb.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class FirmwareInfo implements Serializable {
    private static final long serialVersionUID = 5220649035046417488L;

    private Integer id;
    private String name;
    private String model;
    private String company;
    private Integer configId;
    private String version;
    private Integer type;
    private String describ;
    private Date gmtCreate;

}
