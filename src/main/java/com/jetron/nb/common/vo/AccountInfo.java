package com.jetron.nb.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class AccountInfo implements Serializable {
    private Integer id;
    private String name;
    private Integer role;
    private String company;
    private String describ;
    private String lastLoginIp;
    private Date lastLoginTime;
    private String currLoginIp;
    private Date currLoginTime;
    private Integer status;
    private Integer del;
    private Date gmtCreate;
    private Date gmtModify;
    private String vpnHost;
    private Integer vpnPort;
    private String multilayer;
}
