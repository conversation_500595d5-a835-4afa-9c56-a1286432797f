package com.jetron.nb.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class UserInfo implements Serializable {
    private static final long serialVersionUID = -2253797623369632484L;

    private Integer userId;
    private String name;
    private String passwd;
    private String company;
    private String describ;
    private String vpnHost;
    private Integer vpnPort;
    private String multilayer;
}
