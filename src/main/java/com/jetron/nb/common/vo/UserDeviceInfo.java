package com.jetron.nb.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class UserDeviceInfo implements Serializable {
    private static final long serialVersionUID = -1232486484244759928L;

    private Integer id;
    private String name;
    private String model;
    private String sn;
    private String subIp;
    private String dumIp;
    private String trueIp;
    // 在线状态 0离线 1在线
    private Integer status;
    private Integer onlineTime;
    private String verNo;
    private String hardVerNo;
    private String signalNum;
    private String signal5G;
    private String gps;
    private String describ;
    private Integer baseDevNum;
    private String temperature;
    private String cellId;
    private String imei;
    private String imsi;
    private String rssi;
    private String sinr;
    private String networkType;
    // 报警状态 0 正常 1报警
    private Integer deviceStatus;
    private String path;


    private String company;
    private String username;
    private String deviceUrl;
    private String alias;
    private String position;
    private Double flow;
    private String villageNum;
    private String bindIp;
    private String iccid;
    private Date loginDate;
    private String signalQuality;

    // 驱动升级状态
    private Integer driverUpStatus;
    // 是否上传点表
    private boolean uploadPoint;
    // 网关启动时长，秒数（区别online）
    private Integer uptime;
    // lanip
//    private String lanip;
    // 1表示启用锁小区，0表示未启用
    private String lockCellEnable;
    // 1表示启用锁小区，0表示未启用
    private String lock4GCellEnable;
    // 网关LAN口ip
    private String lanIp;
    // 精度
    private Double lon;
    // 维度
    private Double lat;
    // 当前路由出接口 WAN、4G/5G、Wifi
    private String iface;
}
