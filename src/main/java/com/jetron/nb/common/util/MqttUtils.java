package com.jetron.nb.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.vo.MqttClientInfo;
import com.jetron.nb.common.vo.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MqttUtils {

    public static final int SUCCESS_CODE = 0;

    public static final String MYSQL_PLUGIN_NAME = "emqx_auth_mysql";

    public static String urlPrefix;
    public static String username;
    public static String password;

    @Value("${mqtt.api.prefix}")
    public void setUrlPrefix(String urlPrefix) {
        MqttUtils.urlPrefix = urlPrefix;
    }

    @Value("${mqtt.platform.username}")
    public void setUsername(String username) {
        MqttUtils.username = username;
    }

    @Value("${mqtt.platform.password}")
    public void setPassword(String password) {
        MqttUtils.password = password;
    }

    public static String getBasic() {
        String passStr = username + ":" + password;
        String s = Base64.getEncoder().encodeToString(passStr.getBytes());
        return "Basic " + s;
    }

    public static Result getNode() {
        String url = urlPrefix + "nodes";
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error(json.toJSONString());
                return ResultUtils.fail();
            }
            JSONArray array = json.getJSONArray("data");
            if (CollectionUtils.isEmpty(array)) {
                log.error("没有节点错误！{}", s);
                return ResultUtils.fail();
            }
            return Result.success(array.getObject(0, Node.class));
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.fail();
        }
    }

    public static Result getPlugs() {
        String url = urlPrefix + "plugins";
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error(json.toJSONString());
                return ResultUtils.fail();
            }
            JSONArray nodesArray = json.getJSONArray("data");
            JSONObject node = nodesArray.getJSONObject(0);
            List<Plugin> plugins = JSONArray.parseArray(node.getString("plugins"), Plugin.class);
            if (CollectionUtils.isEmpty(plugins)) {
                log.error("没有节点错误！{}", s);
                return ResultUtils.fail();
            }
            return Result.success(plugins);
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.fail();
        }
    }

    public static Result getClinets() {
        String url = urlPrefix + "clients";
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error(json.toJSONString());
                return ResultUtils.fail();
            }
            List<Client> clientList = JSONArray.parseArray(json.getString("data"), Client.class);
            clientList = clientList == null ? new ArrayList<>() : clientList;
            return Result.success(clientList);
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.error();
        }
    }

    public static MqttClientInfo getClients() {
        String url = urlPrefix + "clients";
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String result = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(result)) {
                return null;
            }
            return JSON.parseObject(result, MqttClientInfo.class);
        } catch (IOException e) {
            log.error("" + e);
            return null;
        }
    }

    public static Result getSubscribe(String clinentId) {
        String url = urlPrefix + "subscriptions";
        if (StringUtils.isNotBlank(clinentId)) {
            url += "/" + clinentId;
        }
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error(json.toJSONString());
                return ResultUtils.fail();
            }
            List<Subscribe> subscribeList = JSONArray.parseArray(json.getString("data"), Subscribe.class);
            subscribeList = subscribeList == null ? new ArrayList<>() : subscribeList;
            return Result.success(subscribeList);
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.error();
        }
    }

    public static Result restartMysqlPlugin() {
        Result nodeResult = getNode();
        if (!ResultUtils.isSuccess(nodeResult)) {
            log.error("加载Mysql插件失败！请求节点失败");
            return nodeResult;
        }
        Node node = (Node) nodeResult.getData();
        String url = urlPrefix + "nodes/" + node.getNode() + "/plugins/emqx_auth_mysql/load";
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.put(url, null, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (json.containsKey("message") && "already_started".equals(json.getString("message"))) {
                return Result.success();
            }
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error("加载Mysql插件失败！返回结果" + json.toJSONString());
                return ResultUtils.fail();
            }
            return Result.success();
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.error();
        }
    }

    public static Result mysqlPlugsRunning() {
        Result plugs = getPlugs();
        if (!ResultUtils.isSuccess(plugs)) {
            return ResultUtils.error();
        }
        List<Plugin> pluginList = (List<Plugin>) plugs.getData();
        for (int i = 0; i < pluginList.size(); i++) {
            Plugin plugin = pluginList.get(i);
            if (MYSQL_PLUGIN_NAME.equals(plugin.getName())) {
                if (plugin.active) {
                    return Result.success();
                }
                return ResultUtils.fail();
            }
        }
        return ResultUtils.error();
    }

    public static Result deviceSubscribe() {
        Result clinetResult = getClinets();
        if (!ResultUtils.isSuccess(clinetResult)) {
            return ResultUtils.error();
        }
        List<Client> clientList = (List<Client>) clinetResult.getData();
        Map<String, DeviceSubscribe> dataMap = new HashMap<>();
        for (int i = 0; i < clientList.size(); i++) {
            Client client = clientList.get(i);
            String clientid = client.getClientid();
            String createdAt = client.getCreatedAt();
            if (clientid.contains(Constants.MQTT_ONLINE_SUB_PREFIX)
                    || clientid.contains(Constants.MQTT_ONLINE_PUB_PREFIX)) {

                String sn = clientid.substring(Constants.MQTT_ONLINE_SUB_PREFIX.length());
                DeviceSubscribe deviceSubscribe = dataMap.get(sn);
                deviceSubscribe = deviceSubscribe == null ? new DeviceSubscribe() : deviceSubscribe;
                deviceSubscribe.setSn(sn);

                List<String> connectTimeList = deviceSubscribe.getConnectTimeList();
                if (connectTimeList == null) {
                    connectTimeList = new ArrayList<>();
                    connectTimeList.add(null);
                    connectTimeList.add(null);
                }

                if (clientid.contains(Constants.MQTT_ONLINE_SUB_PREFIX)) {
                    deviceSubscribe.setSubBool(true);
                    deviceSubscribe.setSubTopicNum(deviceSubscribe.getSubTopicNum() + 1);
                    String connecTimeStr = Constants.MQTT_ONLINE_SUB_PREFIX + ": " + createdAt;
                    connectTimeList.set(1, connecTimeStr);
                }
                if (clientid.contains(Constants.MQTT_ONLINE_PUB_PREFIX)) {
                    deviceSubscribe.setPubBool(true);
                    String connecTimeStr = Constants.MQTT_ONLINE_PUB_PREFIX + ": " + createdAt;
                    connectTimeList.set(0, connecTimeStr);
                }
                deviceSubscribe.setConnectTimeList(connectTimeList);

                List<String> topicList = deviceSubscribe.getTopicList();
                if (CollectionUtils.isEmpty(topicList)) {
                    topicList = new ArrayList<>();
                    Result subscribe = getSubscribe(clientid);
                    if (ResultUtils.isSuccess(subscribe)) {
                        List<Subscribe> subscribeList = (List<Subscribe>) subscribe.getData();
                        List<String> topicsList222 = subscribeList.stream().map(Subscribe::getTopic).collect(Collectors.toList());
                        topicList.addAll(topicsList222);
                    }
                    deviceSubscribe.setTopicList(topicList);
                }
                dataMap.put(sn, deviceSubscribe);
            } else {
                // 网关新改动，mqtt只有一个 名为sn的客户端
                DeviceSubscribe deviceSubscribe = new DeviceSubscribe();

                List<String> connectTimeList = new ArrayList<>();
                List<String> topicList = new ArrayList<>();
                deviceSubscribe.setSn(clientid);
                deviceSubscribe.setSubBool(true);
                deviceSubscribe.setPubBool(true);
                deviceSubscribe.setSubTopicNum(1);
                connectTimeList.add(Constants.MQTT_ONLINE_PUB_PREFIX + ": " + createdAt);
                connectTimeList.add(Constants.MQTT_ONLINE_SUB_PREFIX + ": " + createdAt);
                deviceSubscribe.setConnectTimeList(connectTimeList);
                Result subscribe = getSubscribe(clientid);
                if (ResultUtils.isSuccess(subscribe)) {
                    List<Subscribe> subscribeList = (List<Subscribe>) subscribe.getData();
                    List<String> topicsList222 = subscribeList.stream().map(Subscribe::getTopic).collect(Collectors.toList());
                    topicList.addAll(topicsList222);
                }
                deviceSubscribe.setTopicList(topicList);
                dataMap.put(clientid, deviceSubscribe);
            }
        }
        List<DeviceSubscribe> list = new ArrayList<>(dataMap.values());
        for (int i = 0; i < list.size(); i++) {
            DeviceSubscribe subscribe = list.get(i);
            if (subscribe.getPubBool() == null || (boolean) subscribe.getPubBool() == false
                    || subscribe.getSubBool() == null || (boolean) subscribe.getSubBool() == false) {

                subscribe.setAdvise("网关重启");
            }
        }
        return Result.success(list);
    }


    public static Result getClientsByClientId(String clientId) {
        String url = urlPrefix + "clients";
        if (StringUtils.isNotBlank(clientId)) {
            url += "/" + clientId;
        }
        Map<String, String> heard = new HashMap<>();
        heard.put("Authorization", getBasic());
        try {
            String s = HttpUtils.get(url, heard);
            if (!JSONObject.isValid(s)) {
                return ResultUtils.fail();
            }
            JSONObject json = JSONObject.parseObject(s);
            if (!json.containsKey("code") || SUCCESS_CODE != json.getIntValue("code")) {
                log.error(json.toJSONString());
                return ResultUtils.fail();
            }
            List<Client> clientList = JSONArray.parseArray(json.getString("data"), Client.class);
            clientList = clientList == null ? new ArrayList<>() : clientList;
            return Result.success(clientList);
        } catch (IOException e) {
            log.error("" + e);
            return ResultUtils.error();
        }
    }


    public static String analyseSN(String clientId) {
        if (clientId.contains(Constants.MQTT_ONLINE_SUB_PREFIX)
                || clientId.contains(Constants.MQTT_ONLINE_PUB_PREFIX)) {
            return clientId.substring(Constants.MQTT_ONLINE_SUB_PREFIX.length());
        } else {
            return clientId;
        }
    }


    @Data
    public static class Node {
        private String version;
        private String node;
        private String nodeStatus;
    }

    @Data
    public static class Plugin {
        private String name;
        private boolean active;
    }

    @Data
    public static class Subscribe {
        private String clientid;
        private String node;
        private String topic;
    }

    @Data
    public static class Client {
        private String clientid;
        private String createdAt;
        private String username;
        private String ipAddress;
        private String node;
        private Boolean connected;
    }

    @Data
    public static class DeviceSubscribe {
        private String sn;
        private Boolean pubBool;
        private Boolean subBool;
        private int subTopicNum;
        private List<String> topicList;
        private List<String> connectTimeList;
        private String advise;
    }

}
