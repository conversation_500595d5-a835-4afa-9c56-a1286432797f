package com.jetron.nb.common.util;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class RequestUtils {

    public static JSONObject getJSONParam(HttpServletRequest request) throws IOException {
        InputStreamReader streamReader = new InputStreamReader(request.getInputStream(), "utf-8");
        char[] read = new char[1024];
        int len;
        StringBuffer sb = new StringBuffer();
        while ((len = streamReader.read(read)) != -1) {
            String s = new String(read, 0, len);
            sb.append(s);
        }
        return JSONObject.parseObject(sb.toString());
    };

    public static JSONObject getFormParam(HttpServletRequest request) throws IOException {
        Enumeration<?> pNames = request.getParameterNames();
        JSONObject params = new JSONObject();
        while (pNames.hasMoreElements()) {
            String pName = (String) pNames.nextElement();
            Object pValue = request.getParameter(pName);
            params.put(pName, pValue);
        }
        return params;
    };
}
