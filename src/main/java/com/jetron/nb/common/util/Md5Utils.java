package com.jetron.nb.common.util;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 *
 * Date: 2019-06-04 11:05
 * Version: 1.0.0
 */
public class Md5Utils {
    private static Charset UTF_8 = Charset.forName("utf-8");

    public final static String getMD5(String s) {
        char hexDigits[] = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            byte strTemp[] = s.getBytes(UTF_8);
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(strTemp);
            byte b[] = md.digest();

            int len = b.length;
            char str[] = new char[len*2];
            int k=0;
            for(int i=0; i<len; i++){
                str[k++] = hexDigits[b[i] >>> 4 & 0xf];
                str[k++] = hexDigits[b[i] & 0xf];
            }
            return new String(str);

        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public final static String getPureMD5(String s) {
        try {
            byte strTemp[] = s.getBytes(UTF_8);
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(strTemp);
            byte b[] = md.digest();
            return new String(b,UTF_8);

        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(Md5Utils.getMD5("63833b8e6"));
        System.out.println(Md5Utils.getMD5("3a42abc1ab3b6ca036a818b6df2fe1b9"));
        System.out.println(Md5Utils.getMD5("Muhtadin"));
    }

}
