package com.jetron.nb.common.util;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class PageUtils {

    public static Map<String, Object> toPageResult(int total, int page, int size, List records) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("total", total);
        data.put("page", page);
        data.put("size", size);
        data.put("records", records);

        return data;
    }

    public static Map<String, Object> toPageResult(int total, int page, int size, List records,Object obj) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("total", total);
        data.put("page", page);
        data.put("size", size);
        data.put("records", records);
        data.put("data", obj);

        return data;
    }
}
