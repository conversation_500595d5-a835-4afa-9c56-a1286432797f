package com.jetron.nb.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class LicenseUtils {
    static  byte [] key = new byte[] {
            (byte)0x30,
            (byte)0x81, (byte)0x9F, (byte)0x30, (byte)0x0D, (byte)0x06, (byte)0x09, (byte)0x2A, (byte)0x86,
            (byte)0x48, (byte)0x86, (byte)0xF7, (byte)0x0D, (byte)0x01, (byte)0x01, (byte)0x01, (byte)0x05,
            (byte)0x00, (byte)0x03, (byte)0x81, (byte)0x8D, (byte)0x00, (byte)0x30, (byte)0x81, (byte)0x89,
            (byte)0xE3, (byte)0xBB, (byte)0xE3, (byte)0xB1, (byte)0x67, (byte)0xAC, (byte)0x2A, (byte)0x9D,
            (byte)0x9D, (byte)0x67, (byte)0xB0, (byte)0x9D, (byte)0x3A, (byte)0xDE, (byte)0x48, (byte)0xA5,
            (byte)0x2A, (byte)0xE8, (byte)0xBB, (byte)0xC6, (byte)0xE2, (byte)0x39, (byte)0x0D, (byte)0x41,
            (byte)0xDF, (byte)0x76, (byte)0xD0, (byte)0xA7, (byte)0x02, (byte)0x03, (byte)0x01, (byte)0x00,
            (byte)0x01,
    };

    public static String generateKeys(String value) {
        StringBuilder sb = new StringBuilder();
        for (char aChar : value.toCharArray()) {
            sb.append(ShaUtils.SHA512(String.valueOf(aChar)));
        }

        return sb.toString();
    }

    public static String stringToAscii(String value)
    {
        StringBuilder sb = new StringBuilder();
        char[] chars = value.toCharArray();
        for (char aChar : chars) {
            if ((int) aChar < 100) {
                sb.append("0").append((int) aChar);
                continue;
            }
            sb.append((int) aChar);
        }

        return sb.toString();
    }

    public static String asciiToString(String value)
    {
        StringBuilder sb = new StringBuilder();
        int index = 0;
        while (index < value.length()) {
            String str = value.substring(index, index + 3);
            sb.append((char) Integer.parseInt(str));
            index = index + 3 ;
        }
        return sb.toString();
    }

    public static boolean checkLicensePath(String filePath) {
        return FileUtils.isFileExists(filePath);
    }

    public static boolean checkLicense(String filepath) {
        String license = FileUtils.readFile(filepath);
        if (StringUtils.isEmpty(license)) {
            return false;
        }
        String[] content = license.split("\n");

        return checkLicense(content);
    }

    public static boolean checkLicense(String[] content) {
        boolean isOK = false;
        if (content.length < 3) {
            return isOK;
        }

        try {
            String outDateStr = asciiToString(content[0]);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date outDate = format.parse(outDateStr);
            String keys = generateKeys(stringToAscii(outDateStr));
            String modificationDateStr = asciiToString(content[2]);
            Date modificationDate = format.parse(modificationDateStr);
            if (keys.equals(content[1]) && outDate.compareTo(new Date()) >= 0 && modificationDate.compareTo(new Date()) <= 0) {
                isOK = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return isOK;
    }

    // 获取licence中员工创建人数，没有就为0
    public static Integer getLicenseLimiter(String filepath) {
        String license = FileUtils.readFile(filepath);
        if (StringUtils.isEmpty(license)) {
            return null;
        }
        String[] content = license.split("\n");
        if (content.length > 3) {
            return Integer.parseInt(asciiToString(content[3]));
        }
        return 0;
    }

    public static String generateLicenseValue(String expireDate, Integer limiter) {
        String date = stringToAscii(expireDate);
        String keys = generateKeys(date);
        Date now = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String modificationDate = format.format(now);
        modificationDate = stringToAscii(modificationDate);
        // 生成过期时间信息
        StringBuilder licenseValue = new StringBuilder(date + "\n" + keys + "\n" + modificationDate);
        // 如果限制人数参数不为空 就在尾部拼接数据
        if (limiter != null && limiter > 0) {
            String limiterValue = stringToAscii(String.valueOf(limiter));
            licenseValue.append("\n").append(limiterValue);
        }
        return licenseValue.toString();
    }

}
