package com.jetron.nb.common.util;

import com.jetron.nb.common.method.LocalCommandExecutorImpl;
import com.jetron.nb.common.method.StreamGobbler;
import com.jetron.nb.common.vo.ExecuteResult;
import com.jetron.nb.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Arrays;


/**
 * 用来执行 centos 中的 参数命令
 */
@Component
public class CommandUtils {
    private final static Logger logger = LoggerFactory.getLogger(CommandUtils.class);

    @Autowired
    private LocalCommandExecutorImpl localCommandExecutor;

    public static CommandUtils commandUtils;

    @PostConstruct
    public void init() {
        commandUtils = this;
        commandUtils.localCommandExecutor = this.localCommandExecutor;
    }

    /**
     * 命令执行
     *
     * @param cmd 例子：String[] cmd = new String[]{”/bin/sh”, “-c”, ” ls “};
     * @return 执行结果，0表示执行成功
     */
    public static int exec(String[] cmd) {
        String result = "error";
        Process ps = null;
        try {
            logger.info("CMD : " + Arrays.toString(cmd) + "    开始执行!\n");
            if (Arrays.toString(cmd).contains(">")) {
                return execOutput(cmd);
            }

            ps = Runtime.getRuntime().exec(cmd);
            BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            StringBuffer sb = new StringBuffer();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            result = sb.toString();
            System.out.println(result);
            br.close();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("CMD : " + Arrays.toString(cmd) + "\n" + e.toString());
        } finally {
            if (ps != null) {
                ps.destroy();
            }
        }
        return result.equals("error") ? 1 : 0;

//        ExecuteResult result = commandUtils.localCommandExecutor.executeCommand(cmd,5000);
//        return result.getExitCode();
    }

    public static int execOutput(String[] cmd) {
        int exec = 0;
        try {
            //FileOutputStream fos = new FileOutputStream("usr.ovpn");
            Runtime rt = Runtime.getRuntime();
            Process proc = rt.exec(cmd);

            // 重定向输出流和错误流
            StreamGobbler errorGobbler = new StreamGobbler(proc.getErrorStream(), "ERROR");
            //StreamGobbler outputGobbler = new StreamGobbler(proc.getInputStream(), "OUTPUT", fos);

            errorGobbler.start();
            //outputGobbler.start();
            int exitVal = proc.waitFor();
            System.out.println("ExitValue: " + exitVal);
            //fos.flush();
            //fos.close();
            exec = 1;
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return exec;
    }

    public static Result execResult(String[] cmd) {
        String result = "error";
        Process ps = null;
        try {
            logger.info("CMD : " + Arrays.toString(cmd) + "    开始执行!\n");
            if (Arrays.toString(cmd).contains(">")) {
                return execOutputResult(cmd);
            }

            ps = Runtime.getRuntime().exec(cmd);
            BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            StringBuffer sb = new StringBuffer();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            result = sb.toString();
            System.out.println(result);
            br.close();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("CMD : " + Arrays.toString(cmd) + "\n" + e.toString());
        } finally {
            if (ps != null) {
                ps.destroy();
            }
        }
        return result.equals("error") ? ResultUtils.fail() : Result.success(result);
    }

    public static Result execOutputResult(String[] cmd) {
        int exec = 0;
        try {
            //FileOutputStream fos = new FileOutputStream("usr.ovpn");
            Runtime rt = Runtime.getRuntime();
            Process proc = rt.exec(cmd);

            // 重定向输出流和错误流
            StreamGobbler errorGobbler = new StreamGobbler(proc.getErrorStream(), "ERROR");
            //StreamGobbler outputGobbler = new StreamGobbler(proc.getInputStream(), "OUTPUT", fos);

            errorGobbler.start();
            //outputGobbler.start();
            int exitVal = proc.waitFor();
            System.out.println("ExitValue: " + exitVal);
            //fos.flush();
            //fos.close();
            exec = 1;
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return exec == 0 ? Result.success() : ResultUtils.fail();
    }

    public static Result execThree(String[] cmd) {
        Result stopExec = ResultUtils.fail();
        for (int i = 0; i < 3; i++) {
            stopExec = CommandUtils.execResult(cmd);
            if (ResultUtils.isSuccess(stopExec)) {
                break;
            }
        }
        return stopExec;
    }

    public static boolean dockerContainerRunning(String containerName) {
        String cmd = String.format("docker ps --filter name=%s  --filter  status=running", containerName);
        String[] cmdArray = new String[]{"/bin/sh", "-c", cmd};
        Result result = execThree(cmdArray);
        if (!ResultUtils.isSuccess(result)) {
            logger.info("dockerContainerRunning:" + containerName + ";结果" + false);
            return false;
        }
        String returnStr = (String) result.getData();
        if (StringUtils.isBlank(returnStr)) {
            logger.info("dockerContainerRunning:" + containerName + ";结果" + false);
            return false;
        }
        String[] split = returnStr.split("\n");
        if (split.length == 1) {
            logger.info("dockerContainerRunning:" + containerName + ";结果" + false);
            return false;
        }
        logger.info("dockerContainerRunning:" + containerName + ";结果" + true);
        return true;
    }

    public static boolean dockerContainerExist(String containerName) {
        String cmd = String.format("docker ps -a --filter name=%s", containerName);
        String[] cmdArray = new String[]{"/bin/sh", "-c", cmd};
        Result result = execThree(cmdArray);
        if (!ResultUtils.isSuccess(result)) {
            logger.info("dockerContainerExist:" + containerName + ";结果" + false);
            return false;
        }
        String returnStr = (String) result.getData();
        if (StringUtils.isBlank(returnStr)) {
            logger.info("dockerContainerExist:" + containerName + ";结果" + false);
            return false;
        }
        String[] split = returnStr.split("\n");
        if (split.length == 1) {
            logger.info("dockerContainerExist:" + containerName + ";结果" + false);
            return false;
        }
        logger.info("dockerContainerExist:" + containerName + ";结果" + true);
        return true;
    }
}
