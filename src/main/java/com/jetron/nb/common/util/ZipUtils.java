package com.jetron.nb.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class ZipUtils {

    private static int BUFFER = 1024;

    public static void main(String[] args) {
        unzip("E:\\11.zip","E:\\");
    }



    public  static String unzip(String filePath,String zipDir) {
        String name = "";
        try {
            BufferedOutputStream dest = null;
            BufferedInputStream is = null;
            ZipEntry entry;
            ZipFile zipfile = new ZipFile(filePath);

            Enumeration dir = zipfile.entries();
            while (dir.hasMoreElements()){
                entry = (ZipEntry) dir.nextElement();
                if( entry.isDirectory()){
                    name = entry.getName();
                    name = name.substring(0, name.length() - 1);
                    File fileObject = new File(zipDir + name);
                    fileObject.mkdir();
                }
            }

            Enumeration e = zipfile.entries();
            while (e.hasMoreElements()) {
                entry = (ZipEntry) e.nextElement();
                if( entry.isDirectory()){
                    continue;
                }else{
                    is = new BufferedInputStream(zipfile.getInputStream(entry));
                    int count;
                    byte[] dataByte = new byte[BUFFER];
                    FileOutputStream fos = new FileOutputStream(zipDir+entry.getName());
                    dest = new BufferedOutputStream(fos, BUFFER);
                    while ((count = is.read(dataByte, 0, BUFFER)) != -1) {
                        dest.write(dataByte, 0, count);
                    }
                    dest.flush();
                    dest.close();
                    is.close();
                }
            }
            zipfile.close();
        } catch (Exception e) {
            log.error("" + e);
        }
        return  name;
    }

    public static void zip(List<File> fileList, String zipPath){
        try{
            File zipFile = new File(zipPath);
            // 判断文件是否存在，如文件不存在创建一个新文件
            if (!zipFile.exists()){
                zipFile.createNewFile();
            }

            // 创建一个zip文件输出流
            ZipOutputStream zipOutput = new ZipOutputStream(new FileOutputStream(zipFile));

            for (File file : fileList){
                // 判断文件是否存在，如不存在直接跳过
                if (!file.exists()){
                    continue;
                }
                /**
                 * 创建一个缓冲读取流，提高读取效率
                 * 也可以直接创建一个 FileInputStream 对象，BufferedInputStream内部维护了一个8KB的缓冲区，BufferedInputStream本身不具备读取能力
                 * BufferedInputStream 可以手动指定缓冲区大小 单位为字节例如：new BufferedInputStream(new FileInputStream(file), 10240)
                 */
                BufferedInputStream bufferedInput = new BufferedInputStream(new FileInputStream(file));
//                InputStream inputStream = new FileInputStream(file);

                // 设置压缩条目名称
                zipOutput.putNextEntry(new ZipEntry(file.getName()));
                byte[] bytes = new byte[1024];
                int len = -1;
                // 读取file内的字节流，写入到zipOutput内
                while ((len = bufferedInput.read(bytes)) != -1){
                    zipOutput.write(bytes,0,len);
                }
                // 关闭输入流
                // 无需关闭new FileInputStream(file)的输入流 因为BufferedInputStream.close()方法内部已经调用了FileInputStream.close()方法
                bufferedInput.close();
                // 写入完毕后关闭条目
                zipOutput.closeEntry();
            }
            zipOutput.close();
        }catch (IOException e){
            e.printStackTrace();
        }
    }
}