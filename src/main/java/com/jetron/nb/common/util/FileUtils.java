package com.jetron.nb.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.zip.GZIPInputStream;

@Slf4j
public class FileUtils {

    public static void doDownload(HttpServletResponse response, File file) throws IOException {
        String filename = file.getName();

        byte[] buffer = new byte[1024];
        FileInputStream fis = null;
        BufferedInputStream bis = null;

        try {
            response.setContentType("APPLICATION/OCTET-STREAM");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(filename, "UTF-8"));

            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);
            OutputStream os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                }
            }

            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                }
            }
        }
    }

    public static void doDownload(HttpServletResponse response,String fileName, String str) throws IOException {
        try {
            response.setContentType("APPLICATION/OCTET-STREAM");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, "UTF-8"));

            OutputStream os = response.getOutputStream();
            os.write(str.getBytes());
            os.flush();
            os.close();
        } finally {
        }
    }

    public static String getExtName(String filename) {
        if (filename.contains(".tar.gz")) {
            return ".tar.gz";
        }

        int index = filename.lastIndexOf(".");

        if (index == -1) {
            return null;
        }
        return filename.substring(index);
    }

    public static boolean isFileExists(String filename) {
        File file = new File(filename);
        try {
            return file.exists();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static String readFile(String filename) {
        FileReader fileReader = null;
        BufferedReader br = null;
        String line = null;
        StringBuilder sb = new StringBuilder();
        try {
            // Target file path
            File targetFile = new File(filename);
            if (!targetFile.exists()) {
                System.out.println(targetFile.getName() + " isn't existed");
                return null;
            }
            // Read target file
            fileReader = new FileReader(targetFile);
            br = new BufferedReader(fileReader);
            line = br.readLine();
            while (line != null) {
                sb.append(line).append("\n");
                // Notice: the following statement is necessary.
                line = br.readLine();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    br = null;
                }
            }
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString();
    }

    public static List<String> read(String filename) {
        FileReader fileReader = null;
        BufferedReader br = null;
        String line = null;
        List<String> list = new ArrayList<>();
        try {
            // Target file path
            File targetFile = new File(filename);
            if (!targetFile.exists()) {
                System.out.println(targetFile.getName() + " isn't existed");
                return null;
            }
            // Read target file
            fileReader = new FileReader(targetFile);
            br = new BufferedReader(fileReader);
            line = br.readLine();
            while (line != null) {
                list.add(line);
                // Notice: the following statement is necessary.
                line = br.readLine();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    br = null;
                }
            }
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return list;
    }

    public static List<String> readResource(String filePath) {
        Resource resource = new ClassPathResource(filePath);
        FileReader fileReader = null;
        BufferedReader br = null;
        String line = null;
        StringBuilder sb = new StringBuilder();
        List<String> lineList = new ArrayList<>();
        try {
            // Target file path
            File targetFile = resource.getFile();
            if (!targetFile.exists()) {
                log.error(targetFile.getName() + " isn't existed");
                return lineList;
            }
            // Read target file
            fileReader = new FileReader(targetFile);
            br = new BufferedReader(fileReader);
            line = br.readLine();
            while (line != null) {
                lineList.add(line);
                line = br.readLine();
            }
        } catch (Exception e) {
            log.error("readFile List<String>;"+e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (fileReader != null) {
                    fileReader.close();
                }
                fileReader.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return lineList;
    }

    public static StringBuilder readCrtFile(String filename) {
        FileReader fileReader = null;
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        try {
            // Target file path
            File targetFile = new File(filename);
            if (!targetFile.exists()) {
                System.out.println(targetFile.getName() + " isn't existed");
                return sb;
            }
            // Read target file
            fileReader = new FileReader(targetFile);
            br = new BufferedReader(fileReader);
            String line = "";
            boolean isAppend = false;
            while ((line = br.readLine()) != null) {
                if (line.contains("-----BEGIN PRIVATE KEY-----")) {
                    sb.append("<key>\n").append("-----BEGIN RSA PRIVATE KEY-----\n");
                    isAppend = true;
                } else if (line.contains("-----END PRIVATE KEY-----")) {
                    sb.append("-----END RSA PRIVATE KEY-----\n").append("</key>\n");
                    isAppend = false;
                } else if (line.contains("-----BEGIN CERTIFICATE-----")) {
                    sb.append("<cert>\n").append("-----BEGIN CERTIFICATE-----\n");
                    isAppend = true;
                } else if (line.contains("-----END CERTIFICATE-----")) {
                    sb.append("-----END CERTIFICATE-----\n").append("</cert>\n");
                    isAppend = false;
                } else if (isAppend) {
                    sb.append(line).append("\n");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    br = null;
                }
            }
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return sb;
    }

    public static void generateLicenseFile(String filepath, String value) throws FileNotFoundException {
        FileOutputStream fos = new FileOutputStream(filepath);
        try {
            PrintWriter pw = new PrintWriter(fos);
            pw.println(value);
            pw.flush();
            pw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Date lastModifiedDate(String filepath) {
        File file = new File(filepath);
        long lastModified = file.lastModified();
        return new Date(lastModified);
    }

    /*写入Text文件操作*/
    public static void writeText(String filePath, String content,boolean isAppend) {
        File file = new File(filePath);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                log.error("create file err:"+e);
            }
        }
        FileOutputStream outputStream = null;
        OutputStreamWriter outputStreamWriter = null;
        BufferedWriter bufferedWriter = null;
        try {
            outputStream = new FileOutputStream(filePath,isAppend);
            outputStreamWriter = new OutputStreamWriter(outputStream);
            bufferedWriter = new BufferedWriter(outputStreamWriter);
            bufferedWriter.write(content);
        } catch (IOException e) {
            log.error("writeText ;"+e);
        } finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.close();
                }
                if (outputStreamWriter != null) {
                    outputStreamWriter.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch(Exception e) {
                log.error("writeText ;"+e);
                e.printStackTrace();
            }
        }
    }

    /**
     * 解压log文件。
     */
    public static void doUncompressTarGZFile(String sourcePath) {
        // 要解压到的目录
        String extractPath = FilenameUtils.getFullPath(sourcePath) + FilenameUtils.getBaseName(FilenameUtils.getBaseName(sourcePath));
        File sourceFile = new File(sourcePath);
        TarArchiveInputStream fin = null;
        try {
            fin = new TarArchiveInputStream(new GzipCompressorInputStream(new FileInputStream(sourceFile)));
            TarArchiveEntry entry;
            // 将 tar 文件解压到 extractPath 目录下
            while ((entry = fin.getNextTarEntry()) != null) {
                if(entry.isDirectory()){//是目录
                    entry.getName();
                    createDirectory(extractPath,entry.getName());//创建空目录
                }else{//是文件
                    File tmpFile = new File(extractPath + "/" + entry.getName());
                    if (entry.getName().contains(":")){
                        String replace = entry.getName().replace(":", "_");
                        tmpFile = new File(extractPath + "/" + replace);
                    }
                    if (!tmpFile.getParentFile().exists()) {
                        tmpFile.getParentFile().mkdirs();
                    }
                    OutputStream out = null;
                    try{
                        out = new FileOutputStream(tmpFile);
                        int length = 0;

                        byte[] b = new byte[2048];

                        while((length = fin.read(b)) != -1){
                            out.write(b, 0, length);
                        }

                    } catch(IOException ex) {
                        throw ex;
                    } finally{

                        if (out!=null) {
                            out.close();
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fin.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 构建目录
     * @param outputDir
     * @param subDir
     */
    public static void createDirectory(String outputDir,String subDir){
        File file = new File(outputDir);
        if (!(subDir == null || subDir.trim().equals(""))) {//子目录不为空
            file = new File(outputDir + "/" + subDir);
        }
        if (!file.exists()) {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            file.mkdirs();
        }
    }

}
