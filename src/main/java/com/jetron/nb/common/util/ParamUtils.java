package com.jetron.nb.common.util;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.vo.UserInfo;
import com.jetron.nb.dal.po.AcsUser;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ParamUtils {
    private static final String IPV4_PATTERN = "^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(\\.(?!$)|$)){4}$";

    public static void validateUserId(Integer userId) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser != null && !Objects.equals(currentUser.getId(), userId)) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
    }

    public static void checkCreateUserInfo(UserInfo userInfo) {
        if (StringUtils.isAnyBlank(userInfo.getName(), userInfo.getPasswd())) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        int role = currentUser.getRole();
        if (role == 0 && (StringUtils.isBlank(userInfo.getVpnHost())
                || userInfo.getVpnPort() == null || userInfo.getVpnPort() < 0)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        } else if (role == 0 && !Pattern.matches(IPV4_PATTERN,userInfo.getVpnHost())){
            throw new IotException(ApiCode.USER_HOST_IP_WRONG);
        } else if (role == 1 && StringUtils.isBlank(userInfo.getCompany())) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
    }

    public static String getNumeric(String str) {
        String regEx="[^0-9-.]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

}
