package com.jetron.nb.common.util;

import com.github.promeg.pinyinhelper.Pinyin;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 中文转拼音工具类
 * 用于将中文字符串转换为拼音，并处理特殊字符
 */
public class PinyinUtils {

    /**
     * 特殊字符正则表达式，包括括号、空格等
     */
    private static final Pattern SPECIAL_CHARS_PATTERN = Pattern.compile("[\\s\\(\\)\\（\\）\\[\\]\\{\\}\\<\\>\\-\\_\\+\\=\\|\\\\\\&\\%\\$\\#\\@\\!\\~\\`\\^\\*\\?\\:\\.\\,\\;\\'\\\"]");

    /**
     * 将中文字符串转换为拼音，并处理特殊字符
     * 
     * @param chinese 中文字符串
     * @return 转换后的拼音字符串，去除特殊字符，全小写
     */
    public static String toPinyin(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        // 先移除特殊字符
        String cleaned = removeSpecialChars(chinese);
        
        if (StringUtils.isBlank(cleaned)) {
            return "";
        }

        // 转换为拼音
        String pinyin = Pinyin.toPinyin(cleaned, "");
        
        // 转换为小写
        return pinyin.toLowerCase();
    }

    /**
     * 将中文字符串转换为拼音，保留分隔符
     * 
     * @param chinese 中文字符串
     * @param separator 分隔符，如 "-" 或 "_"
     * @return 转换后的拼音字符串，用分隔符连接
     */
    public static String toPinyinWithSeparator(String chinese, String separator) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        // 先移除特殊字符
        String cleaned = removeSpecialChars(chinese);
        
        if (StringUtils.isBlank(cleaned)) {
            return "";
        }

        // 转换为拼音，用分隔符连接
        String pinyin = Pinyin.toPinyin(cleaned, separator);
        
        // 转换为小写
        return pinyin.toLowerCase();
    }

    /**
     * 移除字符串中的特殊字符
     * 
     * @param input 输入字符串
     * @return 移除特殊字符后的字符串
     */
    public static String removeSpecialChars(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }

        // 移除特殊字符
        String result = SPECIAL_CHARS_PATTERN.matcher(input).replaceAll("");
        
        return result.trim();
    }

    /**
     * 检查字符串是否包含中文字符
     * 
     * @param input 输入字符串
     * @return 如果包含中文字符返回 true，否则返回 false
     */
    public static boolean containsChinese(String input) {
        if (StringUtils.isBlank(input)) {
            return false;
        }

        // 中文字符的 Unicode 范围
        Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");
        return chinesePattern.matcher(input).find();
    }

    /**
     * 智能处理公司名称，转换为适合作为用户名的格式
     * 
     * @param companyName 公司名称
     * @return 处理后的字符串，适合作为 Headscale 用户的 displayName
     */
    public static String processCompanyName(String companyName) {
        if (StringUtils.isBlank(companyName)) {
            return "";
        }

        // 如果包含中文，转换为拼音
        if (containsChinese(companyName)) {
            return toPinyin(companyName);
        } else {
            // 如果不包含中文，只移除特殊字符
            String result = removeSpecialChars(companyName);
            return result.toLowerCase();
        }
    }

    /**
     * 生成安全的用户名格式
     * 只包含字母、数字、下划线和连字符
     * 
     * @param input 输入字符串
     * @return 安全的用户名格式
     */
    public static String toSafeUsername(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }

        String processed = processCompanyName(input);
        
        // 确保只包含安全字符：字母、数字、下划线、连字符
        String safe = processed.replaceAll("[^a-zA-Z0-9_-]", "");
        
        // 如果结果为空或太短，添加默认前缀
        if (StringUtils.isBlank(safe) || safe.length() < 2) {
            safe = "company_" + System.currentTimeMillis() % 10000;
        }
        
        return safe;
    }
}
