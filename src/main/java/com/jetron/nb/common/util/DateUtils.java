package com.jetron.nb.common.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {

    /**
     * 周一到周日的所有时间
     * @param date
     * @return
     */
    public static List<Date> getTimeInterval(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // System.out.println("要计算日期为:" + sdf.format(cal.getTime())); // 输出要计算日期
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        Date w1 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w2 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w3 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w4 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w5 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w6 = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date w7 = cal.getTime();
        List<Date> list = new ArrayList<>();
        list.add(w1);
        list.add(w2);
        list.add(w3);
        list.add(w4);
        list.add(w5);
        list.add(w6);
        list.add(w7);
       /* String imptimeBegin = sdf.format(cal.getTime());
        // System.out.println("所在周星期一的日期：" + imptimeBegin);
        cal.add(Calendar.DATE, 6);
        String imptimeEnd = sdf.format(cal.getTime());
        // System.out.println("所在周星期日的日期：" + imptimeEnd);
        return imptimeBegin + "," + imptimeEnd;*/
        return list;
    }

    public static List<Date> getBefore7DayDate(Date date) {
        List<Date> list = new ArrayList<>();
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-6));
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-5));
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-4));
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-3));
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-2));
        list.add(org.apache.commons.lang3.time.DateUtils.addDays(date,-1));
        list.add(date);
        return list;
    }

    public static Date getMonday(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // System.out.println("要计算日期为:" + sdf.format(cal.getTime())); // 输出要计算日期
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        Date w1 = cal.getTime();
        return w1;
    }

    public static Integer diffMinute(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return null;
        }
        return (int) (date1.getTime() - date2.getTime()) / (1000 * 60);
    }

    public static Integer diffSeconds(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return null;
        }
        return (int) (date1.getTime() - date2.getTime()) / (1000);
    }

    /**
     * 获取几天前数据
     * @param d
     * @param day
     * @return
     */
    public static Date getDateBefore(Date d,int day) {
        Calendar now =Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE,now.get(Calendar.DATE)-day);
        return now.getTime();
    }

    /**
     * 获得入参日期所在月的第一天凌晨
     * @param date
     * @return
     */
    public static Date getMonthFirstDay(Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 0);
        // 设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND,0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获得上月的第一天凌晨
     * @param date
     * @return
     */
    public static Date getLastMonthFirstDay(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        // 设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND,0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 流量预警，获取当月起始时间。 不通用
     * @return
     */
    public static Date getStartDay(Integer dictValue){
        Date date = new Date();
        //date = org.apache.commons.lang3.time.DateUtils.parseDate("2023")
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (dictValue == null) {
            // 设置为1号,当前日期既为本月第一天
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, 1);
        } else if (dictValue.intValue() > 0){
            // 设置为1号,当前日期既为本月第一天
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, dictValue);
        } else {
            int abs = Math.abs(dictValue);
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, abs);
            if (new Date().getTime() < c.getTime().getTime()) {
                // 如果是自27号算起。那么当前时间如果小于27号，则起始时间为上月27号
                // 如果当前时间大于27号，则起始时间为本月27号
                c.add(Calendar.MONTH, -1);
            }
        }
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND,0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 流量预警，获取当月起始时间。 不通用
     * @return
     */
    public static String getCurrentMonth(Integer dictValue){
        Date now = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(now);
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND,0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        if (dictValue == null) {
            return DateFormatUtils.format(now, "yyyy-MM");
        } else if (dictValue.intValue() > 0){
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, dictValue);
            if (now.getTime() < c.getTime().getTime()) {
                // 如果当前时间，小于配置时间，则是上月，否则是本月
                c.add(Calendar.MONTH, -1);
                c.set(Calendar.DAY_OF_MONTH, 0);
                return DateFormatUtils.format(c.getTime(), "yyyy-MM");
            } else {
                return DateFormatUtils.format(now, "yyyy-MM");
            }
        } else {
            int abs = Math.abs(dictValue);
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, abs);
            if (now.getTime() > c.getTime().getTime()) {
                // 如果是自27号算起。如果当前时间大于27号，则为下月数据
                c.add(Calendar.MONTH, 1);
                return DateFormatUtils.format(c.getTime(), "yyyy-MM");
            }
            return DateFormatUtils.format(now, "yyyy-MM");
        }
    }

    /**
     * 获得入参日期凌晨
     * @param date
     * @return
     */
    public static Date getDawn(Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND,0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获得入参日期
     * @param date
     * @return
     */
    public static Date getDayEnd(Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND,59);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }
}
