package com.jetron.nb.common.util;

import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 *
 * Date: 2019-04-12 14:38
 * Version: 1.0.0
 */
@Slf4j
public class HostUtils {
    private static String localIp;

    public static String getLocalIp() {
        if (localIp != null) {
            return localIp;
        }

        Enumeration<NetworkInterface> netInterfaces;
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            label : {
                while (netInterfaces.hasMoreElements()) {
                    NetworkInterface netInterface = netInterfaces.nextElement();
                    Enumeration<InetAddress> inetAddresses = netInterface.getInetAddresses();
                    while (inetAddresses.hasMoreElements()) {
                        InetAddress inetAddress = inetAddresses.nextElement();
                        //非本地环回接口 && IPV4
                        if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                            localIp = inetAddress.getHostAddress();
                            break label;
                        }
                    }
                }
            }

        } catch (SocketException e) {
            log.error("Get local ip failed", e);
            throw new RuntimeException("Get local ip failed");
        }

        return localIp == null ? "127.0.0.1" : localIp;
    }
}
