package com.jetron.nb.common.util;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.dal.po.AcsUser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

public class MdcUtils {
    private static final String CUR_ACS_USER = "currentAcsUser";
    private static final String DETAIL_LOG = "detailLog";
    private static final String LOG_TYPE = "logType";

    private static void put(String key, Object value) {
        if (value != null) {
            String val = value.toString();
            if (StringUtils.isNoneBlank(key, val)) {
                MDC.put(key, val);
            }
        }
    }

    public static void putCurrentAcsUser(String acsUser) {
        MDC.put(CUR_ACS_USER, acsUser);
    }

    public static AcsUser getCurrentAcsUser() {
        String acsUser = MDC.get(CUR_ACS_USER);
        return StringUtils.isBlank(acsUser) ? null : JSON.parseObject(acsUser, AcsUser.class);
    }

    public static boolean isAdmin() {
        String acsUser = MDC.get(CUR_ACS_USER);
        AcsUser user = StringUtils.isBlank(acsUser) ? null : JSON.parseObject(acsUser, AcsUser.class);
        return user != null && user.getRole() == RoleEnum.SUPER_ADMIN.role;
    }

    public static void putLogType(LogType logType) {
        put(LOG_TYPE, logType.type);
    }

    public static Integer getLogType() {
        String tmp = MDC.get(LOG_TYPE);
        if (StringUtils.isNoneBlank(tmp)) {
            return Integer.valueOf(tmp);
        }
        return null;
    }

    public static void appendDetailLog(String log) {
        String existLog = MDC.get(DETAIL_LOG);
        if (StringUtils.isNoneBlank(existLog)) {
            existLog += "," + log;
        } else {
            existLog = log;
        }

        MDC.put(DETAIL_LOG, existLog);
    }

    public static String getDetailLog() {
        return MDC.get(DETAIL_LOG);
    }

    public static void clear() {
        MDC.clear();
    }
}
