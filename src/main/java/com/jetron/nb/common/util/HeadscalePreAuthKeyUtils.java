package com.jetron.nb.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Headscale PreAuth Key Redis 管理工具类
 */
@Component
@Slf4j
public class HeadscalePreAuthKeyUtils {

    @Resource
    private RedisTemplate<String, String> redisTemplateString;

    /**
     * 生成 Redis Key
     * 格式: username + "-headscale"
     * 
     * @param username 用户名
     * @return Redis Key
     */
    public static String generateRedisKey(String username) {
        if (StringUtils.isBlank(username)) {
            throw new IllegalArgumentException("Username cannot be blank");
        }
        return username + "-headscale";
    }

    /**
     * 存储 PreAuth Key 到 Redis
     * 
     * @param username 用户名
     * @param preAuthKey PreAuth Key 值
     * @param expirationHours 过期时间（小时）
     */
    public void storePreAuthKey(String username, String preAuthKey, int expirationHours) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(preAuthKey)) {
            throw new IllegalArgumentException("Username and preAuthKey cannot be blank");
        }
        
        String redisKey = generateRedisKey(username);
        redisTemplateString.opsForValue().set(redisKey, preAuthKey, expirationHours, TimeUnit.HOURS);
        
        log.info("Stored PreAuth Key to Redis: username={}, redisKey={}, expiration={}h", 
                username, redisKey, expirationHours);
    }

    /**
     * 获取 PreAuth Key
     * 
     * @param username 用户名
     * @return PreAuth Key，如果不存在返回 null
     */
    public String getPreAuthKey(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }
        
        String redisKey = generateRedisKey(username);
        String preAuthKey = redisTemplateString.opsForValue().get(redisKey);
        
        log.debug("Retrieved PreAuth Key from Redis: username={}, redisKey={}, found={}", 
                username, redisKey, preAuthKey != null);
        
        return preAuthKey;
    }

    /**
     * 删除 PreAuth Key
     * 
     * @param username 用户名
     * @return 是否删除成功
     */
    public boolean deletePreAuthKey(String username) {
        if (StringUtils.isBlank(username)) {
            return false;
        }
        
        String redisKey = generateRedisKey(username);
        Boolean deleted = redisTemplateString.delete(redisKey);
        
        log.info("Deleted PreAuth Key from Redis: username={}, redisKey={}, deleted={}", 
                username, redisKey, deleted);
        
        return Boolean.TRUE.equals(deleted);
    }

    /**
     * 检查 PreAuth Key 是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    public boolean hasPreAuthKey(String username) {
        if (StringUtils.isBlank(username)) {
            return false;
        }
        
        String redisKey = generateRedisKey(username);
        Boolean exists = redisTemplateString.hasKey(redisKey);
        
        return Boolean.TRUE.equals(exists);
    }

    /**
     * 获取 PreAuth Key 的剩余过期时间
     * 
     * @param username 用户名
     * @return 剩余过期时间（秒），-1 表示永不过期，-2 表示 key 不存在
     */
    public long getPreAuthKeyTTL(String username) {
        if (StringUtils.isBlank(username)) {
            return -2;
        }
        
        String redisKey = generateRedisKey(username);
        Long ttl = redisTemplateString.getExpire(redisKey, TimeUnit.SECONDS);
        
        return ttl != null ? ttl : -2;
    }

    /**
     * 更新 PreAuth Key 的过期时间
     * 
     * @param username 用户名
     * @param expirationHours 新的过期时间（小时）
     * @return 是否更新成功
     */
    public boolean updatePreAuthKeyExpiration(String username, int expirationHours) {
        if (StringUtils.isBlank(username)) {
            return false;
        }
        
        String redisKey = generateRedisKey(username);
        Boolean updated = redisTemplateString.expire(redisKey, expirationHours, TimeUnit.HOURS);
        
        log.info("Updated PreAuth Key expiration: username={}, redisKey={}, expiration={}h, updated={}", 
                username, redisKey, expirationHours, updated);
        
        return Boolean.TRUE.equals(updated);
    }

    /**
     * 批量删除 PreAuth Key（根据用户名模式）
     * 注意：这个方法会扫描所有匹配的 key，在生产环境中谨慎使用
     * 
     * @param usernamePattern 用户名模式，如 "test*"
     * @return 删除的 key 数量
     */
    public int deletePreAuthKeysByPattern(String usernamePattern) {
        if (StringUtils.isBlank(usernamePattern)) {
            return 0;
        }
        
        String redisKeyPattern = usernamePattern + "-headscale";
        Set<String> keys = redisTemplateString.keys(redisKeyPattern);
        
        if (keys != null && !keys.isEmpty()) {
            Long deleted = redisTemplateString.delete(keys);
            int deletedCount = deleted != null ? deleted.intValue() : 0;
            
            log.info("Batch deleted PreAuth Keys: pattern={}, deletedCount={}", 
                    redisKeyPattern, deletedCount);
            
            return deletedCount;
        }
        
        return 0;
    }

    /**
     * 获取所有 PreAuth Key 的统计信息
     * 注意：这个方法会扫描所有 headscale 相关的 key，在生产环境中谨慎使用
     * 
     * @return 统计信息字符串
     */
    public String getPreAuthKeyStatistics() {
        String pattern = "*-headscale";
        Set<String> keys = redisTemplateString.keys(pattern);
        
        if (keys == null || keys.isEmpty()) {
            return "No PreAuth Keys found in Redis";
        }
        
        int totalKeys = keys.size();
        int expiredKeys = 0;
        int validKeys = 0;
        
        for (String key : keys) {
            Long ttl = redisTemplateString.getExpire(key, TimeUnit.SECONDS);
            if (ttl != null) {
                if (ttl == -2) {
                    expiredKeys++;
                } else {
                    validKeys++;
                }
            }
        }
        
        return String.format("PreAuth Key Statistics: Total=%d, Valid=%d, Expired=%d", 
                totalKeys, validKeys, expiredKeys);
    }
}
