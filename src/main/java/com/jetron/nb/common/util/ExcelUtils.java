package com.jetron.nb.common.util;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.common.exception.IotException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

@Slf4j
public class ExcelUtils {

    private static final short COLUMN_WIDTH = (short) (37.5 * 200);

    private static final String EXCEL_2003 = ".xls";

    private static final String EXCEL_2007 = ".xlsx";

    /**
     * 默认起始行
     */
    private static final Integer DEFAULT_START_ROW = 1;

    /**
     * 导出excel默认title行index
     */
    private static final Integer DEFAULT_TITLE_INDEX = 0;

    /**
     * 带注意事项的title行index
     */
    private static final Integer WITH_ATTENTIONS_TITLE_INDEX = 1;

    /**
     * 注意事项的行index
     */
    private static final Integer ATTENTIONS_INDEX = 0;

    private static final String SHEET_NAME = "Sheet0";

    private static final short ATTENTIONS_ROW_HEIGHT = 3500;

    /**
     * 最多可导出行数限制
     */
    private static final Integer MAX_EXPORT_NUM = 10000;

    /**
     * 获取导入的数据，使用自定义起始行
     * @param is       输入流
     * @param maxCell  最大列 从0开始
     * @param startRow 开始列 从0开始
     * @return excel数据列表
     * @throws IOException 文件不存在
     */
    public static List<List<Object>> getImportData(InputStream is,int sheetNum, Integer maxCell, Integer startRow) throws IOException {
        try {
            XSSFWorkbook xssfWorkbook  = new XSSFWorkbook(is);
            XSSFSheet sheet = xssfWorkbook.getSheetAt(sheetNum);
            int flag;
            Object cellVal;
            List<Object> li;
            List<List<Object>> result = new ArrayList<>();
            for (int j = startRow; j <= sheet.getLastRowNum(); j++) {
                XSSFRow row = sheet.getRow(j);
                if (row == null || row.getFirstCellNum() > maxCell) continue;
                li = new ArrayList<>();
                flag = 0;
                for (int k = 0; k <= maxCell; k++) {
                    XSSFCell cell = row.getCell(k);
                    cellVal = ExcelUtils.getCellValue(cell);
                    // 判断是否所有行都为空
                    if (StringUtils.isBlank((String)cellVal)) {
                        flag++;
                    }
                    // 检查Null数据
                    if (cellVal == null) {
                        cellVal = "";
                    }
                    li.add(cellVal);
                }
                if (!li.isEmpty()) {
                    if (flag <= maxCell) {
                        result.add(li);
                    }
                }
            }
            if (result.size() > 10000) {
                throw new RuntimeException("已超出系统导入【10000】条数量限制，请分批次导入");
            }
            return result;
        } finally {
            if (is != null) is.close();
        }
    }


    public static List<List<Object>> getImportData(InputStream is,int sheetNum) throws IOException {
        List<List<Object>> data = new ArrayList<>();
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(is);
        XSSFSheet sheet = xssfWorkbook.getSheetAt(sheetNum);
        int maxRow = sheet.getLastRowNum();
        int colStart = 0;
        for (int row = 0; row <= maxRow; row++) {
            List<Object> rowData = new ArrayList<>();
            int maxRol = sheet.getRow(row).getLastCellNum();
            int nullNum = 0;
            for (int col = colStart; col < maxRol; col++) {
                XSSFCell cell = sheet.getRow(row).getCell(col);
                Object value = getCellValue(cell);
                rowData.add(value);
                if (value == null) {
                    ++nullNum;
                }
            }
            if (nullNum == rowData.size()) {
                // 遇到空行，视为结束
                break;
            }
            data.add(rowData);
        }
        return data;
    }

    /**
     * 将内容写进EXCEL
     *
     * @param data  要写的数据
     * @param keys  字段的主键
     * @param heads 表头
     * @return excel
     */
    public static Workbook writeExcel(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions) {
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        for (int i = 0; i < heads.length; i++)
            sheet.setColumnWidth(i, COLUMN_WIDTH);

        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyle1 = wb.createCellStyle();
        CellStyle cellStyle2 = wb.createCellStyle();

        Font font1 = wb.createFont();
        Font font2 = wb.createFont();

        font1.setColor(IndexedColors.BLACK.getIndex());
        font1.setFontHeightInPoints((short) 12);
        font1.setBold(true);

        font2.setColor(IndexedColors.BLACK.getIndex());
        font2.setFontHeightInPoints((short) 10);

        cellStyle1.setFont(font1);
        // 边框
        cellStyle1.setBorderLeft(BorderStyle.THICK);
        cellStyle1.setBorderRight(BorderStyle.THICK);
        cellStyle1.setBorderTop(BorderStyle.THICK);
        cellStyle1.setBorderBottom(BorderStyle.THICK);
        // 居中
        cellStyle1.setAlignment(HorizontalAlignment.CENTER);
        cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle1.setFillForegroundColor(IndexedColors.YELLOW1.getIndex());
        cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        cellStyle2.setFont(font2);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);

        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);

        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            cell.setCellStyle(cellStyle1);
        }

        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < keys.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, data.get(i).get(keys[j]));
                cell.setCellStyle(cellStyle2);
            }
        }
        return wb;
    }



    /**
     * 浏览器导出Excel
     *
     * @param response http响应
     * @param request  http请求
     * @param dataMap  导出数据的map格式
     * @param keys     字段名
     * @param heads    显示的title
     * @param fileName 导出文件名
     * @throws IOException 写excel错误
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new IOException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new IOException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcel(dataMap, keys, heads, null); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }


    /**
     * 带注意事项的浏览器导出Excel
     *
     * @param response   响应结果
     * @param request    响应结果
     * @param dataMap    导出数据
     * @param keys       字段名
     * @param heads      表头名
     * @param fileName   导出文件名
     * @param attentions 注意事项
     * @throws IOException 写excel错误
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName, String attentions) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new IOException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new IOException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcel(dataMap, keys, heads, attentions); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }

    /**
     * 自定义模版的导出
     *
     * @param response 响应结果
     * @param request  响应结果
     * @param data     导出数据
     * @param keys     字段名
     * @param fileName 导出文件名
     * @param is       模版文件输入流
     * @throws IOException 模版文件不存在
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> data, String[] keys,
                                   String fileName, InputStream is) throws IOException {
        exportExcel(response, request, data, keys, fileName, is, DEFAULT_START_ROW);
    }

    /**
     * 自定义模版的导出，若字符有为数字，导出类型则自动转换为数字
     *
     * @param response 响应结果
     * @param request  响应结果
     * @param data     导出数据
     * @param keys     字段名
     * @param fileName 导出文件名
     * @param is       模版文件输入流
     * @param hs       模板预留的行数
     * @throws IOException 模版文件不存在
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> data, String[] keys,
                                   String fileName, InputStream is, Integer hs) throws IOException {
        Workbook wb = null;
        OutputStream out = null;
        try {
            wb = getWorkbook(fileName, is);
            Sheet sheet = wb.getSheet(SHEET_NAME);
            if (sheet == null)
                throw new IOException("请检查[Sheet0]是否存在");
            // 写表体内容  i=1,此为从数据的第1行开始取值，实际数据为从第0行开始，会丢数据；sheet.createRow：开始写数据的行数
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i + hs);
                for (int j = 0; j < keys.length; j++) {

                    Cell cell = row.createCell(j);
                    //判断值是否为数字，若为数字则转换
                    if (data.get(i).get(keys[j]) != null && !("").equals(data.get(i).get(keys[j]))) {
                        boolean a = isNumeric(data.get(i).get(keys[j]).toString());
                        //System.out.println(a+"      存储值类型"+i+"  "+j);
                        if (a) {
                            cell.setCellValue(Double.parseDouble(data.get(i).get(keys[j]).toString()));
                        } else {
                            cell.setCellValue(data.get(i).get(keys[j]).toString());
                        }
                    } else {
                        cell.setCellValue("");
                    }

                }
            }
            // 导出
            out = response.getOutputStream();
            response.reset();
            // 定义浏览器响应表头，顺带定义下载名，比如students
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
            }
            // 定义下载的类型，标明是excel文件
            response.setContentType("application/vnd.ms-excel");
            // 这时候把创建好的excel写入到输出流
            wb.write(out);
            out.flush();
        } finally {
            if (out != null) out.close();
            if (wb != null) wb.close();
            if (is != null) is.close();

        }
    }

    /**
     * 正则 判断字符类型值是否为数字,包含小数、负数
     *
     * @param str 数字类型字符串
     * @return 是否为数字类型
     */
    public static boolean isNumeric(String str) {
        return str.matches("-?[0-9]+(\\.[0-9]+)?");
    }

    /**
     * 将bean List装换维Map List
     *
     * @param beans 要导出的pojo数据
     * @return 导出数据的<code>List<Map></code>格式
     */
    public static List<Map<String, Object>> getBeanMaps(List<?> beans) throws IllegalAccessException,
            IntrospectionException, InvocationTargetException {
        List<Map<String, Object>> dataMaps = new ArrayList<>();
        for (Object bean : beans) {
            dataMaps.add(convertBean(bean));
        }
        return dataMaps;
    }

    public static List<Map<String, Object>> getBeanMaps(List<?> beans, String pattern) throws IllegalAccessException,
            IntrospectionException, InvocationTargetException {
        List<Map<String, Object>> dataMaps = new ArrayList<>();
        for (Object bean : beans) {
            dataMaps.add(convertBean(bean, pattern));
        }
        return dataMaps;
    }

    /**
     * 将bean转换为Map
     *
     * @param bean pojo对象
     * @return pojo的map类型
     * @throws IntrospectionException    e
     * @throws InvocationTargetException e
     * @throws IllegalAccessException    e
     */
    public static <T> Map<String, Object> convertBean(T bean) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return convertBean(bean, sdf);
    }

    public static <T> Map<String, Object> convertBean(T bean, String pattern) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return convertBean(bean, sdf);
    }

    /**
     * @param bean pojo对象
     * @param sdf  日期格式
     * @return
     * @throws IntrospectionException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static <T> Map<String, Object> convertBean(T bean, SimpleDateFormat sdf) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        Map<String, Object> dataMap = new HashMap<>();
        Class type = bean.getClass();
        BeanInfo beanInfo = Introspector.getBeanInfo(type);
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        PropertyDescriptor descriptor;
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            descriptor = propertyDescriptor;
            String propertyName = descriptor.getName();
            if (!"class".equals(propertyName)) {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean);
                if (result != null) {
                    // 时间格式单独处理
                    if (result instanceof Date) {
                        result = sdf.format(result);
                    }
                    dataMap.put(propertyName, result);
                } else {
                    dataMap.put(propertyName, "");
                }
            }
        }
        return dataMap;
    }

    /**
     * 判断是否为低版本浏览器
     *
     * @param request 浏览器请求头
     * @return
     */
    public static boolean isLowVersionBrowser(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent.contains("msie")
                || userAgent.contains("Trident")
                || userAgent.contains("Edge");
    }



    /**
     * 通过文件名创建工作簿
     *
     * @param fileName 文件名
     * @param is       模版excel输入流
     * @return
     * @throws IOException
     */
    public static Workbook getWorkbook(String fileName, InputStream is) throws IOException {
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        if (!EXCEL_2003.equals(suffixName) && !EXCEL_2007.equals(suffixName)) {
            throw new IllegalArgumentException("请上传excel文件。");
        }
        Workbook wb;
        if (EXCEL_2003.equals(suffixName)) {
            wb = new HSSFWorkbook(is);
        } else {
            wb = new XSSFWorkbook(is);
        }
        return wb;
    }

    /**
     * 文本单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getTextStyle(Workbook workbook) {
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        textStyle.setFont(getNormalFont(workbook)); //字体
        textStyle.setBorderBottom(BorderStyle.THIN); //下边框
        textStyle.setBorderLeft(BorderStyle.THIN); //左边框
        textStyle.setBorderTop(BorderStyle.THIN); //上边框
        textStyle.setBorderRight(BorderStyle.THIN); //右边框
        DataFormat format = workbook.createDataFormat();
        textStyle.setDataFormat(format.getFormat("@")); //文本格式
        return textStyle;
    }

    /**
     * 整数单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getIntegerStyle(Workbook workbook) {
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        integerStyle.setFont(getNormalFont(workbook)); //字体
        integerStyle.setBorderBottom(BorderStyle.THIN); //下边框
        integerStyle.setBorderLeft(BorderStyle.THIN); //左边框
        integerStyle.setBorderTop(BorderStyle.THIN); //上边框
        integerStyle.setBorderRight(BorderStyle.THIN); //右边框
        integerStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,#0")); //数据格式只显示整数
        return integerStyle;
    }

    /**
     * 整数单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getFloatStyle(Workbook workbook) {
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        integerStyle.setFont(getNormalFont(workbook)); //字体
        integerStyle.setBorderBottom(BorderStyle.THIN); //下边框
        integerStyle.setBorderLeft(BorderStyle.THIN); //左边框
        integerStyle.setBorderTop(BorderStyle.THIN); //上边框
        integerStyle.setBorderRight(BorderStyle.THIN); //右边框
        DataFormat format = workbook.createDataFormat();
        integerStyle.setDataFormat(format.getFormat("0.00")); //两位小数
        return integerStyle;
    }

    /**
     * 普通字体
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static Font getNormalFont(Workbook workbook) {
        Font font = workbook.createFont();
        font.setBold(true);
        return font;
    }


    /**
     * 填写单元格值
     * TODO 日历类型和富文本类型支持
     *
     * @param cell  单元格
     * @param value 值
     */
    private static void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(Double.parseDouble(value.toString()));
        } else if (value instanceof Date) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            cell.setCellValue(simpleDateFormat.format(value));
        } else if (value instanceof LocalDate) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            cell.setCellValue(simpleDateFormat.format(value));
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    public static Object getCellValue(XSSFCell cell) {
        if (cell == null) {
            return null;
        }
        if (CellType.FORMULA.getCode() == cell.getCellType()) {
            String rawValue = cell.getRawValue();
            if (StringUtils.isBlank(rawValue) && rawValue.contains("#")) {
                return null;
            }
            return "#VALUE!".equals(rawValue) || "#N/A".equals(rawValue) || "#NAME?".equals(rawValue) || StringUtils.isBlank(rawValue) ? null : rawValue;
        } else if (CellType.NUMERIC.getCode() == cell.getCellType()) {
            if (DateUtil.isCellDateFormatted(cell)) {
                Date date = cell.getDateCellValue();
                return DateFormatUtils.format(date,"yyyy-MM-dd");
            }
            String value = cell.getRawValue();
             if (value != null && value.lastIndexOf(".0") > 0) {
                value = value.substring(0,value.lastIndexOf(".0"));
            }
            return value;
        } else {
            int cellType = cell.getCellType();
            String value = cell.toString();
            if (HSSFCell.CELL_TYPE_FORMULA == cell.getCellType()) {
                try {
                    value = String.valueOf(cell.getNumericCellValue());
                } catch (IllegalStateException e) {
                    value = String.valueOf(cell.getRichStringCellValue());
                }
            }
            value = StringUtils.isBlank(value) ? null : value;
            /*if (value != null && value.lastIndexOf(".0") > 0) {
                value = value.substring(0,value.lastIndexOf(".0"));
            }*/
            return value;
        }
    }
}
