package com.jetron.nb.common.util;

import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class PasswdUtils {

    public static String getRandomChar(int length) {
        char[] chr = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
        ;
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int idx = ThreadLocalRandom.current().nextInt(chr.length);
            buffer.append(chr[idx]);
        }
        return buffer.toString();
    }

    public static void main(String[] args) {
        System.out.println(getRandomChar(8));
    }
}
