package com.jetron.nb.common.util;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.vo.Result;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

public class ResultUtils {


    public static boolean isSuccess(Result result){
        return result.getCode() == ApiCode.SUCCESS.code;
    }

    public static boolean isFail(Result result){
        return result.getCode() == ApiCode.FAIL.code;
    }

    public static boolean isError(Result result){
        return result.getCode() == ApiCode.SERVER_ERROR.code;
    }

    public static Result success() {
        return Result.toResult(ApiCode.SUCCESS);
    }

    public static Result success(Object data) {
        return Result.toResult(ApiCode.SUCCESS,data);
    }

    public static Result fail() {
        return Result.toResult(ApiCode.FAIL);
    }

    public static Result error() {
        return Result.toResult(ApiCode.SERVER_ERROR);
    }

    public static Result error(Object data) {
        return Result.toResult(ApiCode.SERVER_ERROR,data);
    }

    public static Result error(ApiCode code, Object data) {
        return Result.toResult(code,data);
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class newResult extends com.jetron.nb.common.vo.Result implements Serializable {
        private static final long serialVersionUID = -3046202914042439811L;

        private int code;
        private String message;
        private Object data;
        private String rightMsg;

        public newResult(ApiCode apiCode, Object data) {
            super(apiCode,data);
            this.code = apiCode.code;
            this.message = apiCode.message;
            this.data = data;
        }

        public static newResult toResult(ApiCode apiCode) {
            return toResult(apiCode, null);
        }
        public static newResult toResult(ApiCode apiCode, Object data) {
            return new newResult(apiCode, data);
        }
        public static newResult toResult(ApiCode apiCode,String msg) {
            return new newResult(apiCode, null).setMessage(msg);
        }

        public static newResult toResult(ApiCode apiCode,String msg, Object data) {
            return new newResult(apiCode, data).setMessage(msg);
        }

        public static newResult rightMsg(ApiCode apiCode, Object data,String rightMsg) {
            return new newResult(apiCode, data).setRightMsg(rightMsg);
        }

        public static newResult fail(String msg) {
            return toResult(ApiCode.FAIL,msg);
        }

    }
}
