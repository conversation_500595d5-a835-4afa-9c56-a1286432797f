package com.jetron.nb.biz.task;

import com.jetron.nb.biz.service.AcsRealDataService;
import com.jetron.nb.biz.service.data.DataCache;
import com.jetron.nb.common.util.DateUtils;
import com.jetron.nb.dal.dao.AcsCaptureDataMapper;
import com.jetron.nb.dal.dao.mqtt.AcsCaptureAlarmMapper;
import com.jetron.nb.dal.po.AcsCaptureAlarm;
import com.jetron.nb.dal.po.AcsCaptureData;
import com.jetron.nb.web.common.config.BeanContextConfig;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class AcsCaptureDataRunner implements Runnable {
    List<AcsCaptureData> acsCaptureDataList;

    public void setAcsCaptureDataList(@NotNull List<AcsCaptureData> acsCaptureDataList) {
        this.acsCaptureDataList = new ArrayList<>();
        this.acsCaptureDataList = acsCaptureDataList;
    }

    @Override
    public void run() {
        if (acsCaptureDataList.size() > 0) {
            insertRealData();
        }
    }

    public void insertRealData() {
        if (this.acsCaptureDataList != null) {
            DataCache.dataQueue.addAll(acsCaptureDataList);
        }
        Date now = new Date();
        if (DataCache.alarmsQueue.size() > 100 || DataCache.dataQueue.size() > 100 || DateUtils.diffMinute(now, DataCache.flag) > 1) {
            QueueToMySql();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void QueueToMySql() {
        List<AcsCaptureAlarm> alarmLists = new ArrayList<>();
        List<AcsCaptureData> dataLists = new ArrayList<>();
        AcsCaptureAlarmMapper acsCaptureAlarmMapper = BeanContextConfig.getApplicationContext()
                .getBean(AcsCaptureAlarmMapper.class);
        AcsCaptureDataMapper acsCaptureDataMapper = BeanContextConfig.getApplicationContext()
                .getBean(AcsCaptureDataMapper.class);
        AcsRealDataService realDataService = BeanContextConfig.getApplicationContext()
                .getBean(AcsRealDataService.class);
        Lock lock=new ReentrantLock();
        lock.lock();
        try {
            while (!DataCache.alarmsQueue.isEmpty()) {
                alarmLists.add(DataCache.alarmsQueue.poll());
            }
            while (!DataCache.dataQueue.isEmpty()) {
                dataLists.add((DataCache.dataQueue.poll()));
            }
            // 更新时间点
            DataCache.flag = new Date();
        } catch (Exception ignored) {

        } finally {
            lock.unlock();
        }
        if (alarmLists.size() > 0) {
            acsCaptureAlarmMapper.insertList(alarmLists);
        }

        if (dataLists.size() > 0) {
            realDataService.updateRealData(dataLists);
            acsCaptureDataMapper.insertList(dataLists);
        }
    }
}
