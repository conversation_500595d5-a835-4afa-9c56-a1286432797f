package com.jetron.nb.biz.task;

import com.jetron.nb.common.util.CommandUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class AppStartupRunner implements CommandLineRunner {
    protected final Logger logger = LoggerFactory.getLogger(AppStartupRunner.class);

    @Override
    public void run(String... args) throws Exception {
        logger.info("AppStartupRunner run method Started !!");
    }
}
