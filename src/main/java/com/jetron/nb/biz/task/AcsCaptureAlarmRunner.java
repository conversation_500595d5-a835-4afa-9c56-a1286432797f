package com.jetron.nb.biz.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.service.AcsDictionaryService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.ThreadPoolService;
import com.jetron.nb.biz.service.data.DataCache;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.RedisKey;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.mqtt.AcsCaptureAlarmMapper;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.web.common.config.BeanContextConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

public class AcsCaptureAlarmRunner implements Runnable {
    List<AcsCaptureAlarm> allList;
    List<AcsCaptureAlarm> acsCaptureAlarmList;
    String sn;

    public void setAcsCaptureAlarmList(@NotNull List<AcsCaptureAlarm> acsCaptureAlarmList) {
        this.acsCaptureAlarmList = new ArrayList<>();
        this.acsCaptureAlarmList = acsCaptureAlarmList;
    }
    public void setAllList(@NotNull List<AcsCaptureAlarm> allList) {
        this.allList = new ArrayList<>();
        this.allList = allList;
    }

    public void setSn(@NotNull String sn) {
        this.sn = sn;
    }

    @Override
    public void run() {
        operatorAlarm();
    }

    @Transactional(rollbackFor = Exception.class)
    public void operatorAlarm() {
        // 清除原先报警信息
        AcsCaptureAlarmMapper acsCaptureAlarmMapper = BeanContextConfig.getApplicationContext()
                .getBean(AcsCaptureAlarmMapper.class);

        // 获取是否自动清除IP报警
        boolean autoClearIpAlarm = false;
        AcsDictionaryService dictionaryService = BeanContextConfig.getApplicationContext()
                .getBean(AcsDictionaryService.class);
        List<AcsDictionary> byKey = dictionaryService.findByKey(Constants.IP_ALARM_AUTO_DICT);
        if (!CollectionUtils.isEmpty(byKey)) {
            String dicContent = byKey.get(0).getDicContent();
            if (Constants.YES.equals(dicContent)) {
                autoClearIpAlarm = true;
            }
        }

        if (acsCaptureAlarmList.size() > 0) {
            // 新增报警信息
            insertRealData();
        }

         /*
         *   考虑到异常数据较多，暂时注释以下自动清除IP功能。逻辑代码待优化
         *   大和客户需求。其它客户没有这个自动清理IP的需求
         */
        AcsAlarmFilter alarmParam = new AcsAlarmFilter();
        alarmParam.setSnEq(this.sn);
        alarmParam.setAlarmStatus(Constants.ALARM_STATUS1);
        List<AcsCaptureAlarm> oldAlarmList = acsCaptureAlarmMapper.findList(alarmParam);
        List<Integer> ids = null;
        List<String> paramterCodeList = this.allList.stream().map(AcsCaptureAlarm::getParameterCode).collect(Collectors.toList());

        if (!autoClearIpAlarm) {
            // 如果是手动清除，则忽略所有IP历史报警
            ids = oldAlarmList.stream().filter(obj ->
                    !Constants.AlarmCode.IP.equalsIgnoreCase(obj.getParameterCode())
                && !paramterCodeList.contains(obj.getParameterCode()))
                    .map(AcsCaptureAlarm::getId).collect(Collectors.toList());
        } else {
            ids = oldAlarmList.stream().filter(obj -> !paramterCodeList.contains(obj.getParameterCode()))
                    .map(AcsCaptureAlarm::getId).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(ids)) {
            acsCaptureAlarmMapper.updateStatusList(Constants.ALARM_STATUS3,ids);
        }
        // 增加今日报警统计
        alarmToday();
    }


    /**
     *  数据插入统一在 AcsCaptureDataRunner 里执行，先将数据缓存到队列中
     */
    public void insertRealData() {
        if (this.acsCaptureAlarmList != null) {
            AcsUserDeviceService userDeviceService = BeanContextConfig.getApplicationContext()
                    .getBean(AcsUserDeviceService.class);

            List<String> snList = acsCaptureAlarmList.stream().map(AcsCaptureAlarm::getSn).distinct().collect(Collectors.toList());
            UserDeviceFilter filter = new UserDeviceFilter();
            filter.setSnList(snList);
            List<AcsUserDevice> deviceList = userDeviceService.findList(filter);
            deviceList = deviceList == null ? new ArrayList<>() : deviceList;
            Map<String, List<AcsUserDevice>> collect = deviceList.stream().collect(Collectors.groupingBy(AcsUserDevice::getSn));

            for (AcsCaptureAlarm alarm : this.acsCaptureAlarmList) {
                String sn = alarm.getSn();
                if (collect.containsKey(sn) && !CollectionUtils.isEmpty(collect.get(sn))) {
                    AcsUserDevice bySn = collect.get(sn).get(0);
                    alarm.setIp(bySn.getTrueIp());
                }
            }
            DataCache.alarmsQueue.addAll(acsCaptureAlarmList);

            /*AcsCaptureDataRunner acsCaptureDataRunner = new AcsCaptureDataRunner();
            ThreadPoolService.getInstance().execute(acsCaptureDataRunner);*/
        }
    }

    /**
     * 统计该sn 今日报警次数。用于大屏看板展示
     * 首次触发告警到恢复，算一次告警
     */
    public void alarmToday() {
        RedisServiceImpl redisService = BeanContextConfig.getApplicationContext().getBean(RedisServiceImpl.class);
        String temp1 = redisService.get(RedisKey.ALARM_STATUS.key + this.sn);
        JSONObject alarmStatus = JSONObject.parseObject(temp1);
        alarmStatus = alarmStatus == null ? new JSONObject() : alarmStatus;
        String format = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        // 现在报警的项
        List<String> paramterCodeList = this.allList.stream().map(AcsCaptureAlarm::getParameterCode).distinct().collect(Collectors.toList());
        for (int i = 0; i < paramterCodeList.size(); i++) {
            String paramterCode = paramterCodeList.get(i);
            if (alarmStatus.containsKey(paramterCode) && "1".equals(alarmStatus.getString(paramterCode))) {
                // 如果设备是报警状态。再次报警，则什么都不做
            } else if (!alarmStatus.containsKey(paramterCode) || "0".equals(alarmStatus.getString(paramterCode))) {
                // 如果设备不是报警状态，触发报警，则更改状态
                alarmStatus.put(paramterCode,"1");
                // 增加今日报警次数
                String alarmTodayKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, this.sn, paramterCode,format);
                String str = redisService.get(alarmTodayKey);
                if (StringUtils.isBlank(str)) {
                    redisService.set(alarmTodayKey,"1");
                    redisService.expire(alarmTodayKey,RedisKey.ALARM_TODAY.expire);
                } else {
                    Integer num = Integer.parseInt(str);
                    redisService.set(alarmTodayKey,String.valueOf(++num));
                }
            }
        }
        // 清除报警状态
        for (String paramterCode:alarmStatus.keySet()) {
            if (!paramterCodeList.contains(paramterCode) &&   "1".equals(alarmStatus.getString(paramterCode))) {
                alarmStatus.put(paramterCode,"0");
            }
        }

        redisService.set(RedisKey.ALARM_STATUS.key + this.sn,alarmStatus.toJSONString());
    }
}
