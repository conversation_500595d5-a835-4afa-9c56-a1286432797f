package com.jetron.nb.biz.task;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.biz.service.*;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.constant.RedisKey;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.AcsCaptureDataMapper;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.web.common.filter.OpenInterfaceFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Date: 2020-02-03
 * Version: 1.0.0
 */
@Slf4j
@Component
public class ScheduleTask {
    private static final Long SUCCESS = 1L;
    private static final String SYNC_LOCK = "sync";
    private static final String UPGRADE_LOCK = "upgrade";
    private static final String MYSQL_CLEANUP_LOCK = "cleanup";
    private static final String HEARTBEAT_LOCK = "heartbeat";
    private static final String MONTH_FLOW_LOCK = "monthflow";

    @Value("${upgrade.timeout}")
    private Integer timeout = 600;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsUserService acsUserService;
    @Autowired
    private MqttClientManager mqttClientManager;
    @Autowired
    private AcsConfigUpgradeService acsConfigUpgradeService;
    @Autowired
    private AcsFirmwareUpgradeService acsFirmwareUpgradeService;
    @Autowired
    private AcsLogService acsLogService;
    @Autowired
    private AcsTopicService acsTopicService;
    @Autowired
    private RedisServiceImpl redisService;
    @Autowired
    private AcsDeviceHeartService heartService;
    @Autowired
    private AcsParameterConfigService acsParameterConfigService;
    @Autowired
    private AcsAlarmService acsAlarmService;
    @Autowired
    private AcsCaptureDataMapper captureDataMapper;
    @Autowired
    private OpenInterfaceService openInterfaceService;
    @Autowired
    private AcsDeviceFlowService flowService;
    @Autowired
    private AcsDeviceFlowMonthService flowMonthService;

    @Scheduled(cron = "0 0/30 * * * ?")
    public void task() {
        try {
            if (getLock(SYNC_LOCK, "1", 10)) {
                // 同步sn列表
                synSnFromDb2Redis();
                // 解绑删除设备topic
                unsubscribeDeleteDevice();
                // 解绑删除用户topic
                unsubscribeDeleteUser();
            }
        } finally {
            releaseLock(SYNC_LOCK, "1");
        }
    }

    private void synSnFromDb2Redis() {
        SetOperations<String, String> operations = redisTemplate.opsForSet();

        DateTime end = DateTime.now();
        DateTime begin = end.minusDays(1).withTimeAtStartOfDay();
        List<AcsUserDevice> increasedDeviceList = acsUserDeviceService.getIncreasedDevice(begin.toDate(), end.toDate());
        for (AcsUserDevice device : increasedDeviceList) {
            operations.add(RedisKey.ALL_SN_SET.key, device.getSn());
        }

        List<AcsUserDevice> deletedDeviceList = acsUserDeviceService.getDeletedDevice(begin.toDate(), end.toDate());
        for (AcsUserDevice device : deletedDeviceList) {
            operations.remove(RedisKey.ALL_SN_SET.key, device.getSn());
        }
    }

    private void unsubscribeDeleteDevice() {
        DateTime end = DateTime.now();
        DateTime begin = end.minusDays(1).withTimeAtStartOfDay();

        List<AcsUserDevice> deletedDeviceList = acsUserDeviceService.getDeletedDevice(begin.toDate(), end.toDate());
        for (AcsUserDevice device : deletedDeviceList) {
            String topic = acsTopicService.getSubscribeDeviceTopic(device.getSn());
            mqttClientManager.unsubscribe(topic);
            String upgradeDeviceTopic = acsTopicService.getSubscribeUpgradeDeviceTopic(device.getSn());
            mqttClientManager.unsubscribe(upgradeDeviceTopic);
            log.info("[MqttMessageHandler#unsubscribeDeleteDevice] unsubscribe {}", topic);
        }
    }

    private void unsubscribeDeleteUser() {
        DateTime end = DateTime.now();
        DateTime begin = end.minusDays(1).withTimeAtStartOfDay();

        List<AcsUser> deletedUserList = acsUserService.getDeletedUser(begin.toDate(), end.toDate());
        for (AcsUser user : deletedUserList) {
            String topic = acsTopicService.getSubscribeUserTopic(user.getName());
            mqttClientManager.unsubscribe(topic);
            log.info("[MqttMessageHandler#unsubscribeDeleteUser] unsubscribe {}", topic);
        }
    }

    private boolean getLock(String key, String value, int expireTime) {
        boolean ret = false;
        try {
            String evalScript = new StringBuilder()
                    .append(" if redis.call('setnx', KEYS[1], ARGV[1])==1 then")
                    .append("   if redis.call('get', KEYS[1])==ARGV[1] then")
                    .append("       return redis.call('expire', KEYS[1], ARGV[2])")
                    .append("   else return 0")
                    .append("   end")
                    .append(" else return 0")
                    .append(" end").toString();
            RedisScript redisScript = new DefaultRedisScript(evalScript, Long.class);
            Object result = redisTemplate.execute(redisScript, Arrays.asList(key), String.valueOf(value), String.valueOf(expireTime));
            if (SUCCESS.equals(result)) {
                return true;
            } else {
                log.info("获取锁失败，key为：{}", key);
                return false;
            }
        } catch (Exception e) {
        }
        return ret;
    }

    private boolean releaseLock(String key, String value) {
        try {
            String evalScript = new StringBuilder()
                    .append("  if redis.call('get', KEYS[1]) == ARGV[1] then ")
                    .append("    return redis.call('del', KEYS[1])")
                    .append("  else return 0 ")
                    .append("  end").toString();
            RedisScript redisScript = new DefaultRedisScript(evalScript, Long.class);
            Object result = redisTemplate.execute(redisScript, Arrays.asList(key), value);
            if (SUCCESS.equals(result)) {
                return true;
            }
        } catch (Exception e) {
            log.error("{}", e);
        }
        return false;
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void upgradeStatusCheck() {
        try {
            if (getLock(UPGRADE_LOCK, "1", 60)) {
                checkConfigUpgrade();
                checkFirmwareUpgrade();
            }
        } finally {
            releaseLock(UPGRADE_LOCK, "1");
        }
    }

    @Transactional
    public void checkConfigUpgrade() {
        Date deadline = DateTime.now().minusSeconds(timeout).toDate();
        List<AcsConfigUpgrade> configUpgradeList = acsConfigUpgradeService.getTimeout(deadline);
        if (CollectionUtils.isEmpty(configUpgradeList)) {
            return;
        }

        List<Integer> deviceIds = configUpgradeList.stream().map(x -> x.getDeviceId())
                .distinct().collect(Collectors.toList());
        // 超时设置
        acsConfigUpgradeService.timeout(deviceIds);
        // 解除Device升级状态
        acsUserDeviceService.releaseUpgradeStatus(deviceIds);
        // 更新日志
        List<AcsUserDevice> deviceList = acsUserDeviceService.getByDeviceIds(deviceIds);
        for (AcsUserDevice device : deviceList) {
            acsLogService.appendLog(device, LogType.CONFIG_UPGRADE, "升级超时");
        }
    }

    @Transactional
    public void checkFirmwareUpgrade() {
        Date deadline = DateTime.now().minusSeconds(timeout).toDate();
        List<AcsFirmwareUpgrade> firmwareUpgradeList = acsFirmwareUpgradeService.getTimeout(deadline);
        if (CollectionUtils.isEmpty(firmwareUpgradeList)) {
            return;
        }

        List<Integer> deviceIds = firmwareUpgradeList.stream().map(x -> x.getDeviceId())
                .distinct().collect(Collectors.toList());
        // 超时设置
        acsFirmwareUpgradeService.timeout(deviceIds);
        // 解除Device升级状态
        acsUserDeviceService.releaseUpgradeStatus(deviceIds);
        // 更新日志
        List<AcsUserDevice> deviceList = acsUserDeviceService.getByDeviceIds(deviceIds);
        for (AcsUserDevice device : deviceList) {
            acsLogService.appendLog(device, LogType.FIRMWARE_UPGRADE, "升级超时");
        }
    }

    /**
     * 监控心跳
     */
    @Scheduled(cron = "0/30 * * * * ?")
    public void monitorHeart() {
        try {
            if (getLock(HEARTBEAT_LOCK, "1", 10)) {
                UserDeviceFilter filter = new UserDeviceFilter();
                filter.setOnline(Constants.NO_INT);
                List<AcsUserDevice> list = acsUserDeviceService.findListWithUser(filter);
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }

                List<AcsDeviceHeart> dataList = new ArrayList<>();
                List<AcsUserDevice> alarmList = new ArrayList<>();
                Date now = new Date();
                for (int i = 0; i < list.size(); i++) {
                    AcsUserDevice device = list.get(i);
                    String sn = list.get(i).getSn();
                    String jsonStr = redisService.get(AcsDeviceHeart.getHeartRedisKey(sn));
                    if (StringUtils.isBlank(jsonStr)) {
                        continue;
                    } else {
                        AcsDeviceHeart deviceHeart = JSONObject.parseObject(jsonStr, AcsDeviceHeart.class);
                        if (Constants.YES_INT == (int) deviceHeart.getStatus()) {
                            // 发现设备离线。记录
                            AcsDeviceHeart offLine = new AcsDeviceHeart();
                            offLine.setSn(sn)
                                    .setStatus(Constants.NO_INT)
                                    .setInsertTime(now);
                            dataList.add(offLine);
                            redisService.set(AcsDeviceHeart.getHeartRedisKey(sn), JSONObject.toJSONString(offLine));
                            alarmList.add(device);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(dataList)) {
                    heartService.insertList(dataList);
                }
       /* if (!CollectionUtils.isEmpty(alarmList)) {
            // 增加离线报警记录
            List<AcsCaptureAlarm> captureAlarmList = new ArrayList<>();
            AcsParameterConfig parameterConfig = new AcsParameterConfig();
            parameterConfig.setParameterCode(Constants.PamameterCode.OFFLINE);
            List<AcsParameterConfig> parameterConfigList = acsParameterConfigService.findList(parameterConfig);
            if (CollectionUtils.isEmpty(parameterConfigList)) {
                return;
            }
            for (int i = 0; i < alarmList.size(); i++) {
                AcsUserDevice device = alarmList.get(i);
                String company = device.getUser().getCompany();
                AcsParameterConfig config = null;
                for (int j = 0; j < parameterConfigList.size(); j++) {
                    AcsParameterConfig temp = parameterConfigList.get(j);
                    if (StringUtils.equals(company,temp.getCompany()) && StringUtils.equals(device.getModel(),temp.getParameterKey())) {
                        config = temp;
                        break;
                    }
                }
                if (config == null) {
                    continue;
                }
                AcsCaptureAlarm acsCaptureAlarm = new AcsCaptureAlarm();
                acsCaptureAlarm.setSn(device.getSn());
                acsCaptureAlarm.setInsertTime(new Date());
                acsCaptureAlarm.setParameterCode(config.getParameterCode());
                acsCaptureAlarm.setCompany(config.getCompany());
                acsCaptureAlarm.setParameterKey(config.getParameterKey());
                acsCaptureAlarm.setParameterContent(config.getParameterContent());
                acsCaptureAlarm.setCurrentValue(Constants.NO);
                acsCaptureAlarm.setParameterValue(String.valueOf(config.getParameterValue()));
                captureAlarmList.add(acsCaptureAlarm);
            }
            acsAlarmService.insertList(captureAlarmList);
        }*/
            }
        } finally {
            releaseLock(HEARTBEAT_LOCK, "1");
        }
    }

    /**
     * 每天检查下acs_capture_alarm 与 acs_capture_data表。
     * 如果数据很多，则删除一些
     */
    @Scheduled(cron = "0 0 0 1/1 * ?")
    public void clearDB() {
        try {
            if (getLock(MYSQL_CLEANUP_LOCK, "1", 10)) {
                final int maxRows = 2000000;
                // 1. 先删除3个月以前的数据。
                Date date = DateUtils.addMonths(new Date(), -3);
                acsAlarmService.delByDate(new Date(0L),date);
                captureDataMapper.delByDate(null,date);
                int count = acsAlarmService.findCount(null);
                int count1 = captureDataMapper.findCount(null);
                if (count > maxRows) {
                    AcsAlarmFilter param = new AcsAlarmFilter();
                    param.setLimit(1);
                    param.setOffset(maxRows - 1);
                    List<AcsCaptureAlarm> list = acsAlarmService.findList(param);
                    AcsCaptureAlarm alarm = list.get(0);
                    acsAlarmService.delByDate(new Date(0L),alarm.getInsertTime());
                }
                if (count1 > maxRows) {
                    AcsCaptureData data = new AcsCaptureData();
                    data.setLimit(1);
                    data.setOffset(maxRows - 1);
                    List<AcsCaptureData> list = captureDataMapper.findList(data);
                    AcsCaptureData data1 = list.get(0);
                    captureDataMapper.delByDate(new Date(0L),data1.getInsertTime());
                }
            }
        } finally {
            releaseLock(MYSQL_CLEANUP_LOCK, "1");
        }
    }

    /**
     * 每小时检查 外部接口表的变化，并更新
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void flushOpenInsterfaceCompany() {
        try {
            if (getLock(MYSQL_CLEANUP_LOCK, "1", 10)) {
                List<OpenInterface> list = openInterfaceService.findList(null);
                list = list == null ? new ArrayList<>() : list;
                List<String> keyList = list.stream().map(OpenInterface::getAccessKey).collect(Collectors.toList());
                Set<String> keySet = OpenInterfaceFilter.hashMap.keySet();
                for(String key : keySet) {
                    if (!keyList.contains(key)) {
                        OpenInterfaceFilter.hashMap.remove(key);
                        OpenInterfaceFilter.userMap.remove(key);
                    }
                }
                for (int i = 0; i < list.size(); i++) {
                    OpenInterface openInterface = list.get(i);
                    OpenInterfaceFilter.hashMap.put(openInterface.getAccessKey(),openInterface.getAccessSecret());
                    OpenInterfaceFilter.userMap.put(openInterface.getAccessKey(),openInterface.getCompany());
                }
            }
        } finally {
            releaseLock(MYSQL_CLEANUP_LOCK, "1");
        }
    }


    /**
     * 每天23:59更新 月流量统计表
     */
    @Scheduled(cron = "50 59 23 * * ?")
    public void updateFlowMonth(){
        try {
            if (getLock(MONTH_FLOW_LOCK, "1", 10)) {
                flowMonthService.flowCalculate();
            }
        } finally {
            releaseLock(MONTH_FLOW_LOCK, "1");
        }
    }
}
