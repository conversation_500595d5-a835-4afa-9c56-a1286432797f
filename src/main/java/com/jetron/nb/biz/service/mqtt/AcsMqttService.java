package com.jetron.nb.biz.service.mqtt;

import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.Md5Utils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.ShaUtils;
import com.jetron.nb.dal.dao.mqtt.AcsMqttUserMapper;
import com.jetron.nb.dal.dao.mqtt.MqttUserMapper;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.mqtt.AcsMqttUser;
import com.jetron.nb.dal.po.mqtt.MqttUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class AcsMqttService {
    @Resource
    MqttUserMapper mqttUserMapper;

    @Resource
    AcsMqttUserMapper acsMqttUserMapper;

    private static final String SALT = "d9&r#1";


    /**
     *  创建公司对应的MQTT账号及密码
     * @param userId 用户ID
     * @param username 用户名
     * @param password 密码
     */
    @Transactional
    public void createAcsMqttUser(int userId,String username,String password) {
        // 创建对应账号的MQTT用户名
//        String mqttUserName = UUID.randomUUID().toString().substring(0,6);
//        String password = Md5Utils.getMD5(mqttUserName + SALT).substring(0,6);
        String sha256Passwd = ShaUtils.SHA256(password);

        AcsMqttUser acsMqttUser = new AcsMqttUser();
        acsMqttUser.setMqttUser(username);
        acsMqttUser.setAcsUserId(userId);
        acsMqttUser.setCreated(new Date());
        acsMqttUserMapper.insert(acsMqttUser);
        // 将认证的账号密码存入数据库
        MqttUser mqttUser = new MqttUser();
        mqttUser.setCreated(new Date());
        mqttUser.setUsername(username);
        mqttUser.setPassword(sha256Passwd);
        mqttUserMapper.insert(mqttUser);
    }


    public void createAcsMqttUser(String sn, String password) {
        String sha256Passwd = ShaUtils.SHA256(password);
        MqttUser m = mqttUserMapper.findByUsername(sn);
        // 如果账户已存在，则更新账户密码
        if (m != null) {
            m.setPassword(sha256Passwd);
            mqttUserMapper.updateById(m);
            return;
        }
        // 将认证的账号密码存入数据库
        MqttUser mqttUser = new MqttUser();
        mqttUser.setCreated(new Date());
        mqttUser.setUsername(sn);
        mqttUser.setPassword(sha256Passwd);
        mqttUserMapper.insert(mqttUser);
    }


    /**
     *  获取当前账户对应的 MQTT服务器 账号密码
     * @param acsUser NMS系统用户
     * @return 返回账户信息
     */
    public AcsMqttUser getMqttUsernameAndPassword(AcsUser acsUser) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        AcsMqttUser acsMqttUser = new AcsMqttUser();
        int userId = 0;
        if (currentUser.getRole() == RoleEnum.SUPER_ADMIN.role) {
            userId = acsUser.getId();
        } else {
            userId = currentUser.getId();
        }
        if (currentUser != null && currentUser.getId() != null) {
            acsMqttUser = acsMqttUserMapper.selectByAcsUserId(userId);
        }

        if (acsMqttUser != null) {
            String password = Md5Utils.getMD5(acsMqttUser.getMqttUser() + SALT).substring(0,6);
            acsMqttUser.setMqttPassword(password);
        }
        return acsMqttUser;
    }

    public void updateByUsername(String username,String password){
        MqttUser byUsername = mqttUserMapper.findByUsername(username);
        if (byUsername == null){
            return ;
        }
        String sha256Passwd = ShaUtils.SHA256(password);
        byUsername.setPassword(sha256Passwd);
        mqttUserMapper.updateById(byUsername);
    }


    /**
     *  删除当前账户对应的 MQTT服务器 账号密码
     * @param list 用户名列表
     */
    @Transactional
    public void delMqttUserByUsername(List<String> list) {
        mqttUserMapper.delByUsername(list);
    }


    /**
     *  删除当前账户对应的 MQTT服务器 账号密码
     * @param userId 用户ID
     */
    @Transactional
    public void delAcsMqttUserByUserId(int userId) {
        acsMqttUserMapper.delByUserId(userId);
    }

    @Transactional
    public void updateByAcsUserId(AcsMqttUser acsMqttUser) {
        acsMqttUserMapper.updateByAcsUserId(acsMqttUser);
    }

    @Transactional
    public void updateMqttUsernameById(MqttUser mqttUser) {
        mqttUserMapper.updateUsernameById(mqttUser);
    }

    public MqttUser findMqttUserByUsername(String username){
        return mqttUserMapper.findByUsername(username);
    }
}
