package com.jetron.nb.biz.service;

import com.google.common.base.Splitter;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.oss.FileClient;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.FileUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.FirmwareInfo;
import com.jetron.nb.dal.dao.AcsFirmwareUpgradeMapper;
import com.jetron.nb.dal.dao.AcsUserDeviceMapper;
import com.jetron.nb.dal.dao.AcsUserMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsFirmwareMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsFirmwareService {
    @Resource
    private AcsFirmwareMapper acsFirmwareMapper;
    @Autowired
    private AcsUserDeviceService userDeviceService;
    @Resource
    private AcsFirmwareUpgradeMapper acsFirmwareUpgradeMapper;
    @Resource
    private AcsUserMapper acsUserMapper;
    @Resource
    private MqttMessageHandler mqttMessageHandler;

    @Value("${app.deployType}")
    private String deployType;

    public void upload(String name, String model, String version, String describ, MultipartFile file, Integer configId) {
        // 校验版本格式
        //this.checkVersionFormat(version);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        this.checkNameAndVersion(name, version, model, currentUser.getId());
        String path = this.saveFile(file);

        AcsFirmware newFirmware = new AcsFirmware();
        newFirmware.setName(name);
        newFirmware.setModel(model);
        newFirmware.setVersion(version);
        newFirmware.setDescrib(describ);
        newFirmware.setBelongTo(currentUser.getId());
        newFirmware.setPath(path);
        newFirmware.setCompany(currentUser.getCompany());
        newFirmware.setConfigId(configId);
        if (MdcUtils.isAdmin()) {
            newFirmware.setType(0);
        } else {
            newFirmware.setType(1);
        }

        acsFirmwareMapper.insertSelective(newFirmware);
    }


    private void checkNameAndVersion(String name, String version, String model, Integer userId) {
        FirmwareFilter filter = new FirmwareFilter();
        filter.setBelongTo(userId);
        filter.setIncludeSys(true);
        List<AcsFirmware> firmwareList = acsFirmwareMapper.getByFilter(filter);

        if ((int) firmwareList.stream().filter( f -> f.getName().equals(name)).count() > 0) {
            throw new IotException(ApiCode.FIRMWARE_ALREADY_EXIST);
        }
        int count = (int) firmwareList.stream().filter(f -> f.getVersion().equals(version) && f.getModel().equals(model)).count();
        if (count > 0) {
            throw new IotException(ApiCode.FIRMWARE_VERSION_ALREADY_EXIST);
        }
    }

    private String saveFile(MultipartFile file) {
        try {
            FileClient fileClient;
            if ("local".equals(deployType)) {
                fileClient = FileClientFactory.getFileClient("local");
            } else {
                fileClient = FileClientFactory.getFileClient("net");
            }
            String ext = FileUtils.getExtName(file.getOriginalFilename());
            String filePath = fileClient.upload(file.getInputStream(), "firmware/", ext);

            return filePath;
        } catch (IOException e) {
            log.error("[AcsFirmwareService#saveFile] error, {}", e);
            throw new IotException(ApiCode.FIRMWARE_UPLOAD_FAILED);
        }
    }

    public Map<String, Object> search(String name, String model, Integer page, Integer size) {
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = 20;
        }

        FirmwareFilter firmwareFilter = new FirmwareFilter();
        firmwareFilter.setName(name);
        firmwareFilter.setModel(model);
        firmwareFilter.setIncludeSys(true);
        firmwareFilter.setOffset((page - 1) * size);
        firmwareFilter.setLimit(size);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        firmwareFilter.setBelongTo(currentUser.getId());

        List<AcsFirmware> devices = acsFirmwareMapper.getByFilter(firmwareFilter);

        // 如果是用户管理员，除了查看自己上传的固件外，还可以查看超级管理员上传的固件
        if (currentUser.getRole() == RoleEnum.USER_ADMIN.role) {
            // 管理员上传的固件
            List<AcsFirmware> adminFirmwares = devices.stream().filter( d -> currentUser.getParentId().equals(d.getBelongTo())).collect(Collectors.toList());
            // 用户管理员上传的固件
            devices = devices.stream().filter( d -> currentUser.getCompany().equals(d.getCompany())).collect(Collectors.toList());
            devices.addAll(adminFirmwares);
        }

        List<FirmwareInfo> deviceInfos = toInfo(devices);
        int total = acsFirmwareMapper.countByFilter(firmwareFilter);

        return PageUtils.toPageResult(total, page, size, deviceInfos);

    }

    private List<FirmwareInfo> toInfo(List<AcsFirmware> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return Collections.EMPTY_LIST;
        }

        List<FirmwareInfo> firmwareInfos = Lists.newArrayList();
        for (AcsFirmware firmware : devices) {
            FirmwareInfo info = new FirmwareInfo();
            BeanUtils.copyProperties(firmware, info);

            firmwareInfos.add(info);
        }
        return firmwareInfos;
    }

    @Transactional
    public void upgrade(Integer firmwareId, String deviceIds) {
        List<Integer> deviceIdList = Splitter.on(",").splitToList(deviceIds)
                .stream()
                .map(e->Integer.valueOf(e))
                .collect(Collectors.toList());
        // 固件升级一次不能超过100台
        if (deviceIdList.size() > 100) {
            throw new IotException(ApiCode.DEVICE_UPGRADE_COUNT_EXCEED_MAX);
        }

        AcsFirmware firmware = acsFirmwareMapper.selectByPrimaryKey(firmwareId);

        StringBuilder sb = new StringBuilder("升级固件：")
                .append("(" + firmware.getName() + "," + firmware.getModel() + "," + firmware.getVersion() + ")\n");

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (firmware == null) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }
        if (firmware.getType() != 0 && !Objects.equals(firmware.getBelongTo(), currentUser.getId())) {
            throw new IotException(ApiCode.FIRMWARE_VALID_USER);
        }

        UserDeviceFilter userDeviceFilter = new UserDeviceFilter();
        userDeviceFilter.setDeviceIds(deviceIdList);
        if (!MdcUtils.isAdmin()) {
            userDeviceFilter.setBelongTo(currentUser.getId());
        }
        List<AcsUserDevice> userDeviceList = userDeviceService.findList(userDeviceFilter);

        sb.append(",升级设备：");
        for(AcsUserDevice device : userDeviceList) {
            sb.append("(" + device.getName() + "," + device.getSn() + "," + device.getModel() + ")\n");
        }
        sb.append(",升级结果：");
        MdcUtils.appendDetailLog(sb.toString());

        if (userDeviceList.size() != deviceIdList.size()) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        List<AcsFirmwareUpgrade> insertRecords = Lists.newArrayList();
        for (Integer deviceId : deviceIdList) {
            AcsFirmwareUpgrade upgrade = new AcsFirmwareUpgrade();
            upgrade.setFirmwareId(firmwareId);
            upgrade.setDeviceId(deviceId);
            upgrade.setBelongTo(currentUser.getId());
            upgrade.setStatus(0);

            insertRecords.add(upgrade);
        }

        acsFirmwareUpgradeMapper.batchInsert(insertRecords);
        userDeviceService.setUpgradeStatus(deviceIdList, 1);

        FileClient fileClient;
        if ("local".equals(deployType)) {
            fileClient = FileClientFactory.getFileClient("local");
        } else {
            fileClient = FileClientFactory.getFileClient("net");
        }
        String url = fileClient.getUrl(firmware.getPath());
//        List<String> snList = userDeviceList.stream().map(AcsUserDevice::getSn).collect(Collectors.toList());
        Map<String, Boolean> resultMap = userDeviceList.stream()
                .collect(Collectors.toMap(AcsUserDevice::getSn, AcsUserDevice::getUpgradeDevice));
        mqttMessageHandler.firmwareUpgrade(resultMap, firmware.getName(), url);

        log.info("[AcsFirmwareService#upgrade] success!");
    }

    @Transactional
    public void delete(Integer firmwareId) {
        AcsFirmware firmware = acsFirmwareMapper.selectByPrimaryKey(firmwareId);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (firmware == null || !Objects.equals(firmware.getBelongTo(), currentUser.getId())) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }

        int count1 = acsFirmwareUpgradeMapper.deleteByFirmwareId(firmwareId);
        int count2 = acsFirmwareMapper.deleteByPrimaryKey(firmwareId);

        if ("local".equals(deployType)) {
            FileClientFactory.getFileClient("local").delete(firmware.getPath());
        } else {
            FileClientFactory.getFileClient("net").delete(firmware.getPath());
        }
        log.info("delete firmware success, name: {}, {}, {}", firmware.getName(), count1, count2);
    }


    public int deleteByPrimaryKey(Integer id) {
        return acsFirmwareMapper.deleteByPrimaryKey(id);
    }

    
    public int insert(AcsFirmware record) {
        return acsFirmwareMapper.insert(record);
    }

    
    public int insertSelective(AcsFirmware record) {
        return acsFirmwareMapper.insertSelective(record);
    }

    
    public AcsFirmware selectByPrimaryKey(Integer id) {
        return acsFirmwareMapper.selectByPrimaryKey(id);
    }

    
    public int updateByPrimaryKeySelective(AcsFirmware record) {
        return acsFirmwareMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(AcsFirmware record) {
        return acsFirmwareMapper.updateByPrimaryKey(record);
    }

}
