package com.jetron.nb.biz.service;


import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.*;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.common.vo.UserInfo;
import com.jetron.nb.dal.po.AcsSysConfig;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
public class RemoteService {

    @Value("${heartbeat.interval}")
    private Integer heartBeat;

    @Value("${remote.nginxPort}")
    public List<Integer> portList;


    @Value("${remote.filePath}")
    public String CONF_PREFIX;

    @Value("${remote.containerPrefix}")
    public String DOCKER_CONTAINER_PREFIX;
    @Value("${remote.imageName}")
    public String DOCKER_IMAGE_NAME;

    @Value("${remote.username}")
    public String VPN_USERNAME;
    @Value("${remote.pass}")
    public String VPN_PASS;


    @Autowired
    private AcsUserDeviceService userDeviceService;

    @Autowired
    private AcsVpnConfigService vpnConfigService;
    @Autowired
    private AcsUserService userService;

    public Result validate(Integer userDeviceId) {
        AcsUserDevice userDevice = userDeviceService.selectByPrimaryKey(userDeviceId);

        if (userDevice == null) {
            return Result.toResult(ApiCode.FAIL);
        }
        // 验证权
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        int role = currentUser.getRole().intValue();
        if (role == RoleEnum.USER.role) {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        } else if (role == RoleEnum.USER_ADMIN.role && !userDevice.getBelongTo().equals(currentUser.getId())) {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        }

        long lastHeartBeatTime = userDevice.getLastHbTime().getTime();
        long now = DateTime.now().getMillis();
        if ((now - lastHeartBeatTime) / 1000 > heartBeat) {
            return Result.toResult(ApiCode.FAIL);
        }
        return Result.success(userDevice);
    }

    public Result runContainer(Integer userDeviceId,int type) {
        Result result = validate(userDeviceId);
        if (!ResultUtils.isSuccess(result)) {
            return result;
        }
        AcsUserDevice userDevice = (AcsUserDevice)result.getData();
        // 检查vpn用户在当前是否存在
        AcsUser byUsername = userService.getByUsername(VPN_USERNAME);
        if (byUsername == null){
            AcsUser user = new AcsUser();
            user.setName(VPN_USERNAME)
                    .setPasswd(Md5Utils.getMD5(VPN_PASS))
                    .setCompany(VPN_USERNAME)
                    .setDescrib(VPN_USERNAME)
                    .setParentId(0)
                    .setRole(1)
                    .setStatus(1);
            userService.insertSelective(user);
        }

        String vpnConfig = null;
        try {
            vpnConfig = vpnConfigService.getFinalConfig(userDevice.getBelongTo());
        } catch (IOException e) {
            log.error("getFinalConfig" + e);
            return Result.toResult(ApiCode.FAIL);
        }

        Integer port = portList.get(0);
        portList.remove(port);
        portList.add(port);
        String vpnConfPath = CONF_PREFIX + port + "/vpn.conf";
        String passPath = CONF_PREFIX + port + "/pwd";
        String nginxConfPath = CONF_PREFIX + port + "/nginx.conf";
        String containName = DOCKER_CONTAINER_PREFIX + port;
        // vpn文件
        FileUtils.writeText(vpnConfPath, vpnConfig, false);
        FileUtils.writeText(passPath, VPN_USERNAME +"\n"+ VPN_PASS, false);
        // nginx文件
        if (type == 1) {
            nginx(userDevice.getDumIp(), nginxConfPath);
        } else {
            nginx(userDevice.getTrueIp(), nginxConfPath);
        }
        // docker重启 nginx，vpn容器
        docker(port, containName, vpnConfPath,passPath, nginxConfPath);
        return Result.success(port);
    }

    public void nginx(String ip, String path) {
        List<String> nginxConfigList = FileUtils.readResource("template/nginx.conf");
        StringBuffer buffer = new StringBuffer("");
        for (int i = 0; i < nginxConfigList.size(); i++) {
            String s = nginxConfigList.get(i);
            if (s.contains("${ip}")) {
                s = s.replace("${ip}", ip);
            }
            buffer.append(s).append("\n");
        }
        FileUtils.writeText(path, buffer.toString(), false);
    }


    public void docker(Integer port, String name, String vpnPath,String vpnPassPath, String nginxPath) {
        // 重启nginx,docker
        log.info("重启 docker下的nginx、openVpn");
        String rm = "docker rm -f " + name;
        String[] rmCmd = new String[]{"/bin/sh", "-c", rm};
        int stopExec = 0;
        for (int i = 0; i < 3; i++) {
            stopExec = CommandUtils.exec(rmCmd);
            if (stopExec == 0) {
                break;
            }
        }
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            log.error("docker; Thread.sleep，error" + e);
        }

        String runVpn = String.format("docker run -d --device /dev/net/tun --cap-add=NET_ADMIN " +
                        " -p %s:80 --name %s  -v  %s:/etc/openvpn/client/pwd  " +
                        " -v  %s:/etc/openvpn/client/client.conf" +
                        " -v  %s:/usr/local/nginx/conf/nginx.conf   %s"
                , port, name,vpnPassPath, vpnPath, nginxPath, DOCKER_IMAGE_NAME);
        String[] cmd = new String[]{"/bin/sh", "-c", runVpn};
        int exec = 0;
        for (int i = 0; i < 3; i++) {
            exec = CommandUtils.exec(cmd);
            if (exec == 0) {
                break;
            }
        }
        // 等待命令执行完毕
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.error("docker; Thread.sleep，error" + e);
        }
    }


}
