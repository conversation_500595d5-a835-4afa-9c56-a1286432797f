package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jetron.nb.biz.service.mqtt.AcsMqttService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RedisKey;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.*;
import com.jetron.nb.common.vo.AccountInfo;
import com.jetron.nb.common.vo.UserInfo;
import com.jetron.nb.common.vo.UserStatisticsVo;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.dal.po.mqtt.AcsMqttUser;
import com.jetron.nb.dal.po.mqtt.MqttUser;
import com.jetron.nb.dal.vo.headscale.HeadscaleUser;
import com.jetron.nb.dal.vo.headscale.HeadscalePreAuthKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.jetron.nb.dal.dao.AcsUserMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsUserService {

    @Resource
    private AcsUserMapper acsUserMapper;
    @Resource
    private AcsUserDeviceService acsUserDeviceService;
    @Resource
    private AcsVpnConfigService acsVpnConfigService;
    @Resource
    private RedisOperationsSessionRepository redisOperationsSessionRepository;
    @Resource
    private RedisTemplate<String, Long> redisTemplateLong;
    @Resource
    private RedisTemplate<String, String> redisTemplateString;
    @Autowired
    private AcsMqttService acsMqttService;
    @Autowired
    private HeadscaleGrpcService headscaleGrpcService;
    @Autowired
    private AcsHeadscaleUserService acsHeadscaleUserService;
    @Autowired
    private HeadscaleService headscaleService;

    @Value("${app.licensePath}")
    private String licensePath;

    public AcsUser login(AcsUser loginUser) {
        // license检查
//        this.checkLicense();

        UserFilter filterByName = new UserFilter();
        filterByName.setName(loginUser.getName());
        List<AcsUser> users = acsUserMapper.getByFilter(filterByName);

        AcsUser user = CollectionUtils.isEmpty(users) ? null : users.get(0);
        this.checkLogin(user, loginUser, false, false);

        AcsUser updateUser = new AcsUser();
        updateUser.setLastLoginIp(user.getCurrLoginIp());
        updateUser.setLastLoginTime(user.getCurrLoginTime());
        updateUser.setCurrLoginIp(loginUser.getCurrLoginIp());
        updateUser.setCurrLoginTime(loginUser.getCurrLoginTime());

        updateUser.setId(user.getId());
        updateUser.setStatus(1);

        acsUserMapper.updateByPrimaryKeySelective(updateUser);

        String redisKey = RedisKeyUtils.getKey(RedisKey.LOGIN_FAILT_COUNT, loginUser.getName());
        redisTemplateLong.delete(redisKey);

        return acsUserMapper.selectByPrimaryKey(user.getId());
    }

    private void checkLicense() {
        String filepath = licensePath + "/license.nms";
        if (!LicenseUtils.checkLicensePath(filepath)) {
            throw new IotException(ApiCode.LICENSE_NOT_FOUND);
        }

        if (!LicenseUtils.checkLicense(filepath)) {
            throw new IotException(ApiCode.LICENSE_NOT_FOUND);
        }
    }

    private void checkLogin(AcsUser user, AcsUser loginUser, boolean deviceLogin, boolean passwdOnly) {
        if (user != null && user.getStatus() == 0) {
            throw new IotException(ApiCode.FAILED_COUNT_EXCEED);
        }

        String redisKey = RedisKeyUtils.getKey(RedisKey.LOGIN_FAILT_COUNT, loginUser.getName());
        if (user == null || (user != null && !checkPasswd(user, loginUser, deviceLogin))) {

            Long failedCount = redisTemplateLong.opsForValue().increment(redisKey);
            if (failedCount == 1 || redisTemplateLong.getExpire(redisKey, TimeUnit.DAYS) == -1) {
                redisTemplateLong.expire(redisKey, 1, TimeUnit.DAYS);
            } else if (failedCount > 5) {
                if (user != null) {
                    this.freeze(loginUser.getName());
                }
                throw new IotException(ApiCode.FAILED_COUNT_EXCEED);
            }

            ApiCode apiCode = !passwdOnly ? ApiCode.LOGIN_FAILED : ApiCode.PASSWD_NOT_CORRECT;
            throw new IotException(apiCode, (failedCount < 5) ? (5 - failedCount) : 0);
        }
    }

    private boolean checkPasswd(AcsUser user, AcsUser loginUser, boolean deviceLogin) {
        if (deviceLogin) {
            return Objects.equals(user.getDevicePasswd(), loginUser.getPasswd());
        } else {
            return Objects.equals(user.getPasswd(), loginUser.getPasswd());
        }
    }

    public void freeze(String username) {
        acsUserMapper.freeze(username);
    }

    public void processSession(AcsUser user, HttpSession session) {
        // 设置session信息
        String userJson = JSON.toJSONString(user);
        session.setAttribute("user_info", userJson);
        MdcUtils.putCurrentAcsUser(userJson);

        clearSession(user.getId());

        String redisKey = String.format("user_%s_sessionId", user.getId());
        redisTemplateString.opsForValue().set(redisKey, session.getId());
    }

    private void clearSession(Integer userId) {
        // 检测登录是否有其他登录session存在
        String redisKey = String.format("user_%s_sessionId", userId);
        String sessionId = redisTemplateString.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(sessionId)) {
            redisOperationsSessionRepository.deleteById(sessionId);
            log.info("[AcsUserService#clearSession] force delete login session, userId: {}, sessionId: {}", userId, sessionId);
        }
    }

    public void logout() {
        AcsUser updateUser = new AcsUser();
        updateUser.setId(MdcUtils.getCurrentAcsUser().getId());
        updateUser.setLastLogoutTime(new Date());

        acsUserMapper.updateByPrimaryKeySelective(updateUser);
    }

    public void changePasswd(String oldPasswd, String newPasswd) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        AcsUser loginUser = new AcsUser();
        loginUser.setName(currentUser.getName());
        loginUser.setPasswd(oldPasswd);

        this.checkLogin(currentUser, loginUser, false, true);

        AcsUser updateUser = new AcsUser();
        updateUser.setId(currentUser.getId());
        updateUser.setPasswd( Md5Utils.getMD5(newPasswd) );
        updateUser.setStatus(1);

        acsUserMapper.updateByPrimaryKeySelective(updateUser);

        acsMqttService.updateByUsername(currentUser.getName(),newPasswd);

        String redisKey = RedisKeyUtils.getKey(RedisKey.LOGIN_FAILT_COUNT, currentUser.getName());
        redisTemplateLong.delete(redisKey);
    }

    public void createUser(UserInfo userInfo) {
        // 验证是否有数量限制
        doesUserCountSurpassMaxLimit(userInfo);
        UserFilter filterByName = new UserFilter();
        filterByName.setName(userInfo.getName());
        List<AcsUser> oldUsers = acsUserMapper.getByFilter(filterByName);
        if (!CollectionUtils.isEmpty(oldUsers)) {
            throw new IotException(ApiCode.USERNAME_ALREADY_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() == RoleEnum.USER_ADMIN.role) {
            userInfo.setCompany(currentUser.getCompany());
        }

        UserFilter filterByParent = new UserFilter();
        filterByParent.setCompany(userInfo.getCompany());
        filterByParent.setParentId(currentUser.getId());
        List<AcsUser> allSubUsers = acsUserMapper.getByFilter(filterByParent);

        long customerAdminCount = allSubUsers.stream().filter(u -> u.getRole() == RoleEnum.USER_ADMIN.role).count();
        if (customerAdminCount > 0) {
            throw new IotException(ApiCode.CUSTOMER_ADMIN_ALREADY_EXIST);
        }

        long customerOperatorCount = allSubUsers.stream().filter(u -> u.getRole() == RoleEnum.USER.role).count();
        // 一个用户管理员下面的用户数量最多为10个
        if (customerOperatorCount > 10) {
            throw new IotException(ApiCode.CUSTOMER_OPERATOR_EXCEED_MAX);
        }

        AcsUser newUser = new AcsUser();
        newUser.setName(userInfo.getName());
        newUser.setPasswd(Md5Utils.getMD5(userInfo.getPasswd()) );
        newUser.setDescrib(userInfo.getDescrib());
        newUser.setParentId(currentUser.getId());
        newUser.setStatus(1);

        AcsVpnConfig vpnConfig = new AcsVpnConfig();
        if (currentUser.getRole() == RoleEnum.SUPER_ADMIN.role) {
            newUser.setCompany(userInfo.getCompany());
            newUser.setRole(1);

            vpnConfig.setVpnHost(userInfo.getVpnHost());
            vpnConfig.setVpnPort(userInfo.getVpnPort());
            vpnConfig.setMultilayer(userInfo.getMultilayer());
        } else {
            newUser.setCompany(currentUser.getCompany());
            newUser.setRole(2);

            AcsVpnConfig currConfig = acsVpnConfigService.getByUserId(currentUser.getId());
            vpnConfig.setVpnHost(currConfig.getVpnHost());
            vpnConfig.setVpnPort(currConfig.getVpnPort());
            vpnConfig.setMultilayer(userInfo.getMultilayer());
        }

        acsUserMapper.insertSelective(newUser);
        // 创建账户对应的MQTT账户密码
        if (currentUser.getRole() == RoleEnum.SUPER_ADMIN.role) {
            acsMqttService.createAcsMqttUser(newUser.getId(),userInfo.getName(),userInfo.getPasswd());
        }

        vpnConfig.setUserId(newUser.getId());
        acsVpnConfigService.insertSelective(vpnConfig);

        // 创建 Headscale 用户
        createHeadscaleUser(userInfo, newUser, currentUser);
    }

    // 验证是否有数量限制
    private void doesUserCountSurpassMaxLimit(UserInfo userInfo) {
        Integer licenseLimiter = getLicenseLimiter();
        if (licenseLimiter > 0) {
            // 查询用户数量
            UserFilter filterByName = new UserFilter();
            filterByName.setCompany(userInfo.getCompany());
            filterByName.setRole(RoleEnum.USER.role);
            List<AcsUser> count = acsUserMapper.getByFilter(filterByName);
            // 判断是否超过限制
            if (count.size() >= licenseLimiter) {
                throw new IotException(ApiCode.LICENSE_LIMITER_EXCEED);
            }
        }
    }

    @Transactional
    public void modifyUser(UserInfo userInfo) {
        checkSubUserValid(userInfo.getUserId());

        if (StringUtils.isNotBlank(userInfo.getName())) {
            UserFilter filterByName = new UserFilter();
            filterByName.setName(userInfo.getName());
            List<AcsUser> users = acsUserMapper.getByFilter(filterByName);
            if (!CollectionUtils.isEmpty(users)
                    && !Objects.equals(userInfo.getUserId(), users.get(0).getId())) {
                throw new IotException(ApiCode.USERNAME_ALREADY_EXIST);
            }
        }
        // 用户名变更时同步变更MQTT账户密码
        AcsUser oldAcsUser = acsUserMapper.selectByPrimaryKey(userInfo.getUserId());
        if (oldAcsUser != null && !Objects.equals(oldAcsUser.getName(), userInfo.getName())) {
            updateMqttInfo(oldAcsUser, userInfo);
        }

        AcsUser updateUser = new AcsUser();
        updateUser.setId(userInfo.getUserId());
        updateUser.setName(userInfo.getName());
        updateUser.setDescrib(userInfo.getDescrib());

        acsUserMapper.updateByPrimaryKeySelective(updateUser);

        // 更新VPNConfig
        if (StringUtils.isNotBlank(userInfo.getVpnHost())
                || !Objects.isNull(userInfo.getVpnPort())) {
            AcsVpnConfig oldConfig = acsVpnConfigService.getByUserId(userInfo.getUserId());
            AcsVpnConfig updateConfig = new AcsVpnConfig();
            updateConfig.setUserId(userInfo.getUserId());
            updateConfig.setVpnHost(userInfo.getVpnHost());
            updateConfig.setVpnPort(userInfo.getVpnPort());
            updateConfig.setMultilayer(userInfo.getMultilayer());
            if (oldConfig != null) {
                updateConfig.setId(oldConfig.getId());
                updateConfig.setVpnConfig("");
                acsVpnConfigService.updateByPrimaryKeySelective(updateConfig);
            } else {
                updateConfig.setGmtCreate(new Date());
                updateConfig.setGmtModify(new Date());
                acsVpnConfigService.insert(updateConfig);
            }
        }
    }

    private void updateMqttInfo(AcsUser oldUser, UserInfo userInfo) {
        AcsMqttUser acsMqttUser = new AcsMqttUser();
        acsMqttUser.setAcsUserId(oldUser.getId());
        acsMqttUser.setMqttUser(userInfo.getName());
        acsMqttService.updateByAcsUserId(acsMqttUser);

        MqttUser mqttUser = acsMqttService.findMqttUserByUsername(oldUser.getName());
        if (mqttUser != null) {
            mqttUser.setUsername(userInfo.getName());
            acsMqttService.updateMqttUsernameById(mqttUser);
        }
    }

    @Transactional
    public void resetPasswd(UserInfo userInfo) {
        checkSubUserValid(userInfo.getUserId());
        AcsUser user = this.selectByPrimaryKey(userInfo.getUserId());

        AcsUser updateUser = new AcsUser();
        updateUser.setId(userInfo.getUserId());
        updateUser.setPasswd( Md5Utils.getMD5(userInfo.getPasswd()));
        updateUser.setStatus(1);
        acsUserMapper.updateByPrimaryKeySelective(updateUser);

        acsMqttService.updateByUsername(user.getName(),userInfo.getPasswd());

        AcsUser acsUser = acsUserMapper.selectByPrimaryKey(userInfo.getUserId());
        String redisKey = RedisKeyUtils.getKey(RedisKey.LOGIN_FAILT_COUNT, acsUser.getName());
        redisTemplateLong.delete(redisKey);
    }

    private void checkSubUserValid(Integer userId) {
        AcsUser modifyUser = acsUserMapper.selectByPrimaryKey(userId);
        if (modifyUser == null) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(currentUser.getId(), modifyUser.getParentId())) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
    }

    /**
     * 查询所有记录，包含被删除记录
     * @param company
     * @param status
     * @param page
     * @param size
     * @return
     */
    public Map<String, Object> search(String company, Integer status, Integer page, Integer size) {
        UserFilter filter = new UserFilter();
        filter.setCompany(company);
        filter.setStatus(status);

        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }
        filter.setLimit((page - 1) * size);
        filter.setOffset(size);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        filter.setParentId(currentUser.getId());
        filter.setAll(true);

        List<AcsUser> users = acsUserMapper.getAll(filter);
        int total = acsUserMapper.countByFilter(filter);

        Map<String, Object> result = Maps.newHashMap();
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("records", toAccountInfo(users));

        return result;
    }

    /**
     * 查询所有记录
     * @param filter
     * @return
     */
    public List<AcsUser> findList(  UserFilter filter) {
        return acsUserMapper.getByFilter(filter);
    }

    /**
     * 查询所有记录
     * @param filter
     * @return
     */
    public Map<String, Object> findPage(UserFilter filter,Integer page, Integer size) {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }
        filter.setLimit((page - 1) * size);
        filter.setOffset(size);

        List<AcsUser> users = acsUserMapper.getByFilter(filter);
        int total = acsUserMapper.countByFilter(filter);

        Map<String, Object> result = Maps.newHashMap();
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("records", toAccountInfo(users));
        return result;
    }

    private List<AccountInfo> toAccountInfo(List<AcsUser> users) {
        List<AccountInfo> result = Lists.newArrayList();

        List<Integer> userIds = users.stream().map(u -> u.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }

        Map<Integer, AcsVpnConfig> vpnConfigMap = acsVpnConfigService.getByUserIds(userIds)
            .stream().collect(Collectors.toMap(v->v.getUserId(), v->v));

        for (AcsUser user : users) {
            AccountInfo accountInfo = new AccountInfo();
            result.add(accountInfo);
            BeanUtils.copyProperties(user, accountInfo);

            AcsVpnConfig config = vpnConfigMap.get(user.getId());
            if (config == null) {
                continue;
            }
            accountInfo.setVpnHost(config.getVpnHost());
            accountInfo.setVpnPort(config.getVpnPort());
            accountInfo.setMultilayer(config.getMultilayer());
        }

        return result;
    }

    @Transactional
    public void deleteUser(Integer userId) {
        AcsUser acsUser = acsUserMapper.selectByPrimaryKey(userId);
        if (acsUser == null) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role && acsUser.getParentId() != currentUser.getId()) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
        UserDeviceFilter filter = new UserDeviceFilter();
        filter.setBelongTo(userId);
        List<AcsUserDevice> list = acsUserDeviceService.findList(filter);
        List<String> snList = list.stream().map(AcsUserDevice::getSn).collect(Collectors.toList());

        // 删除vpn配置信息
        acsVpnConfigService.deleteByUserId(userId);
        // 删除设备信息
        acsUserDeviceService.deleteByUserId(userId);
        // 删除创建的子账号
        acsUserMapper.deleteChildren(userId);
        acsUserMapper.deleteById(userId);
        acsMqttService.delAcsMqttUserByUserId(userId);

        if (!CollectionUtils.isEmpty(snList)){
            acsMqttService.delMqttUserByUsername(snList);
        }

        // 删除 Headscale 用户
        deleteHeadscaleUser(acsUser);

        // 清理删除用户的session
        List<AcsUser> deletedUsers = acsUserMapper.getDeletedUser(DateTime.now().withTimeAtStartOfDay().toDate(), new Date());
        for (AcsUser user : deletedUsers) {
            this.clearSession(user.getId());
        }
        log.info("[AcsUserService#deleteUser] success! userId: {}", userId);
    }

    @Transactional
    public void undoDel(Integer userId) {
        UserFilter filter = new UserFilter();
        filter.setUserIds(Arrays.asList(new Integer[]{userId}));
        List<AcsUser> userList = acsUserMapper.getAll(filter);
        if (CollectionUtils.isEmpty(userList) ) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
        AcsUser acsUser = userList.get(0);
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role && acsUser.getParentId() != currentUser.getId()) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
        if (acsUser.getStatus() == 0){//如果被冻结，则解冻
            acsUser.setStatus(1);
            acsUserMapper.updateByPrimaryKeySelective(acsUser);
        }
        if (acsUser.getDel() != 0) {
            // 撤销删除vpn配置信息
            acsVpnConfigService.undoDeleteByUserId(userId);
            // 撤销删除设备信息
            // 撤销删除创建的子账号
            acsUserMapper.undoDeleteChildren(userId);
            acsUserMapper.undoDel(userId);

            // 清理删除用户的session
            List<AcsUser> deletedUsers = acsUserMapper.getDeletedUser(DateTime.now().withTimeAtStartOfDay().toDate(), new Date());
            for (AcsUser user : deletedUsers) {
                this.clearSession(user.getId());
            }
            log.info("[AcsUserService#deleteUser] success! userId: {}", userId);
        }
    }


    public List<AcsUser> getDeletedUser(Date begin, Date end) {
        List<AcsUser> deletedList = acsUserMapper.getDeletedUser(begin, end);
        List<String> deletedNames = deletedList.stream().map(x->x.getName()).collect(Collectors.toList());

        UserFilter filter = new UserFilter();
        filter.setNameList(deletedNames);
        List<String> reAddedNames = acsUserMapper.getByFilter(filter).stream()
                .map(x->x.getName()).collect(Collectors.toList());

        return deletedList.stream().filter(x->!reAddedNames.contains(x.getName())).collect(Collectors.toList());
    }

    public List<String> getAllUserNames() {
        return acsUserMapper.getAllUserNames();
    }

    public AcsUser getByUsername(String username) {
        return acsUserMapper.getByUsername(username);
    }

    public int insertSelective(AcsUser record) {
        return acsUserMapper.insertSelective(record);
    }

    public AcsUser selectByPrimaryKey(Integer id) {
        return acsUserMapper.selectByPrimaryKey(id);
    }

    public int updateByPrimaryKeySelective(AcsUser record) {
        return acsUserMapper.updateByPrimaryKeySelective(record);
    }

    public int insert(AcsUser record) {
        return acsUserMapper.insert(record);
    }

    public int updateByPrimaryKey(AcsUser record) {
        return acsUserMapper.updateByPrimaryKey(record);
    }

    public AcsMqttUser getMqttUsernameAndPassword(String username) {
        if (org.springframework.util.StringUtils.isEmpty(username)) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
        AcsUser acsUser = getByUsername(username);
        if (acsUser == null) {
            throw new IotException(ApiCode.USER_NOT_EXIST);
        }
        return acsMqttService.getMqttUsernameAndPassword(acsUser);
    }

    /**
     *  创建系统 license
     * @param response x
     * @param expireDate 到期时间，格式 2021-7-4
     * @throws IOException 异常
     */
    public void createLicense(HttpServletResponse response, String expireDate, Integer limiter) throws IOException {
        boolean isDate = false;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            format.setLenient(false);
            format.parse(expireDate);
            isDate = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (!isDate) {
            return;
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        String filepath = licensePath + "/" + currentUser.getName();
        File dir = new File(filepath);
        // 一、检查放置文件的文件夹路径是否存在，不存在则创建
        if (!dir.exists()) {
            // mkdirs创建多级目录
            dir.mkdirs();
        }
        String fileName = licensePath + "/" + currentUser.getName() + "/license.nms";;
        String value = LicenseUtils.generateLicenseValue(expireDate, limiter);
        FileUtils.generateLicenseFile(fileName, value);
        File file = new File(fileName);
        FileUtils.doDownload(response, file);
    }

    /**
     * 查询所有用户公司。 分组查询
     * @return
     */
    public List<String> company(){
        return acsUserMapper.company();
    }

    public int countByFilter(UserFilter filter){
        return acsUserMapper.countByFilter(filter);
    }

    /**
     * 用户统计信息。
     * @param filter
     * @param page
     * @param size
     * @return
     */
    public Map<String, Object> userStatistics(UserFilter filter,Integer page, Integer size) {
        filter.setLimit((page - 1) * size);
        filter.setOffset(size);

        List<UserStatisticsVo> userStatisticsVos = acsUserMapper.userStatistics(filter);
        int total = acsUserMapper.countUserStatistics(filter);

        Map<String, Object> result = Maps.newHashMap();
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("records", userStatisticsVos);
        return result;
    }

    /**
     *  查找租户管理员
     * @return 租户管理员列表
     */
    public List<AcsUser> findTenantAdmin() {
        UserFilter userFilter = new UserFilter();
        userFilter.setRole(1);
        return findList(userFilter);
    }

    // 获取license限制用户创建数量
    private Integer getLicenseLimiter() {
        String filepath = licensePath + "/license.nms";
        return LicenseUtils.getLicenseLimiter(filepath);
    }

    /**
     * 创建 Headscale 用户
     *
     * @param userInfo 用户信息
     * @param newUser 新创建的用户
     * @param currentUser 当前用户
     */
    private void createHeadscaleUser(UserInfo userInfo, AcsUser newUser, AcsUser currentUser) {
        try {
            String username = userInfo.getName();
            String companyName = userInfo.getCompany();

            // 处理公司名称：中文转拼音，去除特殊字符
            String displayName = PinyinUtils.processCompanyName(companyName);

            log.info("Creating Headscale user: username={}, original company={}, processed displayName={}",
                    username, companyName, displayName);

            // 调用 Headscale gRPC API 创建用户
            HeadscaleUser headscaleUser = headscaleGrpcService.createUserWithNamespace(username, displayName);

            // 创建数据库记录
            AcsHeadscaleUser acsHeadscaleUser = new AcsHeadscaleUser();
            acsHeadscaleUser.setUsername(username);
            acsHeadscaleUser.setDisplayName(displayName);
            acsHeadscaleUser.setHeadscaleUserId(headscaleUser.getId());
            acsHeadscaleUser.setAcsUserId(newUser.getId());
            acsHeadscaleUser.setCreateBy(currentUser.getId());
            acsHeadscaleUser.setRemark("Created via ACS user creation");

            // 保存到数据库
            acsHeadscaleUserService.createHeadscaleUser(acsHeadscaleUser);

            log.info("Successfully created Headscale user and saved record: username={}, headscaleUserId={}",
                    username, headscaleUser.getId());

            // 创建 PreAuth Key 并存储到 Redis
            createAndStorePreAuthKey(username);

        } catch (Exception e) {
            log.error("Failed to create Headscale user for username: {}, company: {}",
                    userInfo.getName(), userInfo.getCompany(), e);
            // 注意：这里不抛出异常，避免影响主要的用户创建流程
            // 可以根据业务需求决定是否需要回滚或者重试
        }
    }

    /**
     * 删除 Headscale 用户
     *
     * @param acsUser 要删除的 ACS 用户
     */
    private void deleteHeadscaleUser(AcsUser acsUser) {
        try {
            String username = acsUser.getName();

            log.info("Deleting Headscale user: username={}", username);

            // 首先查询数据库中是否存在对应的 Headscale 用户记录
            AcsHeadscaleUser headscaleUserRecord = acsHeadscaleUserService.getByAcsUserId(acsUser.getId());

            if (headscaleUserRecord != null) {
                // 调用 Headscale REST API 删除用户
                // 使用 deleteUserSafely 方法，它会检查用户是否有设备节点
                headscaleService.deleteUserSafely(username);

                log.info("Successfully deleted Headscale user: username={}", username);

                // 删除数据库中的记录（逻辑删除）
                acsHeadscaleUserService.deleteHeadscaleUser(headscaleUserRecord.getId());

                log.info("Successfully deleted Headscale user record from database: username={}, recordId={}",
                        username, headscaleUserRecord.getId());
            } else {
                log.info("No Headscale user record found for ACS user: username={}, acsUserId={}",
                        username, acsUser.getId());

                // 即使数据库中没有记录，也尝试删除 Headscale 中的用户（可能是数据不一致的情况）
                try {
                    headscaleService.deleteUserSafely(username);
                    log.info("Successfully deleted orphaned Headscale user: username={}", username);
                } catch (Exception e) {
                    // 如果用户不存在，这是正常情况，不需要记录错误
                    if (e.getMessage() != null && e.getMessage().contains("用户不存在")) {
                        log.info("Headscale user does not exist, no need to delete: username={}", username);
                    } else {
                        log.warn("Failed to delete orphaned Headscale user: username={}", username, e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("Failed to delete Headscale user for username: {}", acsUser.getName(), e);
            // 注意：这里不抛出异常，避免影响主要的用户删除流程
            // 可以根据业务需求决定是否需要重试机制或者手动清理
        }
    }

    /**
     * 创建 PreAuth Key 并存储到 Redis
     *
     * @param username 用户名
     */
    public void createAndStorePreAuthKey(String username) {
        try {
            log.info("Creating PreAuth Key for Headscale user: username={}", username);

            // 调用 Headscale REST API 创建 24 小时过期的 PreAuth Key
            // reusable=false, ephemeral=false, expirationHours=24
            HeadscalePreAuthKey preAuthKey = headscaleService.createPreAuthKey(username, true, false, 24);

            if (preAuthKey != null && StringUtils.isNotBlank(preAuthKey.getKey())) {
                // 构建 Redis key: 用户名 + "-headscale"
                String redisKey = username + "-headscale";

                // 存储到 Redis，设置 23 小时过期（比 PreAuth Key 提前 1 小时过期）
                redisTemplateString.opsForValue().set(redisKey, preAuthKey.getKey(), 23, TimeUnit.HOURS);

                log.info("Successfully created and stored PreAuth Key: username={}, redisKey={}, keyId={}, expiration=23h",
                        username, redisKey, preAuthKey.getId(), preAuthKey.getExpiration());
            } else {
                log.warn("Failed to create PreAuth Key: username={}, response is null or key is blank", username);
            }

        } catch (Exception e) {
            log.error("Failed to create PreAuth Key for username: {}", username, e);
            // 注意：这里不抛出异常，避免影响主要的用户创建流程
            // PreAuth Key 创建失败不应该影响用户创建，可以后续手动创建
        }
    }
}




