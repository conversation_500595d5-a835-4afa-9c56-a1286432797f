package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jetron.nb.biz.mqtt.processor.NatRuleProcessor;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.vo.AcsNatInfo;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.AcsNatMapper;
import com.jetron.nb.dal.po.AcsNatRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * NAT规则下发管理逻辑层
 */
@Service
@Slf4j
public class AcsNatManageService {

    @Autowired
    private AcsNatMapper natMapper;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private RedisServiceImpl redisService;


    // 拼接条件
    private QueryWrapper<AcsNatRule> getWrapper(AcsNatRule acsNatRule) {
        QueryWrapper<AcsNatRule> queryWrapper = new QueryWrapper<>();
        // 添加条件，根据 displayName 和 sn 查询
        if (acsNatRule.getDisplayName() != null && !acsNatRule.getDisplayName().isEmpty()) {
            queryWrapper.like("display_name", acsNatRule.getDisplayName());
        }
        if (acsNatRule.getSn() != null && !acsNatRule.getSn().isEmpty()) {
            queryWrapper.eq("sn", acsNatRule.getSn());
        }
        // 返回查询条件
        return queryWrapper;
    }

    /**
     * 根据条件分页查询nat数据
     * @param pageNum 分页
     * @param pageSize 分页
     * @param acsNatRule 查询条件
     * @return 结果集
     */
    public IPage<AcsNatRule> queryPageWithCondition(int pageNum, int pageSize, AcsNatRule acsNatRule) {
        // 创建分页对象
        Page<AcsNatRule> page = new Page<>(pageNum, pageSize);
        // 创建查询条件
        QueryWrapper<AcsNatRule> wrapper = getWrapper(acsNatRule);
        // 执行分页查询
        IPage<AcsNatRule> resultPage = natMapper.selectPage(page, wrapper);
        return resultPage;
    }

    /**
     * 新增nat规则
     * @param acsNatRule 日志数据
     */
    public void addNatLog(AcsNatRule acsNatRule) {
        // 1、插入数据
        natMapper.insert(acsNatRule);
    }


    /**
     * 删除nat规则
     * @param id 数据id
     */
    public void delNatLog(Integer id) {
        // 1、校验
        AcsNatRule acsNatRule = natMapper.selectById(id);
        if (acsNatRule == null) {
            return;
        }
        // 2、删除数据
        natMapper.deleteById(id);
    }

    /**
     * 修改nat单条状态
     */
    public void updateNatStatus(Integer id, String status) {
        // 1、校验
        AcsNatRule acsNatRule = natMapper.selectById(id);
        if (acsNatRule == null) {
            return;
        }
        // 2、修改状态
        acsNatRule.setStatus(status);
        natMapper.updateById(acsNatRule);
    }

    /**
     * 从缓存中获取NAT规则
     * @param sn sn号
     */
    public JSONObject getNatBySnFromCache(String sn) {
        // 根据sn获取存储的NAT规则
        String natRuleStr = redisService.get(NatRuleProcessor.getRedisKey(sn, Constants.NAT_TYPE_GET));
        // 封装数据
        return JSON.parseObject(natRuleStr);
    }

    /**
     * 从数据库中获取NAT规则
     * @param sn sn号
     */
    public List<AcsNatRule> getNatBySn(String sn) {
        // 创建查询条件
        QueryWrapper<AcsNatRule> queryWrapper = new QueryWrapper<>();
        // 根据sn获取存储的NAT规则
        if (StringUtils.isNotEmpty(sn)) {
            queryWrapper.eq("sn", sn);
        } else {
            return new ArrayList<>();
        }
        List<AcsNatRule> acsNatRules = natMapper.selectList(queryWrapper);
        // 封装数据
        return acsNatRules;
    }

    /**
     *  下发接口，推送NAT规则至网关
     * @param nat 下发规则
     * @param sendType 发送类型  add\get\del
     */
    public void sendNatDelivery(AcsNatInfo nat, String sendType) {
        log.info("sendNat start nat：:{}", nat);
        // 1、封装下发数据
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonStr = "";
        try {
            // 将NAT规则对象进行序列化
            jsonStr = objectMapper.writeValueAsString(nat);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        JSONObject paramJson = JSON.parseObject(jsonStr);
        paramJson.put("cmd", MessageCmd.NAT_RULE_DELIVERY.cmd);
        paramJson.put("type", sendType);
        paramJson.remove("sn"); // 消息体移除sn
        if (Constants.NAT_TYPE_ADD.equals(sendType)) {
            // 将 protocol 字段的值拆分为数组
            JSONArray jsonArray = parseProtocol(nat.getProtocol());
            // 将新的 JSON 数组放回原始 JSON 对象
            paramJson.put("protocol", jsonArray);
        }
        // 2、推送消息
        acsUserDeviceService.sendMqtt(nat.getSn(), paramJson);
        // 3、推送后状态处理
        if (Constants.NAT_TYPE_ADD.equals(sendType)) {
            AcsNatRule natRuleByDisplayName = getNatRuleByDisplayName(nat.getDisplayName());
            // 如果没存 新增一条待确认数据
            if (natRuleByDisplayName == null) {
                // 判断是否是重新推送
                AcsNatRule acsNatRule = new AcsNatRule();
                BeanUtils.copyProperties(nat, acsNatRule);
                acsNatRule.setStatus(Constants.NAT_RLUE_STATUS_LOCK);
                acsNatRule.setGmtCreate(new Date());
                addNatLog(acsNatRule);
            }
        }
        log.info("sendNat end nat：:{}", nat);
    }

    // 解析字符串为 JSON 数组
    private JSONArray parseProtocol(String protocol) {
        // 将 protocol 字段的值拆分为数组
        String[] protocolArray = protocol.split(",");
        // 创建包含 protocol 数组的新 JSON 数组
        return new JSONArray(Arrays.asList(protocolArray));
    }

    /**
     * 根据 displayName 查询 NAT 规则
     * @param displayName  名称
     * @return 返回体
     */
    public AcsNatRule getNatRuleByDisplayName(String displayName) {
        QueryWrapper<AcsNatRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("display_name", displayName);
        return natMapper.selectOne(queryWrapper);
    }

    /**
     * 同步网关的NAT规则至数据库
     * @param natsFromGateway nat规则
     */
    public void syncNatRule(List<AcsNatRule> natsFromGateway, String sn) {
        // 获取已有的nat规则数据
        List<AcsNatRule> natsFromDb = getNatBySn(sn);
        // 获取唯一值集合
        List<String> displayNamesFromGateway = natsFromGateway.stream()
                .map(AcsNatRule::getDisplayName).collect(Collectors.toList());
        List<String> displayNamesFromDb = natsFromDb.stream()
                .map(AcsNatRule::getDisplayName).collect(Collectors.toList());
        // 使用流和Collectors将List转换为Map
        Map<String, Integer> displayNameToIdMap = natsFromDb.stream()
                .collect(Collectors.toMap(AcsNatRule::getDisplayName, AcsNatRule::getId));
        // 找出删除的数据, displayNamesFromDb存在，displayNamesFromGateway不存在的数据
        List<Integer> removeIds = natsFromDb.stream().
                filter(nat -> !displayNamesFromGateway.contains(nat.getDisplayName()))
                .map(AcsNatRule::getId).collect(Collectors.toList());
        // 找出新增的数据,  displayNamesFromGateway存在，displayNamesFromDb不存在的数据
        List<AcsNatRule> addNats = natsFromGateway.stream()
                .filter(nat -> !displayNamesFromDb.contains(nat.getDisplayName()))
                .peek(nat -> {
                    nat.setSn(sn);
                    nat.setStatus(Constants.NAT_RLUE_STATUS_NORMAL);
                    nat.setGmtCreate(new Date());
                })
                .collect(Collectors.toList());
        // 找出修改的数据， 两个集合都存在的数据
        List<AcsNatRule> updateNats = natsFromGateway.stream()
                .filter(nat -> displayNamesFromDb.contains(nat.getDisplayName()))
                .peek(nat -> {
                    nat.setId(displayNameToIdMap.get(nat.getDisplayName()));
                })
                .collect(Collectors.toList());
        // 新增、修改、删除同步
        if (CollectionUtils.isNotEmpty(removeIds)) {
            natMapper.deleteBatchIds(removeIds);
        }
        if (CollectionUtils.isNotEmpty(addNats)) {
            natMapper.insertBatch(addNats);
        }
        if (CollectionUtils.isNotEmpty(updateNats)) {
            natMapper.updateBatch(updateNats);
        }
        log.info("syncNatRule end。sn：{}, 删除：{}, 新增：{}, 修改：{}", sn, removeIds.size(), addNats.size(), updateNats.size());
    }

}
