package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.ConfigUpgradeInfo;
import com.jetron.nb.dal.po.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsConfigUpgradeMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AcsConfigUpgradeService {

    @Resource
    private AcsConfigUpgradeMapper acsConfigUpgradeMapper;
    @Resource
    private AcsConfigService acsConfigService;
    @Resource
    private AcsUserDeviceService acsUserDeviceService;

    public List<ConfigUpgradeInfo> getUpgradeRecords(Integer configId) {
        AcsConfig acsConfig = acsConfigService.selectByPrimaryKey(configId);
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (acsConfig == null
                || (acsConfig.getType() != 0 && !Objects.equals(acsConfig.getBelongTo(), currentUser.getId()))) {
            throw new IotException(ApiCode.CONFIG_NOT_EXIST);
        }

        UpgradeFilter filter = new UpgradeFilter();
        filter.setId(configId);
        filter.setBelongTo(MdcUtils.getCurrentAcsUser().getId());
        List<AcsConfigUpgrade> upgradeList = acsConfigUpgradeMapper.getByFilter(filter);
        return toInfo(upgradeList);
    }

    public Map<String, Object> page(UpgradeFilter filter, Integer page, Integer size) {
        AcsConfig acsConfig = acsConfigService.selectByPrimaryKey(filter.getId());
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (acsConfig == null
                || (acsConfig.getType() != 0 && !Objects.equals(acsConfig.getBelongTo(), currentUser.getId()))) {
            throw new IotException(ApiCode.CONFIG_NOT_EXIST);
        }
        if (!MdcUtils.isAdmin()) {
            filter.setBelongTo(MdcUtils.getCurrentAcsUser().getId());
        }
        filter.setLimit(size);
        filter.setOffset((page - 1) * size);
        filter.setValid(1);
        List<AcsConfigUpgrade> upgradeList = acsConfigUpgradeMapper.getByFilter(filter);
        List<ConfigUpgradeInfo> infos = toInfo(upgradeList);
        int count = acsConfigUpgradeMapper.count(filter);
        return PageUtils.toPageResult(count, page, size, infos);
    }

    private List<ConfigUpgradeInfo> toInfo(List<AcsConfigUpgrade> upgradeList) {
        if (CollectionUtils.isEmpty(upgradeList)) {
            return Lists.newArrayList();
        }

        List<Integer> deviceIds = upgradeList.stream()
                .map(AcsConfigUpgrade::getDeviceId).collect(Collectors.toList());

        List<AcsUserDevice> deviceList = acsUserDeviceService.getByDeviceIds(deviceIds);
        Map<Integer, AcsUserDevice> deviceMap = deviceList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));

        List<ConfigUpgradeInfo> infoList = Lists.newArrayList();

        for (AcsConfigUpgrade upgrade : upgradeList) {
            ConfigUpgradeInfo info = new ConfigUpgradeInfo();
            BeanUtils.copyProperties(upgrade, info);

            Integer deviceId = upgrade.getDeviceId();
            AcsUserDevice device = deviceMap.get(deviceId);
            if (device == null) {
                continue;
            }
            info.setName(device.getName());
            info.setSn(device.getSn());
            infoList.add(info);
        }

        return infoList;
    }

    public List<AcsConfigUpgrade> findList(UpgradeFilter filter){
        if (filter == null) {
            filter = new UpgradeFilter();
        }
        List<AcsConfigUpgrade> byFilter = acsConfigUpgradeMapper.getByFilter(filter);
        byFilter = byFilter == null ? new ArrayList<>() : byFilter;
        return byFilter;
    }

    public AcsConfigUpgrade getLatestUpgrade(String sn) {
        AcsUserDevice userDevice = acsUserDeviceService.getBySn(sn);
        if (userDevice == null) {
            return null;
        }

        AcsConfigUpgrade upgrade = acsConfigUpgradeMapper.getLatestUpgrade(userDevice.getId());
        return upgrade;
    }

    public int deleteByPrimaryKey(Integer id) {
        return acsConfigUpgradeMapper.deleteByPrimaryKey(id);
    }

    public int insert(AcsConfigUpgrade record) {
        return acsConfigUpgradeMapper.insert(record);
    }

    public int insertSelective(AcsConfigUpgrade record) {
        return acsConfigUpgradeMapper.insertSelective(record);
    }

    public AcsConfigUpgrade selectByPrimaryKey(Integer id) {
        return acsConfigUpgradeMapper.selectByPrimaryKey(id);
    }

    public int updateByPrimaryKeySelective(AcsConfigUpgrade record) {
        return acsConfigUpgradeMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(AcsConfigUpgrade record) {
        return acsConfigUpgradeMapper.updateByPrimaryKey(record);
    }

    public List<AcsConfigUpgrade> getTimeout(Date deadline) {
        return acsConfigUpgradeMapper.getTimeout(deadline);
    }

    public int timeout(List<Integer> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return 0;
        }

        return acsConfigUpgradeMapper.timeout(deviceIds);
    }

    /**
     * 强制完成升级
     */
    @Transactional
    public void completeUpgrade(Integer upgradeId){
        AcsConfigUpgrade configUpgrade = selectByPrimaryKey(upgradeId);
        configUpgrade.setStatus(3);

        Integer deviceId = configUpgrade.getDeviceId();
        List<Integer> list = new ArrayList<>();
        list.add(deviceId);
        acsUserDeviceService.setUpgradeStatus(list,0);
        updateByPrimaryKeySelective(configUpgrade);
    }

    public void deleteUpgradeByConfigId(Integer configId) {
        AcsConfig config = acsConfigService.selectByPrimaryKey(configId);

        if (config == null) {
            throw new IotException(ApiCode.CONFIG_NOT_EXIST);
        }
        UpgradeFilter filter = new UpgradeFilter();
        filter.setId(configId);
        if (!MdcUtils.isAdmin()) {
            filter.setBelongTo(MdcUtils.getCurrentAcsUser().getId());
        }
        List<AcsConfigUpgrade> list = findList(filter);
        List<Integer> userDeviceIdList = list.stream()
                .map(AcsConfigUpgrade::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userDeviceIdList)) {
            acsUserDeviceService.setUpgradeStatus(userDeviceIdList,0);
        }
        int count1 = acsConfigUpgradeMapper.deleteConfigId(configId);

    }
}
