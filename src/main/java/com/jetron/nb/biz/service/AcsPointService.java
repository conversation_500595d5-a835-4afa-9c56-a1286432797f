package com.jetron.nb.biz.service;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.oss.FileClient;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.*;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.common.vo.UserDeviceInfo;
import com.jetron.nb.dal.dao.AcsPointMapper;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.web.common.config.NMSConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcsPointService {

    @Autowired
    private AcsPointMapper mapper;
    @Autowired
    private AcsUserDeviceService userDeviceService;
    @Autowired
    private AcsDriverUpgradeService driverUpgradeService;
    @Autowired
    private AcsDriverService driverService;
    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    public QueryWrapper<AcsPoint> wrapper(AcsPoint param) {
        QueryWrapper<AcsPoint> wrapper = new QueryWrapper<AcsPoint>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.eq("company",param.getCompany());
        }
        if (param.getDeviceId() != null) {
            wrapper.eq("device_id",param.getDeviceId());
        }
        if (StringUtils.isNotBlank(param.getDevName())) {
            wrapper.eq("dev_name",param.getDevName());
        }
        return wrapper;
    }

    public QueryWrapper<AcsDriverUpgrade> driverUpgradeWrapper(AcsDriverUpgrade param) {
        QueryWrapper<AcsDriverUpgrade> wrapper = new QueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.eq("company",param.getCompany());
        }
        if (param.getDeviceId() != null) {
            wrapper.eq("device_id",param.getDeviceId());
        }
        if (param.getStatus() != null) {
            wrapper.eq("status",param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getDevName())) {
            wrapper.eq("dev_name",param.getDevName());
        }
        return wrapper;
    }


    public AcsPoint findOne(AcsPoint param) {
        return mapper.selectOne(wrapper(param));
    }

    public List<AcsPoint> findList(AcsPoint param) {
        return mapper.selectList(wrapper(param));
    }

    public Map<String, Object> page(Integer pageNum, Integer pageSize, UserDeviceFilter deviceFilter) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            deviceFilter.setBelongTo(currentUser.getId());
        } else if (currentUser.getRole().intValue() == RoleEnum.USER.role) {
            deviceFilter.setBelongTo(currentUser.getParentId());
        }
        deviceFilter.setOffset((pageNum - 1) * pageSize);
        deviceFilter.setLimit(pageSize);

        List<AcsUserDevice> devices = userDeviceService.findListWithPoint(deviceFilter);
        int total = userDeviceService.countByFilter(deviceFilter);

        List<UserDeviceInfo> deviceInfos = userDeviceService.toDeviceInfo(devices);

        String company = MdcUtils.getCurrentAcsUser().getCompany();
        List<AcsPoint> pointList = findList(new AcsPoint().setCompany(company));
        List<Integer> deviceIdList = pointList.stream().map(AcsPoint::getDeviceId).collect(Collectors.toList());

        List<AcsDriverUpgrade> upgradeList = driverUpgradeService
                .selectCurrentStatus(new AcsDriverUpgrade().setCompany(company));
        for (int i = 0; i < deviceInfos.size(); i++) {
            UserDeviceInfo device = deviceInfos.get(i);
            Integer id = device.getId();

            List<AcsDriverUpgrade> uList = upgradeList.stream().filter(obj -> id.equals(obj.getDeviceId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uList)) {
                device.setDriverUpStatus(uList.get(0).getStatus());
            }
            if (deviceIdList.contains(id)) {
                device.setUploadPoint(true);
            }
        }

        return PageUtils.toPageResult(total, pageNum, pageSize, deviceInfos);
    }

    public List<AcsBaseDev> getBaseDevice(Integer deviceId) {
        JSONArray devArray = userDeviceService.getBaseDevice(deviceId);
        if (devArray.size() == 0) {
            return new ArrayList<>();
        }
        String company = MdcUtils.getCurrentAcsUser().getCompany();
        List<AcsBaseDev> list = JSONArray.parseArray(devArray.toJSONString(),AcsBaseDev.class);
        List<AcsPoint> pointList = findList(new AcsPoint().setCompany(company).setDeviceId(deviceId));
        List<AcsDriverUpgrade> upgradeList = driverUpgradeService
                .selectCurrentStatus(new AcsDriverUpgrade().setCompany(company).setDeviceId(deviceId));
        for (int i = 0; i < list.size(); i++) {
            AcsBaseDev baseDev = list.get(i);
            String name = baseDev.getBaseName();
            List<AcsPoint> pList = pointList.stream().filter(obj -> deviceId.equals(obj.getDeviceId()) && name.equals(obj.getDevName()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pList)) {
                baseDev.setPoint(true);
            }
            List<AcsDriverUpgrade> uList = upgradeList.stream().filter(obj -> deviceId.equals(obj.getDeviceId()) && name.equals(obj.getDevName()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uList)) {
                baseDev.setUpStatus(uList.get(0).getStatus());
            }
        }
        return list;
    }

    public Result upload(Integer deviceId, MultipartFile file) {
        String company = MdcUtils.getCurrentAcsUser().getCompany();
        Date date = new Date();
        String yyyyMmDdHhMmSs = DateFormatUtils.format(date, "yyyy_MM_dd HH_mm_ss");
        // 临时文件夹路径
        String tempPath = "temp/" + yyyyMmDdHhMmSs + "/";
        // 临时zip文件
        String tempZipPath = tempPath + "temp.zip";
        File zipFile = new File(tempZipPath);
        zipFile.getParentFile().mkdirs();
        // 临时excel文件夹
        String tempExcelDir = tempPath  + "excel/";
        File excelDir = new File(tempExcelDir);
        excelDir.mkdirs();
        try {
            FileUtils.copyInputStreamToFile(file.getInputStream(),zipFile);
            // 解压缩
            ZipUtils.unzip(zipFile.getAbsolutePath(),tempExcelDir);

            // {"total":,"timestamp":,"configurations":[{"Device":[{}],"version":"1.0.0","PointList":[{}]}]}
            List<AcsPoint> list = new ArrayList<>();
            File[] files = excelDir.listFiles();
            List<File> fileList = new ArrayList<>();
            for (int i = 0; i < files.length; i++) {
                File excel = files[i];
                if (excel.isDirectory()) {
                    File[] files1 = excel.listFiles();
                    for (int k = 0; k < files1.length; k++) {
                        fileList.add(files1[k]);
                    }
                } else {
                    fileList.add(excel);
                }
            }
            for (int i = 0; i < fileList.size(); i++) {
                File excel = fileList.get(i);
                List<List<Object>> deviceList = ExcelUtils.getImportData(new FileInputStream(excel), 0);
                List<List<Object>> pointList = ExcelUtils.getImportData(new FileInputStream(excel), 1);
                if (CollectionUtils.isEmpty(deviceList) || CollectionUtils.isEmpty(pointList)) {
                    return Result.toResult(ApiCode.BAD_REQUEST);
                }
                JSONObject deviceTemp = getDevice(deviceList);
                JSONArray pointArray = getPointList(pointList);

                JSONObject deviceJson = new JSONObject();
                JSONArray deviceArray = new JSONArray();
                deviceArray.add(deviceTemp);

                deviceJson.put("Device",deviceArray);
                deviceJson.put("PointList",pointArray);
                deviceJson.put("version","1.0.0");

                JSONArray configArray = new JSONArray();
                configArray.add(deviceJson);

                JSONObject pointJson = new JSONObject();
                pointJson.put("timestamp", date.getTime());
                pointJson.put("configurations",configArray);
                pointJson.put("total",configArray.size());

                String deviceID = deviceTemp.getString("DeviceID");
                AcsPoint point = new AcsPoint();
                point.setCompany(company);
                point.setGmtCreate(date);
                point.setDeviceId(deviceId);
                point.setDevName(deviceID);
                point.setPoint(pointJson);
                list.add(point);
            }
            AcsPoint param = new AcsPoint();
            param.setDeviceId(deviceId);
            mapper.delete(wrapper(param));

            mapper.insertList(list);
        } catch (Exception e) {
            log.error("" + e);
            return Result.toResult(ApiCode.SERVER_ERROR);
        } finally {
            try {
                FileUtils.deleteDirectory(new File(tempPath));
            } catch (IOException e) {
                log.error("" + e);
            }
        }
        return Result.success();
    }

    public Result uploadDev(Integer deviceId,String devName, MultipartFile file) {
        Date date = new Date();
        String yyyyMmDdHhMmSs = DateFormatUtils.format(date, "yyyy_MM_dd HH_mm_ss");
        String name = file.getOriginalFilename();
        String filePrefix = name.substring(0,name.indexOf("."));
        String tempPath = "temp/" + yyyyMmDdHhMmSs + "/";
        String tempZipPath = tempPath + name;
        // 解压后的excel文件
        String excelPath = tempPath  + filePrefix + ".xlsx";
        // 临时zip文件
        File zipFile = new File(tempZipPath);
        zipFile.getParentFile().mkdirs();
        try {
            FileUtils.copyInputStreamToFile(file.getInputStream(),zipFile);
            // 解压缩
            ZipUtils.unzip(zipFile.getAbsolutePath(),tempPath);
            // 读取excel
            File excel = new File(excelPath);

            JSONObject pointJson = new JSONObject();
            JSONArray configArray = new JSONArray();

            List<List<Object>> deviceList = ExcelUtils.getImportData(new FileInputStream(excel), 0);
            List<List<Object>> pointList = ExcelUtils.getImportData(new FileInputStream(excel), 1);
            if (CollectionUtils.isEmpty(deviceList) || CollectionUtils.isEmpty(pointList)) {
                return Result.toResult(ApiCode.BAD_REQUEST);
            }
            JSONObject deviceTemp = getDevice(deviceList);
            JSONArray pointArray = getPointList(pointList);

            JSONObject deviceJson = new JSONObject();
            JSONArray deviceArray = new JSONArray();
            deviceArray.add(deviceTemp);

            deviceJson.put("Device",deviceArray);
            deviceJson.put("PointList",pointArray);
            deviceJson.put("version","1.0.0");
            configArray.add(deviceJson);

            pointJson.put("configurations",configArray);
            pointJson.put("total",configArray.size());
            pointJson.put("timestamp", date.getTime());

            AcsPoint param = new AcsPoint();
            param.setDeviceId(deviceId);
            param.setDevName(devName);
            mapper.delete(wrapper(param));
            String company = MdcUtils.getCurrentAcsUser().getCompany();
            AcsPoint point = new AcsPoint();
            point.setCompany(company);
            point.setGmtCreate(date);
            point.setDeviceId(deviceId);
            point.setDevName(devName);
            point.setPoint(pointJson);
            mapper.insert(point);
        } catch (Exception e) {
            log.error("" + e);
            return Result.toResult(ApiCode.SERVER_ERROR);
        } finally {
            try {
                FileUtils.deleteDirectory(new File(tempPath));
            } catch (IOException e) {
                log.error("" + e);
            }
        }
        return Result.success();
    }

    public JSONObject getDevice(List<List<Object>> deviceList) throws Exception{
        List<Object> headList = deviceList.get(1);
        List<Object> dList = deviceList.get(2);
        JSONObject deviceJson = new JSONObject();
        for (int i = 0; i < headList.size(); i++) {
            String head = (String)headList.get(i);
            String val = null;
            if (dList.size() > i) {
                val = (String)dList.get(i);
            }
            deviceJson.put(head,val);
        }
        return deviceJson;
    }

    public JSONArray getPointList(List<List<Object>> pointList) {
        JSONArray array = new JSONArray();
        List<Object> headList = pointList.get(1);
        for (int row = 2; row < pointList.size(); row++) {
            List<Object> rowList = pointList.get(row);
            JSONObject pointJson = new JSONObject();
            for (int i = 0; i < headList.size(); i++) {
                String head = (String)headList.get(i);
                String val = null;
                if (rowList.size() > i) {
                    val = (String)rowList.get(i);
                }
                pointJson.put(head,val);
            }
            array.add(pointJson);
        }

        return array;
    }

    public void downloadJson(HttpServletResponse resp, Integer deviceId) {
        List<AcsPoint> list = findList(new AcsPoint().setDeviceId(deviceId));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        JSONObject pointJson = list.get(0).getPoint();
        JSONArray devArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject point = list.get(0).getPoint();
            JSONArray jsonArray = point.getJSONArray("configurations");
            devArray.addAll(JSONArray.parseArray(jsonArray.toJSONString()));
        }
        pointJson.put("total",list.size());
        pointJson.put("configurations",devArray);
        try {
            com.jetron.nb.common.util.FileUtils.doDownload(resp,  "temp.json",pointJson.toJSONString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void deleteList(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        mapper.deleteBatchIds(list);
    }

    public Result upgradeDriver(Integer deviceId,String devName,String driverStr) {
        String company = MdcUtils.getCurrentAcsUser().getCompany();
        List<String> list1 = Arrays.asList(driverStr.split(","));
        List<Integer> driverIdList = JSONArray.parseArray(JSONObject.toJSONString(list1), Integer.class);

        AcsDriverUpgrade uParam = new AcsDriverUpgrade();
        uParam.setDeviceId(deviceId);
        AcsDriverUpgrade last = driverUpgradeService.findLast(uParam);
        if (last!= null && (int)last.getStatus() == Constants.DriverUpgradeStatus.INT) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        AcsUserDevice device = userDeviceService.selectByPrimaryKey(deviceId);
        AcsPoint pointParam = new AcsPoint();
        pointParam.setDeviceId(deviceId);
        pointParam.setDevName(devName);
        List<AcsPoint> list = findList(pointParam);

        AcsDriver driverParam = new AcsDriver();
        driverParam.setIdList(driverIdList);
        List<AcsDriver> driverList = driverService.list(driverParam);
        String path = null;
        if (CollectionUtils.isEmpty(list)) {
            // 如果没有上传点表
            return Result.toResult(ApiCode.BAD_REQUEST);
        } else {
            JSONObject pointJson = list.get(0).getPoint();
            JSONArray devArray = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject point = list.get(i).getPoint();
                JSONArray jsonArray = point.getJSONArray("configurations");
                devArray.addAll(JSONArray.parseArray(jsonArray.toJSONString()));
            }
            pointJson.put("total",list.size());
            pointJson.put("configurations",devArray);

            String tempPath = "temp/" + DateFormatUtils.format(new Date(),"yyyy_MM_dd_HH_mm_ss");

            String zipPath = String.format("%s/%s.zip",tempPath,deviceId);
            FileClient fileClient;
            try{
                List<File> fileList = new ArrayList<>();
                // 点表文件
                File pointFile = new File(tempPath + "/default_config.json");
                pointFile.getParentFile().mkdirs();
                FileUtils.write(pointFile,pointJson.toJSONString(),"utf-8");
                fileList.add(pointFile);

                if ("local".equals(NMSConfig.deployType)) {
                    fileClient = FileClientFactory.getFileClient("local");
                } else {
                    fileClient = FileClientFactory.getFileClient("net");
                }
                for (int i = 0; i < driverList.size(); i++) {
                    AcsDriver driver = driverList.get(i);
                    String driverPath = driver.getPath();
                    String driverFileName = driverPath.substring(driverPath.lastIndexOf("/")+ 1);
                    File driverFile = new File(tempPath + "/" + driverFileName);
                    InputStream driverIs = fileClient.getFileStream(driverPath);
                    FileUtils.copyToFile(driverIs,driverFile);
                    fileList.add(driverFile);
                }
                ZipUtils.zip(fileList,zipPath);
                File zipFile = new File(zipPath);
                String extName = com.jetron.nb.common.util.FileUtils.getExtName(zipFile.getName());
                path = fileClient.upload(new FileInputStream(zipPath), "driver/", extName);
            } catch (Exception e) {
                log.error("" + e);
                return Result.toResult(ApiCode.SERVER_ERROR);
            } finally {
                try {
                    FileUtils.deleteDirectory(new File(tempPath));
                } catch (IOException e) {
                    log.error("" + e);
                }
            }
            AcsDriverUpgrade upgrade = new AcsDriverUpgrade();
            upgrade.setCompany(company);
            upgrade.setDeviceId(deviceId);
            upgrade.setDriverId(driverStr);
            upgrade.setPath(path);
            upgrade.setStatus(Constants.DriverUpgradeStatus.INT);
            driverUpgradeService.add(upgrade);

            String url = fileClient.getUrl(path);
            JSONObject mqttMsg = new JSONObject();
            mqttMsg.put("cmd", MessageCmd.DRIVER_UPGRADE.cmd);
            mqttMsg.put("url",url);
            mqttMessageHandler.sendMqtt(device.getSn(), mqttMsg, device.getUpgradeDevice());
        }
        return Result.success();
    }

    public Result pointKey(Integer deviceId,String devName) {
        AcsPoint one = findOne(new AcsPoint().setDeviceId(deviceId).setDevName(devName));
        if (one == null) {
            return Result.success();
        }
        JSONObject point = one.getPoint();
        JSONArray configurations = point.getJSONArray("configurations");
        JSONArray pointArray =  configurations.getJSONObject(0).getJSONArray("PointList");
        Set<String> keySet = pointArray.getJSONObject(0).keySet();
        List<String> collect = keySet.stream().sorted().collect(Collectors.toList());
        return Result.success(collect);
    }


    public Result pointTable(Integer pageNum,Integer pageSize,Integer deviceId,String devName) {
        AcsPoint one = findOne(new AcsPoint().setDeviceId(deviceId).setDevName(devName));
        if (one == null) {
            return Result.success();
        }
        JSONObject point = one.getPoint();
        JSONArray configurations = point.getJSONArray("configurations");
        JSONArray pointArray =  configurations.getJSONObject(0).getJSONArray("PointList");
        int total = pointArray.size();
        int from = (pageNum - 1) * pageSize;
        int end = pageNum * pageSize > pointArray.size() ?  pointArray.size() : pageNum * pageSize;
        List<Object> list = pointArray.subList(from, end);

        Map<String, Object> map = PageUtils.toPageResult(total, pageNum, pageSize, list);
        return Result.success(map);
    }

    public static void pointTemplate() {
        String str = "{" +
                "    \"configurations\": [" +
                "        {" +
                "            \"Device\": [" +
                "                {" +
                "                    \"DeviceID\": \"modbustcp1\"," +
                "                    \"Interval\": \"1000\"," +
                "                    \"PollsDelay\": \"5\"," +
                "                    \"ResponseTimeout\": \"500\"," +
                "                    \"PortName\": \"192.168.1.100\"," +
                "                    \"Protocol\": \"modbus_tcp\"," +
                "                    \"Type\": \"2\"," +
                "                    \"Parameter1\": \"502\"," +
                "                    \"Parameter2\": \"1\"," +
                "                    \"Describe\": \"\"" +
                "                }" +
                "            ]," +
                "            \"PointList\": [" +
                "                {" +
                "                    \"name\": \"point01\"," +
                "                    \"Writable\": \"1\"," +
                "                    \"Uom\": \"\"," +
                "                    \"RegisterType\": \"0\"," +
                "                    \"OffSet\": \"0\"," +
                "                    \"DataType\": \"1\"," +
                "                    \"Address\": \"000000\"," +
                "                    \"DataLen\": \"\"," +
                "                    \"ByteOrder\": \"1\"," +
                "                    \"Describe\": \"\"" +
                "                }                " +
                "            ]," +
                "            \"version\": \"1.0.0\"" +
                "        }" +
                "    ]," +
                "    \"total\": 10," +
                "    \"timestamp\": 1673488265" +
                "}";
    }
}
