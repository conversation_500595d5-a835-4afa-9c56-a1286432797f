package com.jetron.nb.biz.service;

import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.SysConfig;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.CommandUtils;
import com.jetron.nb.common.util.FileUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.AcsVpnMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.jetron.nb.dal.dao.AcsVpnConfigMapper;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class AcsVpnConfigService{

    @Value("${vpn.shell.path}")
    private String vpnShellPath;
    // 用户 user.ovpn 文件存放地点
    @Value("${vpn.client.path}")
    private String vpnClientPath;

    @Resource
    private AcsVpnConfigMapper acsVpnConfigMapper;
    @Autowired
    private MqttMessageHandler mqttMessageHandler;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsSysConfigService acsSysConfigService;
    @Autowired
    private AcsUserService acsUserService;
    @Resource
    private AcsVpnMapper acsVpnMapper;

    public int insert(AcsVpnConfig record) {
        return acsVpnConfigMapper.insert(record);
    }

    
    public int insertSelective(AcsVpnConfig record) {
        return acsVpnConfigMapper.insertSelective(record);
    }

    
    public AcsVpnConfig selectByPrimaryKey(Integer id) {
        return acsVpnConfigMapper.selectByPrimaryKey(id);
    }

    
    public int updateByPrimaryKeySelective(AcsVpnConfig record) {
        return acsVpnConfigMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(AcsVpnConfig record) {
        return acsVpnConfigMapper.updateByPrimaryKey(record);
    }


    public AcsVpnConfig getByUserId(Integer userId) {
        return acsVpnConfigMapper.getByUserId(userId);
    }

    public List<AcsVpnConfig> getByUserIds(List<Integer> userIds) {
        return acsVpnConfigMapper.getByUserIds(userIds);
    }


    public void dispatchConfigBySn(String sn, Boolean upgradeTopic) throws IOException {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice == null) {
            throw new IotException(ApiCode.DEVICE_LOG_NOT_EXIST);
        }
        if (upgradeTopic) {
            acsUserDevice.setUpgradeDevice(true);
            acsUserDeviceService.updateByPrimaryKey(acsUserDevice);
        }

        String content = this.getFinalConfig(acsUserDevice.getBelongTo());
        mqttMessageHandler.dispatchVpnConfigBySn(sn, content, upgradeTopic);
    }

    public void dispatchConfigAndDeviceInfoByUserName(String userName) throws IOException {
        AcsUser user = acsUserService.getByUsername(userName);
        Integer userId = user.getId();
        String content = this.getFinalConfig(userId);
        List<AcsUserDevice> deviceList = acsUserDeviceService.getByUserId(userId);
        mqttMessageHandler.dispatchVpnConfigByUsername(userName, content, deviceList);
    }

    public String getFinalConfig(Integer userId) throws IOException {
        AcsVpnConfig vpnConfig = acsVpnConfigMapper.getByUserId(userId);
        AcsSysConfig sysConfig;
        // 是否是二层，否则三层
        if (null != vpnConfig && Constants.MULTILAYER_TWO.equals(vpnConfig.getMultilayer())) {
            sysConfig = acsSysConfigService.getByName(SysConfig.VPN_CONFIG2.name);
        } else {
            sysConfig = acsSysConfigService.getByName(SysConfig.VPN_CONFIG.name);
        }
        if (Objects.isNull(sysConfig) || StringUtils.isBlank(sysConfig.getContent())) {
            return Strings.EMPTY;
        }

        AcsVpnConfig acsVpnConfig = this.getByUserId(userId);
        if (Objects.isNull(acsVpnConfig)) {
            throw new IotException(ApiCode.USER_VPN_CONFIG_NOT_EXIST);
        }

        if (StringUtils.isBlank(acsVpnConfig.getVpnConfig())) {
            AcsUser user = acsUserService.selectByPrimaryKey(userId);

            String configContent = this.generateVPNConfig(user.getName(), acsVpnConfig.getVpnHost(), acsVpnConfig.getVpnPort());

            AcsVpnConfig updateConfig = new AcsVpnConfig();
            updateConfig.setId(acsVpnConfig.getId());
            updateConfig.setVpnConfig(configContent);
            this.updateByPrimaryKeySelective(updateConfig);
            acsVpnConfig.setVpnConfig(configContent);
        }

        return sysConfig.getContent() + "\n" + acsVpnConfig.getVpnConfig();
    }

    public String generateVPNConfig(String username, String vpnHost, int vpnPort) throws IOException {
        String filename = vpnClientPath + "/" + username +".crt";

        // 生成用户凭证, crt格式
        log.info("开始生成VPN证书名：  " + username);
        String dockerCmd = "docker exec -i docker_openvpn sh -c 'cd /root && sh gen_client_crt.sh " + username + "'";
        String[] cmd = new String[]{"/bin/sh", "-c", dockerCmd};
        int exec = 0;
        for (int i = 0; i < 3; i++) {
            exec = CommandUtils.exec(cmd);
            if (exec == 0) {
                break;
            }
        }
        // 等待证书生成
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        StringBuilder sb = FileUtils.readCrtFile(filename);
        sb.append("remote ").append(vpnHost).append(" ").append(vpnPort).append("\n");
        return sb.toString();
    }

    public void deleteByUserId(Integer userId) {
        acsVpnConfigMapper.deleteByUserId(userId);
    }

    public void undoDeleteByUserId(Integer userId) {
        acsVpnConfigMapper.undoDeleteByUserId(userId);
    }

    /**
     *  设置openvpn静态IP
     * @param userId 用户名ID
     * @param tIP 目标IP，即用户名对应的静态IP
     * @param rIP 远程IP，一般是 目标IP 顺序IP
     * @return 成功：true
     */
    public boolean setVpnStaticIp(Integer userId, String tIP, String rIP) {
        AcsUser acsUser = acsUserService.selectByPrimaryKey(userId);
        String dockerCmd = "echo \"ifconfig-push "+ tIP + " " + rIP +"\" | docker run -v /mnt/vpn-data:/etc/openvpn -i --rm openvpn tee /etc/openvpn/ccd/" + acsUser.getName();
        String[] cmd = new String[]{"/bin/sh", "-c", dockerCmd};
        return CommandUtils.exec(cmd) == 0;
    }

    public int insertVpnStatic(AcsVpn acsVpn) {
        return acsVpnMapper.insert(acsVpn);
    }

    public int updateVpnStatic(AcsVpn acsVpn) {
        return acsVpnMapper.update(acsVpn);
    }

    public int deleteVpnStaticById(int id) {
        return acsVpnMapper.delete(id);
    }

    public Map<String, Object> findAllVpnStatic(Integer page, Integer size) {
        List<AcsVpn> result = acsVpnMapper.findAll();
        return PageUtils.toPageResult(result.size(), page, size, result);
    }

    public Map<String, Object> findVpnStaticByUserName(Integer userId, Integer page, Integer size) {
        AcsUser acsUser = acsUserService.selectByPrimaryKey(userId);
        List<AcsVpn> result = acsVpnMapper.findByUserName(acsUser.getName());
        return PageUtils.toPageResult(result.size(), page, size, result);
    }

    public void pushConfigBySn(String sn) throws IOException {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice == null) {
            throw new IotException(ApiCode.DEVICE_LOG_NOT_EXIST);
        }
        String content = this.getFinalConfig(acsUserDevice.getBelongTo());
        mqttMessageHandler.pushVpnConfigBySn(sn, content, acsUserDevice.getUpgradeDevice());
    }

    public void pushConfigByUserName(String userName) throws IOException {
        AcsUser user = acsUserService.getByUsername(userName);
        Integer userId = user.getId();
        String content = this.getFinalConfig(userId);
        mqttMessageHandler.pushVpnConfigByUsername(userName, content);
    }
}
