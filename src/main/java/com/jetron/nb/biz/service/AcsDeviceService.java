package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RedisDatabaseEnum;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.EncryptUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.util.ResultUtils;
import com.jetron.nb.common.vo.DeviceOnlineInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsDeviceMapper;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsDeviceService {

    @Value("${heartbeat.interval}")
    private Integer heartBeat;

    @Value("${mqtt.broker}")
    private String mqttBroker;

    @Resource
    private AcsDeviceMapper acsDeviceMapper;
    @Resource
    private AcsUserDeviceService acsUserDeviceService;
    @Resource
    private AcsUserService acsUserService;
    @Autowired
    private RedisServiceImpl redisService;
    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    public static final String KEY_256 = "Jetron@888";
    public static final String MESSAGE_256 = "JENET";

    public void add(String model, String sn) {
        AcsDevice record = new AcsDevice();
        record.setModel(model);
        record.setSn(sn);

        List<AcsDevice> oldDevice = acsDeviceMapper.getBySnList(Arrays.asList(record.getSn()));
        if (!CollectionUtils.isEmpty(oldDevice)) {
            throw new IotException(ApiCode.DEVICE_ALREADY_EXIST);
        }

        acsDeviceMapper.insertSelective(record);

        // 初始化MQTT账户
        initMqttAccount(sn);
    }

    /**
     * 将给定的序列号列表（snList）转换为AcsDevice对象列表，并进行一系列处理。
     *
     * @param snList 字符串列表，每个字符串表示一个设备的序列号和型号，格式为"型号;序列号"。
     * @return 处理结果，如果成功则返回Result.success()，否则返回包含错误信息的Result对象。
     * @throws IllegalArgumentException 如果列表中某个字符串的格式不正确（即不是"型号;序列号"格式），则可以选择抛出此异常。
     */
    public Result addList(List<String> snList) {
        // 创建一个用于存储AcsDevice对象的列表
        List<AcsDevice> devices = new ArrayList<>();
        // 遍历snList数组
        for (String item : snList) {
            // 使用;分割字符串
            String[] parts = item.split(";");
            if (parts.length != 2) {
                // 如果分割后的数组长度不是2，说明格式不正确，可以抛出异常或记录日志
                log.error("Invalid format for item: {}", item);
                continue; // 或者抛出异常 throw new IllegalArgumentException("Invalid format");
            }

            // 创建AcsDevice对象并赋值
            AcsDevice device = new AcsDevice();
            device.setModel(parts[0].trim()); // 去除可能存在的空格
            device.setSn(parts[1].trim());

            // 添加到列表
            devices.add(device);
        }

        Result result = this.checkDevice(devices);
        if (!ResultUtils.isSuccess(result)) {
            return result;
        }

        // 创建新的线程执行MQTT账户生成
        new Thread(new Runnable() {
            @Override
            public void run() {
                // 在新线程中异步执行 initMqttAccounts 函数
                initMqttAccounts(devices);
            }
        }).start();
        this.batchInsert(devices);
        return Result.success();
    }


    public void remove(String sn) {
        // 设备MQTT显示在线不允许删除
        if (deviceIsOnline(sn)) {
            throw new IotException(ApiCode.DEVICE_IS_ONLINE);
        }

        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice != null) {
            throw new IotException(ApiCode.DEVICE_IS_IN_USE);
        }
        acsDeviceMapper.deleteBySn(sn);
    }

    public Result importDevice(MultipartFile file) {
        List<AcsDevice> uploadDevices = this.parseDeviceFile(file);
        Result result = this.checkDevice(uploadDevices);
        if (!ResultUtils.isSuccess(result)) {
            return result;
        }
        // 创建新的线程执行MQTT账户生成
        new Thread(new Runnable() {
            @Override
            public void run() {
                // 在新线程中异步执行 initMqttAccounts 函数
                initMqttAccounts(uploadDevices);
            }
        }).start();
        this.batchInsert(uploadDevices);
        return Result.success();
    }

    private List<AcsDevice> parseDeviceFile(MultipartFile file) {
        String filename = file.getOriginalFilename().toLowerCase();
        if (!filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            throw new IotException(ApiCode.DEVICE_FILE_TYPE_NOT_SUPPORT);
        }

        Workbook workbook = null;
        try {
            filename = file.getOriginalFilename();
            if (filename.endsWith(".xls")) {
                workbook = new HSSFWorkbook(file.getInputStream());
            } else if (filename.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(file.getInputStream());
            }
        } catch (Exception e) {
            log.error("File parse error, {}", filename, e);
            throw new IotException(ApiCode.DEVICE_FILE_PARSE_ERROR);
        }

        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum > 10000) {
            throw new IotException(ApiCode.DEVICE_FILE_EXCEED_MAX_COUNT);
        }

        List<AcsDevice> deviceList = Lists.newArrayList();
        for (int j = 1; j <= lastRowNum; j++) {
            Row row = sheet.getRow(j);
            String model = getCellValue(row, 0);
            String sn = getCellValue(row, 1);

            if (StringUtils.isAllBlank(model, sn)) {
                continue;
            }

            AcsDevice device = new AcsDevice();
            device.setModel(model);
            device.setSn(sn);
            deviceList.add(device);
        }

        return deviceList;
    }

    private String getCellValue(Row row, int i) {
        Cell cell = row.getCell(i);
        if (cell == null) {
            return null;
        }
        cell.setCellType(CellType.STRING);
        return row.getCell(i).getStringCellValue().trim();
    }

    private Result checkDevice(List<AcsDevice> devices) {
        for (AcsDevice device : devices) {
            if (StringUtils.isAnyBlank(device.getModel(), device.getSn())) {
                return ResultUtils.newResult.rightMsg(ApiCode.DEVICE_FILE_PARSE_ERROR,null,String.format("%s_%s", device.getModel(), device.getSn()));
            }
        }

        List<String> snList = devices.stream().map(e -> e.getSn()).collect(Collectors.toList());
        List<String> duplicationSnList = snList.stream()
                .collect(Collectors.toMap(e -> e, e -> 1, (a, b) -> a + b))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(duplicationSnList)) {
            return ResultUtils.newResult.rightMsg(ApiCode.DEVICE_FILE_DUP_SN,null,JSONObject.toJSONString(duplicationSnList));
        }

        List<String> existSnList = Lists.newArrayList();
        int total = snList.size();
        int batch = (total / 200) + 1;
        for (int i = 0; i < batch; i++) {
            List<AcsDevice> tmpList = acsDeviceMapper.getBySnList(snList.subList(i * 200, Integer.min(total, (i + 1) * 200)));
            if (!CollectionUtils.isEmpty(tmpList)) {
                List<String> sns = tmpList.stream().map(AcsDevice::getSn).collect(Collectors.toList());
                existSnList.addAll(sns);
            }
        }

        if (!CollectionUtils.isEmpty(existSnList)) {
            return ResultUtils.newResult.rightMsg(ApiCode.DEVICE_ALREADY_EXIST,null,JSONObject.toJSONString(existSnList));
        }
        return Result.success();
    }

    private void batchInsert(List<AcsDevice> uploadDevices) {
        int total = uploadDevices.size();
        int batch = (total / 200) + 1;
        for (int i = 0; i < batch; i++) {
            List<AcsDevice> subList = uploadDevices.subList(i * 200, Integer.min(total, (i + 1) * 200));
            acsDeviceMapper.bachInsert(subList);
        }
    }

    public Map<String, Object> search(DeviceFilter deviceFilter, Integer page, Integer size) {


        List<AcsDevice> devices = acsDeviceMapper.getByFilter(deviceFilter);
        int total = acsDeviceMapper.countByFilter(deviceFilter);

        return PageUtils.toPageResult(total, page, size, devices);

    }

    public List<AcsDevice> getByFilter(DeviceFilter deviceFilter) {
        return acsDeviceMapper.getByFilter(deviceFilter);
    }

    public int deleteByPrimaryKey(Integer id) {
        return acsDeviceMapper.deleteByPrimaryKey(id);
    }

    public int insert(AcsDevice record) {
        return acsDeviceMapper.insert(record);
    }

    public int insertSelective(AcsDevice record) {
        return acsDeviceMapper.insertSelective(record);
    }


    public AcsDevice selectByPrimaryKey(Integer id) {
        return acsDeviceMapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(AcsDevice record) {
        return acsDeviceMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(AcsDevice record) {
        return acsDeviceMapper.updateByPrimaryKey(record);
    }

    /**
     * 获取设备在线信息
     *
     * @return 设备在线、离线及总数量
     */
    public DeviceOnlineInfo getDeviceOnlineInfo() {
        // 当前登入用户
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        DeviceOnlineInfo deviceOnlineInfo = new DeviceOnlineInfo();

        int userId = 1;
        List<AcsUserDevice> list = null;
        if (currentUser.getRole() == RoleEnum.USER_ADMIN.role) {
            userId = currentUser.getId();
            list = acsUserDeviceService.getByUserId(userId);
        } else if (currentUser.getRole() == RoleEnum.USER.role) {
            userId = currentUser.getParentId();
            list = acsUserDeviceService.getByUserId(userId);
        } else if (currentUser.getRole() == RoleEnum.SUPER_ADMIN.role) {
            list = acsUserDeviceService.selectAllNotDelete();

        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return deviceOnlineInfo;
        }
        deviceOnlineInfo.setTotal(list.size());
        long now = DateTime.now().getMillis();
        list = list.stream().filter(l -> (now - l.getLastHbTime().getTime()) / 1000 <= heartBeat).collect(Collectors.toList());
        deviceOnlineInfo.setOnline(list.size());
        deviceOnlineInfo.setOffline(deviceOnlineInfo.getTotal() - deviceOnlineInfo.getOnline());

        return deviceOnlineInfo;
    }

    /**
     * 查找设备是否在线
     *
     * @param sn 设备sn号
     * @return 在线：true
     */
    public boolean deviceIsOnline(String sn) {
        String value = redisService.get(sn, RedisDatabaseEnum.ONLINE_STATUS.getKey());
        return !StringUtils.isEmpty(value);
    }

    /**
     *  分配多个设备
     * @param deviceIds 设备ID列表
     * @param username 被分配的租户管理员
     */
    public Result allocateMultiDevice(String deviceIds, String username) {
        List<Integer> deviceIdList = Splitter.on(",").splitToList(deviceIds)
                .stream()
                .map(e->Integer.valueOf(e))
                .collect(Collectors.toList());

        List<AcsDevice> acsDevices = acsDeviceMapper.getUnregisteredDeviceInUse(deviceIdList);
        if (acsDevices != null && acsDevices.size() > 0) {
            log.error("AcsDeviceService allocateMultiDevice 分配设备错误，设备已激活!");
            return Result.toResult(ApiCode.DEVICE_ALREADY_ACTIVATED);
        }

        UserFilter userFilter = new UserFilter();
        userFilter.setName(username);
        userFilter.setRole(1);
        List<AcsUser> acsUsers = acsUserService.findList(userFilter);
        if (acsUsers == null || acsUsers.size() <= 0) {
            log.error("AcsDeviceService allocateMultiDevice 分配设备错误，用户不是租户管理员!");
            return Result.toResult(ApiCode.USER_NOT_CUSTOMER_ADMIN);
        }

        acsDeviceMapper.updateByPrimaryKeys(deviceIdList, acsUsers.get(0).getId());
        return Result.success();
    }

    /**
     *  初始化MQTT密码，用来支持新网关连接注册
     * @param sn 设备SN
     */
    public void initMqttAccount(String sn) {
        try {
            String[] parts = mqttBroker.split("//|:");
            String mqttIp = parts[2];
            String mqttPort = parts[3];
            mqttIp = org.springframework.util.StringUtils.isEmpty(System.getenv("SERVER_IP")) ? mqttIp : System.getenv("SERVER_IP");
            String message = MESSAGE_256 + "+" + sn + "+" + mqttIp + ":" + mqttPort;
            String key = sn + "+" + KEY_256;
            String password = EncryptUtils.hmacSha256(message, key);
            acsUserDeviceService.createMqttUserForGateway(sn, password);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initMqttAccounts(List<AcsDevice> list) {
        log.warn("****************************************************");
        log.warn("开始导入文件，生成mqtt密码!");
        log.warn("****************************************************");
        if (list == null || list.size() <= 0) {
            log.error("导入的文件有错误!");
            return;
        }

        for (AcsDevice dev: list) {
            initMqttAccount(dev.getSn());
        }
    }

    public Result reActivate(String sn, Integer id) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice != null) {
            log.error("AcsDeviceService Reactivate 重置密码错误，设备已激活!");
            return Result.toResult(ApiCode.DEVICE_ALREADY_ACTIVATED);
        }

        mqttMessageHandler.handlerReactivateMessage(sn);
        AcsDevice acsDevice = selectByPrimaryKey(id);
        if (acsDevice.getAllocateUserId() == null || acsDevice.getAllocateUserId() < 0) {
            return Result.toResult(ApiCode.FAIL);
        }
        acsDevice.setGmtModify(new Date());
        updateByPrimaryKeySelective(acsDevice);
        // 重置MQTT密码
        initMqttAccount(sn);
        return Result.success();
    }

    public Result getAllocatedDevices(String company) {
        List<AcsDevice> deviceList;
        // 根据company区分处理逻辑
        if ("jetron".equals(company)) {
            // 如果是jetron公司，直接查找allocate_user_id不等于-1的设备
            deviceList = acsDeviceMapper.findDevicesWithAllocatedUsers();
        } else {
            // 如果不是jetron公司，先查找该公司的所有用户
            UserFilter filter = new UserFilter();
            filter.setCompany(company);
            List<AcsUser> companyUsers = acsUserService.findList(filter);
            if (companyUsers == null || companyUsers.isEmpty()) {
                return null;
            }
            
            // 获取所有用户ID
            List<Integer> userIds = companyUsers.stream()
                    .map(AcsUser::getId)
                    .collect(Collectors.toList());
            
            // 根据用户ID查找对应的设备
            deviceList = acsDeviceMapper.findDevicesByUserIds(userIds);
        }
        // 处理设备列表，将敏感字段设置为0，防止信息泄露
        if (deviceList != null && !deviceList.isEmpty()) {
            deviceList.forEach(device -> {
                device.setAllocateUserId(0);
                device.setId(0);
            });
        }
        return ResultUtils.success(deviceList);
    }
}
