package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.FirmwareUpgradeStatus;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.FirmwareUpgradeInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsFirmwareUpgradeMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AcsFirmwareUpgradeService {

    @Resource
    private AcsFirmwareUpgradeMapper acsFirmwareUpgradeMapper;
    @Resource
    private AcsFirmwareService acsFirmwareService;
    @Resource
    private AcsUserDeviceService acsUserDeviceService;

    public List<FirmwareUpgradeInfo> getUpgradeRecords(Integer firmwareId) {
        AcsFirmware acsFirmware = acsFirmwareService.selectByPrimaryKey(firmwareId);
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (acsFirmware == null ||
                (acsFirmware.getType() != 0 && !Objects.equals(acsFirmware.getBelongTo(), currentUser.getId()))) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }

        UpgradeFilter filter = new UpgradeFilter();
        filter.setId(firmwareId);
        if (!MdcUtils.isAdmin()) {
            filter.setBelongTo(currentUser.getId());
        }
        List<AcsFirmwareUpgrade> upgradeList = acsFirmwareUpgradeMapper.getByFilter(filter);
        return toInfo(upgradeList);
    }

    public Map<String, Object> page(UpgradeFilter filter, Integer page, Integer size) {
        AcsFirmware acsFirmware = acsFirmwareService.selectByPrimaryKey(filter.getId());
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (acsFirmware == null ||
                (acsFirmware.getType() != 0 && !Objects.equals(acsFirmware.getBelongTo(), currentUser.getId()))) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }
        if (!MdcUtils.isAdmin()) {
            filter.setBelongTo(currentUser.getId());
        }
        filter.setLimit(size);
        filter.setOffset((page - 1) * size);
        filter.setValid(1);
        filter.setOrderByDesc(Constants.YES);
        List<AcsFirmwareUpgrade> upgradeList = acsFirmwareUpgradeMapper.getByFilter(filter);
        int count = acsFirmwareUpgradeMapper.count(filter);
        List<FirmwareUpgradeInfo> upgradeInfos = toInfo(upgradeList);
        return PageUtils.toPageResult(count, page, size, upgradeInfos);
    }

    private List<FirmwareUpgradeInfo> toInfo(List<AcsFirmwareUpgrade> upgradeList) {
        if (CollectionUtils.isEmpty(upgradeList)) {
            return Lists.newArrayList();
        }

        List<Integer> deviceIds = upgradeList.stream()
                .map(AcsFirmwareUpgrade::getDeviceId).collect(Collectors.toList());

        List<AcsUserDevice> deviceList = acsUserDeviceService.getByDeviceIds(deviceIds);
        Map<Integer, AcsUserDevice> deviceMap = deviceList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));

        List<FirmwareUpgradeInfo> infoList = Lists.newArrayList();

        for (AcsFirmwareUpgrade upgrade : upgradeList) {
            FirmwareUpgradeInfo info = new FirmwareUpgradeInfo();
            BeanUtils.copyProperties(upgrade, info);

            Integer deviceId = upgrade.getDeviceId();
            AcsUserDevice device = deviceMap.get(deviceId);
            if (device == null) {
                continue;
            }
            info.setName(device.getName());
            info.setSn(device.getSn());
            info.setVersion(device.getVerNo());

            infoList.add(info);
        }

        return infoList;
    }

    public List<AcsFirmwareUpgrade> findList(UpgradeFilter filter){
        if (filter == null) {
            filter = new UpgradeFilter();
        }
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (!MdcUtils.isAdmin()) {
            filter.setBelongTo(currentUser.getId());
        }
        List<AcsFirmwareUpgrade> upgradeList = acsFirmwareUpgradeMapper.getByFilter(filter);
        return upgradeList;
    }

    public AcsFirmwareUpgrade getLatestUpgrade(String sn) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        return acsFirmwareUpgradeMapper.getLatestUpgrade(acsUserDevice.getId());
    }

    public int deleteByPrimaryKey(Integer id) {
        return acsFirmwareUpgradeMapper.deleteByPrimaryKey(id);
    }


    public int insert(AcsFirmwareUpgrade record) {
        return acsFirmwareUpgradeMapper.insert(record);
    }


    public int insertSelective(AcsFirmwareUpgrade record) {
        return acsFirmwareUpgradeMapper.insertSelective(record);
    }


    public AcsFirmwareUpgrade selectByPrimaryKey(Integer id) {
        return acsFirmwareUpgradeMapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(AcsFirmwareUpgrade record) {
        return acsFirmwareUpgradeMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(AcsFirmwareUpgrade record) {
        record.setGmtModify(new Date());
        return acsFirmwareUpgradeMapper.updateByPrimaryKey(record);
    }

    public List<AcsFirmwareUpgrade> getTimeout(Date deadline) {
        return acsFirmwareUpgradeMapper.getTimeout(deadline);
    }

    public int timeout(List<Integer> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return 0;
        }
        return acsFirmwareUpgradeMapper.timeout(deviceIds);
    }

    @Transactional
    public void deleteFirmwareUpgradeByFirmwareId(Integer firmwareId) {
        AcsFirmware firmware = acsFirmwareService.selectByPrimaryKey(firmwareId);

        if (firmware == null) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }
        UpgradeFilter filter = new UpgradeFilter();
        filter.setId(firmwareId);
        List<AcsFirmwareUpgrade> list = findList(filter);
        List<Integer> userDeviceIdList = list.stream()
                .map(AcsFirmwareUpgrade::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userDeviceIdList)) {
            acsUserDeviceService.setUpgradeStatus(userDeviceIdList,0);
        }
        int count1 = acsFirmwareUpgradeMapper.deleteByFirmwareId(firmwareId);
       

    }

    @Transactional
    public void completeUpgrade(Integer id) {
        AcsFirmwareUpgrade upgrade = selectByPrimaryKey(id);
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (upgrade == null) {
            throw new IotException(ApiCode.FIRMWARE_NOT_EXIST);
        }
        /*if (!MdcUtils.isAdmin() && !Objects.equals(upgrade.getBelongTo(), currentUser.getId())) {
            throw new IotException(ApiCode.FIRMWARE_VALID_USER);
        }*/
        Integer userDeviceId = upgrade.getDeviceId();
        AcsUserDevice acsUserDevice = acsUserDeviceService.selectByPrimaryKey(userDeviceId);
        if (acsUserDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        upgrade.setStatus(FirmwareUpgradeStatus.SUCCESS.status);
        acsUserDevice.setUpgradeStatus(0);

        updateByPrimaryKey(upgrade);
        acsUserDeviceService.updateByPrimaryKey(acsUserDevice);
    }

}
