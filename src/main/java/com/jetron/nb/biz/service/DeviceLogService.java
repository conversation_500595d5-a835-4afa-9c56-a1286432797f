package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.common.vo.UserDeviceInfo;
import com.jetron.nb.dal.po.AcsUserDevice;
import com.jetron.nb.dal.po.UserDeviceFilter;
import com.jetron.nb.web.controller.log.DeviceLogController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceLogService {

    public static String COMPRESS_TYPE = ".tar.gz";

    @Value("${app.deployType}")
    private String deployType;
    @Value("${app.output}")
    private String appDir;
    @Autowired
    private AcsUserDeviceService userDeviceService;


    public Result list(DeviceLogController.DeviceLogFilter param, Integer page, Integer size) throws Exception {
        UserDeviceFilter userDeviceFilter = new UserDeviceFilter();
        userDeviceFilter.setSnEq(param.getSn());
        List<AcsUserDevice> list = userDeviceService.findList(userDeviceFilter);
        AcsUserDevice device = list.get(0);
        String path = device.getPath();
        if (StringUtils.isBlank(path)) {
            return Result.toResult(ApiCode.NO_FILE);
        }
        InputStream stream = getStream(path);
        if (stream == null) {
            return Result.toResult(ApiCode.FILE_NOT_EXIST);
        }
        JSONObject read = read(stream, param, page, size);
        JSONArray array = read.getJSONArray("list");
        read.remove("list");
        return Result.success(PageUtils.toPageResult(read.getInteger("lines"), page, size,array,read));
    }

    public Result statistics(DeviceLogController.DeviceLogFilter logFilter, Integer page, Integer size) throws Exception {
        logFilter.setLevel("ERROR");
        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        deviceFilter.setHavePath(1);
        Map<String, Object> search = userDeviceService.search(deviceFilter, null, 1, 1000);
        List<UserDeviceInfo> records = JSONArray.parseArray(JSONObject.toJSONString(search.get("records")), UserDeviceInfo.class);
        if (StringUtils.isNotBlank(logFilter.getModel())) {
            records = records.stream().filter(obj-> logFilter.getModel().equals(obj.getModel())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(records)) {
            return Result.success();
        }
        Map<String, List<UserDeviceInfo>> collect = records.stream().collect(Collectors.groupingBy(UserDeviceInfo::getModel));
        List<String> modelList =  new ArrayList<>(collect.keySet());
        Collections.sort(modelList);
        List<DeviceLogController.ModelError> modelErrorList = new ArrayList<>();
        for (int i = 0;i < modelList.size();i++) {
            String model = modelList.get(i);
            List<UserDeviceInfo> deviceInfoList = collect.get(model);
            deviceInfoList = deviceInfoList.stream().sorted(Comparator.comparing(UserDeviceInfo::getSn)).collect(Collectors.toList());
            JSONObject allErr = new JSONObject();
            for (int k = 0; k < deviceInfoList.size(); k++) {
                UserDeviceInfo deviceInfo = deviceInfoList.get(k);
                String path = deviceInfo.getPath();
                InputStream stream = getStream(path);
                if (stream == null) {
                    continue;
                }
                JSONObject read = read(stream, logFilter, null, null);
                JSONObject errorMap = read.getJSONObject("errorMap");
                for (String err : errorMap.keySet()) {
                    if (allErr.containsKey(err)) {
                        int v1 = allErr.getIntValue(err);
                        int v2 = errorMap.getIntValue(err);
                        allErr.put(err,v1 + v2);
                    } else {
                        allErr.put(err,errorMap.getIntValue(err));
                    }
                }
            }
            for (String err : allErr.keySet()) {
                DeviceLogController.ModelError modelError = new DeviceLogController.ModelError();
                modelError.setModel(model);
                modelError.setError(err);
                modelError.setNum(allErr.getInteger(err));
                modelErrorList.add(modelError);
            }
        }

        modelErrorList = modelErrorList.stream().sorted(Comparator.comparing(DeviceLogController.ModelError::getModel)
        .thenComparing(DeviceLogController.ModelError::getNum).reversed()).collect(Collectors.toList());
        int end = page * size > modelErrorList.size() ? modelErrorList.size() : page * size;
        List<DeviceLogController.ModelError> list = modelErrorList.subList((page - 1) * size, end);
        return Result.success(PageUtils.toPageResult(modelErrorList.size(),page,size,list));
    }

    public  JSONObject read(InputStream inputStream, DeviceLogController.DeviceLogFilter logFilter, Integer page, Integer size) {
        InputStreamReader inputStreamReader = null;
        BufferedReader br = null;
        String line = null;
        List<JSONObject> list = new ArrayList<>();
        List<String> dateList = new ArrayList<>();
        List<Integer> numList = new ArrayList<>();
        Map<String, Integer> errorMap = new HashMap<>();
        long lines = 0;
        try {
            if ("local".equals(deployType)) {
                inputStreamReader = new InputStreamReader(inputStream);
                br = new BufferedReader(inputStreamReader);
            } else {
                inputStreamReader = new InputStreamReader(inputStream);
                br = new BufferedReader(inputStreamReader);
            }
            line = br.readLine();
            int lineNum = 0;
            while (line != null) {
                lineNum++;
                // 防止日志出现不规则的信息比如： "\n fail"等
                if (line.trim().length() <= 20 || !(line.contains("[") && line.contains("]"))) {
                    line = br.readLine();
                    continue;
                }
                String temp = line.substring(line.indexOf("]") + 1);
                String lLevel = temp.substring(temp.indexOf("[") + 1, temp.indexOf("]"));
                if (StringUtils.isNotBlank(logFilter.getContent())) {
                    if (!line.contains(logFilter.getContent())) {
                        line = br.readLine();
                        continue;
                    }
                }
                if (StringUtils.isNotBlank(logFilter.getLevel())) {
                    if (!StringUtils.equalsIgnoreCase(lLevel, logFilter.getLevel())) {
                        line = br.readLine();
                        continue;
                    }
                }
                if (StringUtils.isNotBlank(logFilter.getStart())) {
                    String time = line.substring(0, line.indexOf("."));
                    if (StringUtils.compare(logFilter.getStart(), time) > 0
                            || StringUtils.compare(time, logFilter.getEnd()) > 0) {
                        line = br.readLine();
                        continue;
                    }
                }
                lines++;
                if (page != null && size != null) {
                    if (lines < (page - 1) * size) {
                        line = br.readLine();
                        continue;
                    }
                    if (lines > page * size) {
                        line = br.readLine();
                        continue;
                    }
                }
                if ("ERROR".equalsIgnoreCase(lLevel)) {
                    String hour = line.substring(0, line.indexOf(":"));
                    String error = line.substring(line.indexOf(lLevel) + 8).trim();
                    if (dateList.contains(hour)) {
                        int index = numList.size() - 1;
                        Integer num = numList.get(index);
                        numList.remove(index);
                        numList.add(index, ++num);
                    } else {
                        dateList.add(hour);
                        numList.add(1);
                    }
                    if (errorMap.containsKey(error)) {
                        errorMap.put(error, errorMap.get(error) + 1);
                    } else {
                        errorMap.put(error, 1);
                    }
                }
                JSONObject row = new JSONObject();
                row.put("content",line);
                list.add(row);
                line = br.readLine();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(""+e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (inputStreamReader != null) {
                try {
                    inputStreamReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("dateList", dateList);
        result.put("numList", numList);
        result.put("errorMap", errorMap);
        result.put("lines", lines);
        return result;
    }

    public InputStream getStream(String path) throws Exception {
        // 上传功能bug，本地版会导致上传到linux系统 /null 目录下（LocalFileClient无法获取{app.output}配置值）
        if (("local".equals(deployType))) {
            appDir = "null";
            //appDir = "d:/tmp";
        }
        if (path.contains(COMPRESS_TYPE)) {
            String dir = appDir + "/" + FilenameUtils.getFullPath(path) +
                    FilenameUtils.getBaseName(FilenameUtils.getBaseName(path));
            File dirFile = new File(dir);
            if (!dirFile.exists()) {
                try {
                    // 如果没有文件名相同的目录，则需要解压
                    doUncompressLog(path);
                    // 将所有日志文件合并为一个文件。其余日志文件删除
                    mergeFile(dirFile);
                } catch (Exception e) {
                    log.error("[DeviceLogService#getStream] exception, message: {}", e);
                    return null;
                }
            }
            File[] files = dirFile.listFiles();
            if (files == null || files.length == 0) {
                return null;
            }
            return new FileInputStream(files[0]);
        }
        if ("local".equals(deployType)) {
            path = appDir + "/" + path;
            File file = new File(path);
            if (!file.exists()) {
                return null;
            }
            return new FileInputStream(file);
        } else {
            try {
                return FileClientFactory.getFileClient("net").getFileStream(path);
            } catch (Exception e) {
                log.error("[DeviceLogService#getStream] exception, message: {}", e);
                return null;
            }
        }
    }

    public void doUncompressLog(String sourcePath) throws Exception {
        // 要解压到的目录
        String extractPath = appDir + "/" + FilenameUtils.getFullPath(sourcePath) + FilenameUtils.getBaseName(FilenameUtils.getBaseName(sourcePath));
        InputStream inputStream = null;
        TarArchiveInputStream fin = null;
        try {
            if ("local".equals(deployType)) {
                sourcePath = appDir + "/" + sourcePath;
                inputStream = new FileInputStream(sourcePath);
            } else {
                inputStream =  FileClientFactory.getFileClient("net").getFileStream(sourcePath);
            }
            fin = new TarArchiveInputStream(new GzipCompressorInputStream(inputStream));
            TarArchiveEntry entry;
            // 将 tar 文件解压到 extractPath 目录下
            while ((entry = fin.getNextTarEntry()) != null) {
                if (entry.isDirectory()) {//是目录
                    continue;
                } else {//是文件
                    if (!(entry.getName().contains("log/app_log") || entry.getName().contains("config/log"))) {
                        continue;
                    }
                    String fileName = FilenameUtils.getBaseName(entry.getName());
                    if (fileName.contains(":")) {
                        fileName = fileName.replace(":", "_");
                    }
                    File tmpFile = new File(extractPath + "/" + fileName);
                    if (!tmpFile.getParentFile().exists()) {
                        tmpFile.getParentFile().mkdirs();
                    }
                    OutputStream out = null;
                    try {
                        out = new FileOutputStream(tmpFile);
                        int length = 0;
                        byte[] b = new byte[2048];
                        while ((length = fin.read(b)) != -1) {
                            out.write(b, 0, length);
                        }
                    } catch (IOException ex) {
                        log.error("" + ex);
                        throw ex;
                    } finally {
                        if (out != null) {
                            out.close();
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("" + e);
        } finally {
            try {
                if (fin != null) {
                    fin.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void mergeFile(File dirFile) throws IOException {
        File[] logFiles = dirFile.listFiles();
        if (logFiles == null || logFiles.length == 0) {
            return;
        }
        List<File> collect = Arrays.stream(logFiles).sorted((file1, file2) -> file1.getName().compareToIgnoreCase(file2.getName())).collect(Collectors.toList());
        File file = collect.get(0);
        OutputStream out = new FileOutputStream(file, true);
        for (int i = 1; i < collect.size(); i++) {
            File temp = collect.get(i);
            InputStream inputStream = new FileInputStream(temp);
            int length = 0;
            byte[] b = new byte[2048];
            while ((length = inputStream.read(b)) != -1) {
                out.write(b, 0, length);
            }
            inputStream.close();
            temp.delete();
        }
        out.close();
    }

}


