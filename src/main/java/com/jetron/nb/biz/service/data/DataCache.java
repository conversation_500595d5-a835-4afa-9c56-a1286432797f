package com.jetron.nb.biz.service.data;

import com.jetron.nb.dal.po.AcsCaptureAlarm;
import com.jetron.nb.dal.po.AcsCaptureData;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Component
public class DataCache {
    public static Queue<AcsCaptureAlarm> alarmsQueue = new ConcurrentLinkedQueue<AcsCaptureAlarm>();
    public static Queue<AcsCaptureData> dataQueue = new ConcurrentLinkedQueue<AcsCaptureData>();
    public static Date flag = new Date();
}
