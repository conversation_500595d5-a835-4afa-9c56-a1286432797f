package com.jetron.nb.biz.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.dal.dao.AcsDriverUpgradeMapper;
import com.jetron.nb.dal.dao.AcsRealDataMapper;
import com.jetron.nb.dal.po.AcsCaptureData;
import com.jetron.nb.dal.po.AcsDriverUpgrade;
import com.jetron.nb.dal.po.AcsRealData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AcsRealDataService {

    @Autowired
    private AcsRealDataMapper mapper;

    public QueryWrapper<AcsRealData> wrapper(AcsRealData param) {
        QueryWrapper<AcsRealData> wrapper = new QueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.eq("company",param.getCompany());
        }
        if (param.getSn() != null) {
            wrapper.eq("sn",param.getSn());
        }
        if (!CollectionUtils.isEmpty(param.getSnList())) {
            wrapper.in("sn",param.getSnList());
        }
        return wrapper;
    }



    public AcsRealData find(AcsRealData param) {
        return mapper.selectOne(wrapper(param));
    }


    public List<AcsRealData> list(AcsRealData param) {
        return mapper.selectList(wrapper(param));
    }

    public void add(AcsRealData param) {
        mapper.insert(param);
    }

    @Transactional
    public void insertList(List<AcsRealData> param) {
        mapper.insertList(param);
    }

    @Transactional
    public void updateList(List<AcsRealData>  param) {
        for (int i = 0; i < param.size(); i++) {
            AcsRealData acsRealData = param.get(i);
            mapper.updateById(acsRealData);
        }
    }

    @Transactional
    public void updateRealData(List<AcsCaptureData> list) {
        Map<String, List<AcsCaptureData>> collect1 =
                list.stream().collect(Collectors.groupingBy(AcsCaptureData::getSn));
        List<AcsCaptureData> maxList = new ArrayList<>();
        for (String sn:collect1.keySet()) {
            List<AcsCaptureData> snList = collect1.get(sn);
            AcsCaptureData max = snList.stream()
                    .max(Comparator.comparing(AcsCaptureData::getInsertTime)).get();
            maxList.add(max);
        }
        List<String> snList = maxList.stream().map(AcsCaptureData::getSn)
                .collect(Collectors.toList());

        List<AcsRealData> tableList = list(new AcsRealData().setSnList(snList));
        List<String> snList2 = tableList.stream().map(AcsRealData::getSn)
                .collect(Collectors.toList());
        Map<String, AcsRealData> collect2 = tableList.stream()
                .collect(Collectors.toMap(AcsRealData::getSn, obj -> obj));

        List<AcsRealData> addList = new ArrayList<>();
        List<AcsRealData> updateList = new ArrayList<>();
        Date now = new Date();

        for (int i = 0; i < maxList.size(); i++) {
            AcsCaptureData data = maxList.get(i);
            String sn = data.getSn();
            if (snList2.contains(sn)) {
                AcsRealData acsRealData = collect2.get(sn);

                AcsRealData realData = JSONObject.parseObject(JSONObject.toJSONString(data), AcsRealData.class);
                realData.setId(acsRealData.getId());
                realData.setInsertTime(acsRealData.getInsertTime());
                realData.setUpdateTime(now);
                updateList.add(realData);
            } else {
                AcsRealData realData = JSONObject.parseObject(JSONObject.toJSONString(data), AcsRealData.class);
                realData.setInsertTime(now);
                addList.add(realData);
            }
        }
        if (addList.size() > 0) {
            insertList(addList);
        }
        if (updateList.size() > 0) {
            updateList(updateList);
        }
    }

    public void deleteList(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        mapper.deleteBatchIds(list);
    }


}
