package com.jetron.nb.biz.service.mqtt;

import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.mqtt.AcsMqttOnlineMapper;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AcsMqttOnlineService {
    @Resource
    private AcsMqttOnlineMapper acsMqttOnlineMapper;

    public int insert(AcsMqttOnline acsMqttOnline) {
        return acsMqttOnlineMapper.insert(acsMqttOnline);
    }

    public List<AcsMqttOnline> findList(AcsMqttOnline acsMqttOnline) {
        return acsMqttOnlineMapper.findList(acsMqttOnline);
    }

    public List<AcsMqttOnline> findList2(AcsMqttOnline acsMqttOnline) {
        String sn = acsMqttOnline.getSn();
        if (StringUtils.isNotBlank(sn)) {
            acsMqttOnline.setSn(Constants.MQTT_ONLINE_SUB_PREFIX + sn);
        }
        List<AcsMqttOnline> list = acsMqttOnlineMapper.findPage(acsMqttOnline);
        if (CollectionUtils.isEmpty(list) && StringUtils.isNotBlank(sn)) {
            acsMqttOnline.setSn(sn);
            list = acsMqttOnlineMapper.findPage(acsMqttOnline);
        }
        return list;
    }

    public int findCount(AcsMqttOnline acsMqttOnline) {
        String sn = acsMqttOnline.getSn();
        if (StringUtils.isNotBlank(sn)) {
            acsMqttOnline.setSn(Constants.MQTT_ONLINE_SUB_PREFIX + sn);
        }
        int count = acsMqttOnlineMapper.findCount(acsMqttOnline);
        if (count == 0) {
            acsMqttOnline.setSn(sn);
            count = acsMqttOnlineMapper.findCount(acsMqttOnline);
        }
        return count;
    }

    public AcsMqttOnline findLatest(String sn, Date begin) {
        return acsMqttOnlineMapper.findLatest(sn,begin);
    }

    public Map<String, Object> onOffLineTable(Integer page, Integer size, AcsMqttOnline param) {
        param.setLimit(size);
        param.setOffset((page - 1) * size);
        List<AcsMqttOnline> list = findList2(param);
        int count = this.findCount(param);
        // 查询所有数
        return PageUtils.toPageResult(count, page, size, list);
    }

    public void delete(AcsMqttOnline param) {
        acsMqttOnlineMapper.delete(param);
    }
}
