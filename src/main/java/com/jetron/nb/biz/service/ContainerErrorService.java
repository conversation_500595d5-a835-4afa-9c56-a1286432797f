package com.jetron.nb.biz.service;


import com.jetron.nb.dal.dao.ContainerErrorMapper;
import com.jetron.nb.dal.po.ContainerError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ContainerErrorService {

    @Autowired
    private ContainerErrorMapper mapper;

    public List<ContainerError> findList() {
        return mapper.findList();
    }
}
