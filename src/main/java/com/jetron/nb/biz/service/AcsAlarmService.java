package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.constant.RedisKey;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.DateUtils;
import com.jetron.nb.common.util.ExcelUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.AcsUserDeviceMapper;
import com.jetron.nb.dal.dao.mqtt.AcsCaptureAlarmMapper;
import com.jetron.nb.dal.po.AcsAlarmFilter;
import com.jetron.nb.dal.po.AcsCaptureAlarm;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.BeanUtilsBean2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.hash.BeanUtilsHashMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AcsAlarmService {
    @Resource
    private AcsCaptureAlarmMapper acsCaptureAlarmMapper;
    @Resource
    private AcsUserDeviceMapper acsUserDeviceMapper;
    @Autowired
    private MqttClientManager mqttClientManager;
    @Autowired
    AcsTopicService topicService;
    @Autowired
    RedisServiceImpl redisService;

    @Autowired
    private AcsUserDeviceService userDeviceService;

    public List<AcsCaptureAlarm> findList(AcsAlarmFilter alarm) {
        return acsCaptureAlarmMapper.findList(alarm);
    }

    public int findCount(AcsAlarmFilter alarm) {
        return acsCaptureAlarmMapper.findCount(alarm);
    }

    public Map<String, Object> findWithDeviceList(AcsAlarmFilter alarm, Integer page, Integer size) {
        alarm.setOffset((page - 1)* size);
        alarm.setLimit(size);
        List<AcsCaptureAlarm> list = acsCaptureAlarmMapper.findList(alarm);
        List<AcsUserDevice> userDevices = acsUserDeviceMapper.selectAllNotDelete();
        for (AcsCaptureAlarm a : list) {
            List<AcsUserDevice> acsUserDevices = userDevices.stream().filter( x -> x.getSn().equals(a.getSn()) && x.getDel() == 0)
                    .collect(Collectors.toList());
            if (acsUserDevices.size() > 0) {
                a.setAcsUserDevice(acsUserDevices.get(0));
            }
        }
        int count = acsCaptureAlarmMapper.findCount(alarm);
        return PageUtils.toPageResult(count, page, size, list);
    }

    public List<AcsCaptureAlarm> findWithDeviceList(AcsAlarmFilter alarm) {
        List<AcsCaptureAlarm> list = acsCaptureAlarmMapper.findWithDeviceList(alarm);
        return list;
    }

    public List<AcsCaptureAlarm> findActiveAlarm(AcsCaptureAlarm alarm) {
        List<AcsCaptureAlarm> list = acsCaptureAlarmMapper.findActiveAlarm(alarm);
        return list;
    }

    public void excel(HttpServletRequest request, HttpServletResponse response,AcsAlarmFilter alarm) throws IOException {
        List<AcsCaptureAlarm> list = acsCaptureAlarmMapper.findWithDeviceList(alarm);
        JSONArray array = JSONArray.parseArray(JSONObject.toJSONString(list));
        List<Map<String, Object>> list2 = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            AcsCaptureAlarm alarm1 = list.get(i);
            Map<String, Object> map = (Map<String, Object>)JSONObject.parseObject(JSONObject.toJSONString(alarm1), Map.class);
            map.put("insertTime",DateFormatUtils.format(alarm1.getInsertTime(), "yyyy-MM-dd HH:mm:ss"));
            list2.add(map);
        }

        String[] keys = new String[]{"sn","alarmRank","alarmStatus","parameterCode"
                ,"parameterValue","parameterKey","parameterContent","currentValue","ip"
                ,"insertTime"
        };
        String[] head = new String[]{"序列号","报警等级: 严重/4、主要/3、次要/2、警告/1"
                ,"报警状态: 激活/1，确认/2，清除/3","参数项代码"
                ,"参数值","型号","报警参数","当前值","互联网协议地址"
                ,"创建时间"
        };
        ExcelUtils.exportExcel(response,request,list2,keys,head,"报警");
    }



    public Map<String,Object> kanban(AcsAlarmFilter alarm) {
        Map<String,Object> dataMap = new HashMap<>();
        // 查询近日所有报警的设备
        alarm.setAlarmStatus(null);
        List<AcsCaptureAlarm> list = findList(alarm);
        List<AcsCaptureAlarm> deduplicationData = filterDuplicateData(list);
        // 查询所有当前激活的异常
        List<AcsCaptureAlarm> latest = findCurrentActiveAlarm(deduplicationData);
        // 过滤重复数据
        List<String> snList = list.stream().map(AcsCaptureAlarm::getSn).distinct().collect(Collectors.toList());

        String format = DateFormatUtils.format(new Date(), "yyyy-MM-dd");

        Integer cpu = 0,ram  = 0,tem  = 0,rssi  = 0,quality  = 0,todayNum = 0;
        // 获取某个设备的今日报警数，相加得到总报警数
        for (int i = 0; i < snList.size(); i++) {
            String sn = snList.get(i);
            String cpuKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, sn, Constants.PamameterCode.CPU,format);
            String ramKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, sn, Constants.PamameterCode.RAM,format);
            String temKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, sn, Constants.PamameterCode.TEMPTURE,format);
            String rssiKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, sn, Constants.PamameterCode.RSSI,format);
            String qualityKey = String.format("%s_%s_%s_%s", RedisKey.ALARM_TODAY.key, sn, Constants.PamameterCode.QUALITY,format);
            String cpuStr = redisService.get(cpuKey);
            String ramStr = redisService.get(ramKey);
            String temStr = redisService.get(temKey);
            String rssiStr = redisService.get(rssiKey);
            String quaStr = redisService.get(qualityKey);
            Integer cpuTemp = StringUtils.isBlank(cpuStr) ? 0 : Integer.parseInt(cpuStr);
            Integer ramTemp = StringUtils.isBlank(ramStr) ? 0 : Integer.parseInt(ramStr);
            Integer temTemp = StringUtils.isBlank(temStr) ? 0 : Integer.parseInt(temStr);
            Integer rssiTemp = StringUtils.isBlank(rssiStr) ? 0 : Integer.parseInt(rssiStr);
            Integer qualityTemp = StringUtils.isBlank(quaStr) ? 0 : Integer.parseInt(quaStr);
            cpu = cpu + (cpuTemp == null ? 0 : cpuTemp);
            ram = ram + (ramTemp == null ? 0 : ramTemp);
            tem = tem + (temTemp == null ? 0 : temTemp);
            rssi = rssi + (rssiTemp == null ? 0 : rssiTemp);
            quality = quality + (qualityTemp == null ? 0 : qualityTemp);
        }

        dataMap.put("CpuRation",cpu);
        dataMap.put("ram",ram);
        dataMap.put("DevTemp",tem);
        dataMap.put("rssi",rssi);
        dataMap.put("SignalQuality",quality);
        dataMap.put("todayNum",cpu + ram + tem + rssi + quality);
        dataMap.put("latest",latest);
        return dataMap;
    }

    // 过滤重复数据
    private List<AcsCaptureAlarm> filterDuplicateData(List<AcsCaptureAlarm> latest) {
        if (CollectionUtils.isEmpty(latest)) {
            return latest;
        }
        // 过滤sn和code重复的
        List<AcsCaptureAlarm> filteredList = new ArrayList<>(latest.stream()
                .filter(acsCaptureAlarm -> acsCaptureAlarm.getAlarmStatus().equals("1"))
                .collect(Collectors.toMap(
                        acsCaptureAlarm -> acsCaptureAlarm.getSn() + "-" + acsCaptureAlarm.getParameterCode(),
                        acsCaptureAlarm -> acsCaptureAlarm,
                        (existing, replacement) -> existing.getInsertTime().before(replacement.getInsertTime()) ? existing : replacement
                )).values());
        return filteredList;
    }

    private List<AcsCaptureAlarm> findCurrentActiveAlarm(List<AcsCaptureAlarm> list) {
        // 看板最多提供100条异常数据
        List<AcsUserDevice> userDevices = acsUserDeviceMapper.selectAllNotDelete();
        List<AcsCaptureAlarm> result = new ArrayList<>();
        result = list.stream().limit(100).collect(Collectors.toList());
        for (AcsCaptureAlarm a : result) {
            List<AcsUserDevice> acsUserDevices = userDevices.stream().filter( x -> x.getSn().equals(a.getSn()) && x.getDel() == 0)
                    .collect(Collectors.toList());
            if (acsUserDevices.size() > 0) {
                a.setAcsUserDevice(acsUserDevices.get(0));
            }
        }
        return result;
    }


    public Map<String, Object> findDataByCompany(Date beginDate, Date endDate, Integer page, Integer size) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        List<AcsCaptureAlarm> list;
        if (currentUser.getRole() == RoleEnum.SUPER_ADMIN.role) {
            list = acsCaptureAlarmMapper.findByDate(beginDate, endDate);
            return PageUtils.toPageResult(list.size(), page, size, list);
        }
        list = acsCaptureAlarmMapper.findByDateAndCompany(beginDate, endDate, currentUser.getCompany());

        return PageUtils.toPageResult(list.size(), page, size, list);
    }

    public void updateAlarmStatus(AcsCaptureAlarm alarm) {
        if (alarm == null || alarm.getId() == null) {
            return;
        }
        if (Constants.ALARM_STATUS2.equals(alarm.getAlarmStatus()) || Constants.ALARM_STATUS3.equals(alarm.getAlarmStatus())) {
            if ("ip".equalsIgnoreCase(alarm.getParameterCode())) {
                // 获取设备信息
                AcsUserDevice userDevice = userDeviceService.getBySn(alarm.getSn());
                String publishDeviceTopic = topicService.getPublishDeviceTopic(alarm.getSn());
                JSONObject param = new JSONObject();
                param.put("cmd", MessageCmd.IP_PASS.cmd);
                param.put("ip", alarm.getCurrentValue());
                boolean upgrade =
                        null != userDevice && null != userDevice.getUpgradeDevice() && userDevice.getUpgradeDevice();
                if (upgrade) {
                    mqttClientManager.publish(
                            topicService.getPublishUpgradeDeviceTopic(alarm.getSn()), param.toJSONString());
                } else {
                    mqttClientManager.publish(publishDeviceTopic, param.toJSONString());
                }
            }
        }
        acsCaptureAlarmMapper.updateAlarmStatus(alarm);
    }

    @Transactional
    public void insertList(List<AcsCaptureAlarm> acsCaptureAlarm) {
        if (CollectionUtils.isEmpty(acsCaptureAlarm)) {
            return;
        }
        acsCaptureAlarmMapper.insertList(acsCaptureAlarm);
    }

    public void delList(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        acsCaptureAlarmMapper.delList(ids);
    }

    public void delByDate(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return;
        }
        acsCaptureAlarmMapper.delByDate(startDate, endDate);
    }

    public void clearAlarm(String sn){
        if (StringUtils.isBlank(sn)) {
            return;
        }
        acsCaptureAlarmMapper.clearAlarm(sn);
    }
}
