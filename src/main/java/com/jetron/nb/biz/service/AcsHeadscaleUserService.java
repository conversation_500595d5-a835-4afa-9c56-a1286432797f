package com.jetron.nb.biz.service;

import com.jetron.nb.dal.dao.AcsHeadscaleUserMapper;
import com.jetron.nb.dal.po.AcsHeadscaleUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Headscale 用户记录服务类
 */
@Service
@Slf4j
public class AcsHeadscaleUserService {

    @Resource
    private AcsHeadscaleUserMapper acsHeadscaleUserMapper;

    /**
     * 创建 Headscale 用户记录
     */
    @Transactional
    public int createHeadscaleUser(AcsHeadscaleUser headscaleUser) {
        Date now = new Date();
        headscaleUser.setCreatedAt(now);
        headscaleUser.setUpdatedAt(now);
        headscaleUser.setStatus(1); // 设置为正常状态
        
        log.info("Creating Headscale user record: username={}, displayName={}, acsUserId={}", 
                headscaleUser.getUsername(), headscaleUser.getDisplayName(), headscaleUser.getAcsUserId());
        
        return acsHeadscaleUserMapper.insertSelective(headscaleUser);
    }

    /**
     * 根据 ACS 用户ID查询 Headscale 用户记录
     */
    public AcsHeadscaleUser getByAcsUserId(Integer acsUserId) {
        return acsHeadscaleUserMapper.selectByAcsUserId(acsUserId);
    }

    /**
     * 根据用户名查询 Headscale 用户记录
     */
    public AcsHeadscaleUser getByUsername(String username) {
        return acsHeadscaleUserMapper.selectByUsername(username);
    }

    /**
     * 根据 Headscale 用户ID查询记录
     */
    public AcsHeadscaleUser getByHeadscaleUserId(String headscaleUserId) {
        return acsHeadscaleUserMapper.selectByHeadscaleUserId(headscaleUserId);
    }

    /**
     * 根据创建人查询列表
     */
    public List<AcsHeadscaleUser> getByCreateBy(Integer createBy) {
        return acsHeadscaleUserMapper.selectByCreateBy(createBy);
    }

    /**
     * 查询所有有效记录
     */
    public List<AcsHeadscaleUser> getAllActive() {
        return acsHeadscaleUserMapper.selectAllActive();
    }

    /**
     * 更新 Headscale 用户记录
     */
    @Transactional
    public int updateHeadscaleUser(AcsHeadscaleUser headscaleUser) {
        headscaleUser.setUpdatedAt(new Date());
        return acsHeadscaleUserMapper.updateByPrimaryKeySelective(headscaleUser);
    }

    /**
     * 删除 Headscale 用户记录（逻辑删除）
     */
    @Transactional
    public int deleteHeadscaleUser(Integer id) {
        log.info("Deleting Headscale user record: id={}", id);
        return acsHeadscaleUserMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据条件查询记录数
     */
    public int countByCondition(AcsHeadscaleUser condition) {
        return acsHeadscaleUserMapper.countByCondition(condition);
    }

    /**
     * 根据条件查询记录列表
     */
    public List<AcsHeadscaleUser> getByCondition(AcsHeadscaleUser condition) {
        return acsHeadscaleUserMapper.selectByCondition(condition);
    }

    /**
     * 检查用户名是否已存在
     */
    public boolean isUsernameExists(String username) {
        AcsHeadscaleUser existing = getByUsername(username);
        return existing != null;
    }

    /**
     * 检查 ACS 用户是否已有对应的 Headscale 用户记录
     */
    public boolean isAcsUserExists(Integer acsUserId) {
        AcsHeadscaleUser existing = getByAcsUserId(acsUserId);
        return existing != null;
    }
}
