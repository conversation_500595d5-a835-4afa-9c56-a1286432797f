package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsDeviceFlowMapper;
import com.jetron.nb.dal.dao.AcsDeviceFlowMonthMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsDeviceFlowMonthService {

    @Autowired
    AcsDeviceFlowMonthMapper mapper;
    @Autowired
    AcsDeviceFlowMapper flowMapper;
    @Autowired
    AcsCompanyDictionaryService companyDictionaryService;
    @Autowired
    AcsDeviceFlowService flowService;
    @Autowired
    AcsUserDeviceService deviceService;

    public QueryWrapper<AcsDeviceFlowMonth> getWrapper(AcsDeviceFlowMonth param) {
        QueryWrapper<AcsDeviceFlowMonth> wrapper = new QueryWrapper<AcsDeviceFlowMonth>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getDate())) {
            wrapper.eq("date",param.getDate());
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.like("company",param.getCompany());
        }
        if (StringUtils.isNotBlank(param.getSn())) {
            wrapper.like("sn",param.getSn());
        }
        if (param.getStart() != null) {
            wrapper.ge("gmt_create",param.getStart());
        }
        return wrapper;
    }


    public List<AcsDeviceFlowMonth> findList(AcsDeviceFlowMonth param) {
        List<AcsDeviceFlowMonth> list = mapper.selectList(getWrapper(param));
        return list;
    }

    public Map<String, Object> findPage(AcsDeviceFlowMonth param) {
        IPage<AcsDeviceFlowMonth> page  = new Page<>();
        page.setCurrent(param.getPage());
        page.setSize(param.getSize());
        IPage<AcsDeviceFlowMonth> flowMonthPage = mapper.findList(page, param);
        return PageUtils.toPageResult((int)page.getTotal(), (int)page.getCurrent(), (int)page.getSize(), flowMonthPage.getRecords());
    }

    public void update(String id,Long flow) {
        UpdateWrapper<AcsDeviceFlowMonth> wrapper = new UpdateWrapper();
        wrapper.set("up_flow",flow)
                .eq("id",id);
        mapper.update(null,wrapper);
    }

    @Transactional
    public void insertList(List<AcsDeviceFlowMonth> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        flowMapper.insertMonthList(list);
    }
    @Transactional
    public void updateList(List<AcsDeviceFlowMonth> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> id = list.stream().map(AcsDeviceFlowMonth::getId).collect(Collectors.toList());
        mapper.deleteBatchIds(id);
        this.insertList(list);
        /*for (int i = 0; i < list.size(); i++) {
            AcsDeviceFlowMonth flowMonth = list.get(i);
            mapper.updateById(flowMonth);
        }*/
    }
    @Transactional
    public Result flowCalculate() {
        List<JSONObject>  dataList = new ArrayList<>();
        // 获取上个月第一天开始 到现在的【月流量】
        AcsDeviceFlowMonth monthParam = new AcsDeviceFlowMonth();
        monthParam.setStart(com.jetron.nb.common.util.DateUtils.getLastMonthFirstDay());
        // 月流量集合 monthList1
        List<AcsDeviceFlowMonth> monthList1 = this.findList(monthParam);
        // 获取所有用户设备集合 deviceList
        List<AcsUserDevice> deviceList = deviceService.findListWithUser(new UserDeviceFilter());
        // 查询这两月所有流量,两个月的流量，包含当月的流量数据
        AcsDeviceFlow flowParam = new AcsDeviceFlow();
        flowParam.setStart(com.jetron.nb.common.util.DateUtils.getLastMonthFirstDay());
        // 所有设备上个月第一天开始 所有流量数据 flowList， 将设备流量根据sn封装 snFlowList1
        List<AcsDeviceFlow> flowList = flowService.findList(flowParam);
        Map<String, List<AcsDeviceFlow>> snFlowList1 = flowList.stream()
                .collect(Collectors.groupingBy(AcsDeviceFlow::getSn));

        // 获取公司的规则
        AcsCompanyDictionary dictParam = new AcsCompanyDictionary();
        dictParam.setDictType(Constants.CompanyDict.DICT_TYPE_FLOW);
        dictParam.setDictLabel(Constants.CompanyDict.DICT_lABEL_FLOW);
        // 所有公司规则companyList
        List<AcsCompanyDictionary> companyList = companyDictionaryService.findList(dictParam);
        // 规则MAP
        Map<String, String> companyDictMap = companyList.stream().collect(Collectors
                .toMap(AcsCompanyDictionary::getCompany, AcsCompanyDictionary::getDictValue, (k1, k2) -> k1));

        List<AcsDeviceFlowMonth> addList = new ArrayList<>();
        List<AcsDeviceFlowMonth> updateList = new ArrayList<>();
        // 遍历设备 计算所有设备的流量
        for (int i = 0; i < deviceList.size(); i++) {
            AcsUserDevice device = deviceList.get(i);
            // 此设备所属的公司（用户的公司）
            String company = device.getUser().getCompany();
            String sn = device.getSn();
            // 获取设备所有流量数据
            List<AcsDeviceFlow> flowList1 = snFlowList1.get(sn);
            if (CollectionUtils.isEmpty(flowList1)) {
                continue;
            }
            // 获取计算规则
            String dictValueStr = companyDictMap.get(company);
            Integer dictValue = StringUtils.isNotBlank(dictValueStr) ? Integer.valueOf(dictValueStr) : null;

            // 获取开始的时间 currentMonthStartDate   获取开始的月字符串 currentMonthStr   -10上个月十号开始  5本月5号开始
            Date currentMonthStartDate = com.jetron.nb.common.util.DateUtils.getStartDay(dictValue);
            String currentMonthStr = com.jetron.nb.common.util.DateUtils.getCurrentMonth(dictValue);
            // 获取当前设备和需要统计的月份的【月流量数据】 collect1
            List<AcsDeviceFlowMonth> collect1 = monthList1.stream().filter(obj -> sn.equals(obj.getSn()) && currentMonthStr.equals(obj.getDate()))
                    .collect(Collectors.toList());
            List<AcsDeviceFlow> flowList4 = null;
            if (!CollectionUtils.isEmpty(collect1)) {
                // 如果有本月流量，则累加，则修改.
                AcsDeviceFlowMonth currentFlowMonth = collect1.get(0);
                // startDate 开始时间是月统计量的后一秒
                Date startDate = DateUtils.addSeconds(currentFlowMonth.getGmtModify(),1);
                Date lastDate = currentFlowMonth.getGmtModify();
                // 本月该设备的新流量
                List<AcsDeviceFlow> flowList2 = flowList1.stream().filter(obj -> obj.getGmtCreate().getTime() >= startDate.getTime())
                        .collect(Collectors.toList());
                //  本月已计算流量
                List<AcsDeviceFlow> flowList3 = flowList1.stream()
                        .filter(obj -> obj.getGmtCreate().getTime() <= lastDate.getTime())
                        .sorted(Comparator.comparing(AcsDeviceFlow::getGmtCreate).reversed())
                        .collect(Collectors.toList());
                List<AcsDeviceFlow> lastFlow = new ArrayList<>();
                if (!CollectionUtils.isEmpty(flowList3)) {
                    lastFlow.add(flowList3.get(0));
                }
                flowList4 = flowService.getTrueList(flowList2,lastFlow);
            } else {
                // 如果无本月流量，则计算本月流量，新增
                Date startDate = currentMonthStartDate;
                Date lastDate = DateUtils.addSeconds(currentMonthStartDate, -1);
                // 本月新流量
                List<AcsDeviceFlow> flowList2 = flowList1.stream().filter(obj -> obj.getGmtCreate().getTime() >= startDate.getTime())
                        .collect(Collectors.toList());
                //  本月已计算流量
                List<AcsDeviceFlow> flowList3 = flowList1.stream()
                        .filter(obj -> obj.getGmtCreate().getTime() <= lastDate.getTime())
                        .sorted(Comparator.comparing(AcsDeviceFlow::getGmtCreate).reversed())
                        .collect(Collectors.toList());
                List<AcsDeviceFlow> lastFlow = new ArrayList<>();
                if (!CollectionUtils.isEmpty(flowList3)) {
                    lastFlow.add(flowList3.get(0));
                }
                flowList4 = flowService.getTrueList(flowList2, lastFlow);
            }
            // 修正后的本月流量
            if (CollectionUtils.isEmpty(flowList4)) {
                continue;
            }
            // 如果有新的流量就新增，否则就修改
            Long monthFlow = flowList4.stream().mapToLong(AcsDeviceFlow::getSumFlow).sum();
            if (!CollectionUtils.isEmpty(collect1)) {
                AcsDeviceFlowMonth currentFlowMonth = collect1.get(0);
                currentFlowMonth.setUpFlow(currentFlowMonth.getUpFlow() + monthFlow);
                currentFlowMonth.setGmtModify(new Date());
                updateList.add(currentFlowMonth);
            } else {
                AcsDeviceFlowMonth currentFlowMonth = new AcsDeviceFlowMonth();
                currentFlowMonth.setId(String.valueOf(IdWorker.getId()))
                        .setSn(sn)
                        .setCompany(company)
                        .setDate(currentMonthStr)
                        .setUpFlow(monthFlow)
                        .setGmtModify(new Date())
                        .setGmtCreate(new Date());
                addList.add(currentFlowMonth);
            }
        }
        this.insertList(addList);
        this.updateList(updateList);
        return Result.success(dataList);
    }

}
