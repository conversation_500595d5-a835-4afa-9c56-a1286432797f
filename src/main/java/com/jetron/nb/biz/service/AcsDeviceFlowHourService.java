package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsCaptureDataMapper;
import com.jetron.nb.dal.dao.AcsDeviceFlowHourMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsDeviceFlowHourService {

    @Resource
    private AcsUserDeviceService acsUserDeviceService;
    @Resource
    private AcsUserService acsUserService;
    @Resource
    private AcsDeviceFlowHourMapper mapper;

    @Autowired
    private AcsCaptureDataMapper acsCaptureDataMapper;



    public AcsDeviceFlow getRecord(String sn, String date) {
        return mapper.getRecord(sn, date);
    }

    public List<AcsDeviceFlow> getDeviceFlow(String sn, Date start, Date endDate) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if ((currentUser.getRole() == RoleEnum.USER_ADMIN.role && !Objects.equals(acsUserDevice.getBelongTo(), currentUser.getId()))
                || (currentUser.getRole() == RoleEnum.USER.role && !Objects.equals(acsUserDevice.getBelongTo(), currentUser.getParentId()))) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }
        return mapper.getDeviceFlow(sn, start, endDate);
    }

    /**
     * 流量表中保存的流量是总数。当天流量（正确值） = 当天流量总数 - 昨天流量总数（若无昨天流量则减去上一次流量）
     * sn 不传则查询所有设备
     * @param sn
     * @param start
     * @param endDate
     * @return
     */
    public List<AcsDeviceFlow> findTrueList(String sn, Date start, Date endDate) {
        List<AcsDeviceFlow> dataList = new ArrayList<>();
        List<AcsDeviceFlow> deviceFlow = this.getDeviceFlow(sn, start, endDate);
        List<AcsDeviceFlow> lastFlow = findLastFlow(sn, start);
        return AcsDeviceFlowService.getTrueList(deviceFlow,lastFlow);
    }

    public List<AcsDeviceFlow> findLastFlow(String sn, Date start) {
        AcsDeviceFlow param = new AcsDeviceFlow();
        param.setSn(sn);
        param.setStart(start);
        return mapper.findLastFlow(param);
    }

    public List<UserFlow> getUserFlow(Integer userId, String dateStr) {
        try {
            AcsUser acsUser = acsUserService.selectByPrimaryKey(userId);
            if (acsUser == null) {
                throw new IotException(ApiCode.USER_NOT_EXIST);
            }

            AcsUser currentUser = MdcUtils.getCurrentAcsUser();
            if (!MdcUtils.isAdmin() && !Objects.equals(userId, currentUser.getId())) {
                throw new IotException(ApiCode.USER_NOT_EXIST);
            }

            Date date = DateUtils.parseDate(dateStr, "YYYY-MM");
            Date startDate = date;
            Date endDate = new DateTime(date).plusMonths(1).toDate();

            return mapper.getUserFlow(userId, startDate, endDate);
        } catch (ParseException e) {
            log.error("[AcsDeviceFlowService#getUserFlow] parse date error, {}", dateStr, e);
            throw new IotException(ApiCode.BAD_REQUEST);
        }
    }

    public Result weeklyFlow(String company) {
        Map<String, List<Object>> weeklyFlow = new LinkedHashMap<>();
        List<Object> legend = new ArrayList<>();
        List<Object> series = new ArrayList<>();
        weeklyFlow.put("week", Arrays.asList(new String[]{"周一", "周二", "周三", "周四", "周五", "周六", "周日"}));
        weeklyFlow.put("legend", legend);
        weeklyFlow.put("series", series);

        // 获取公司下的 sn
        List<String> snList = acsUserDeviceService.findSnByCompany(company);
        if (CollectionUtils.isEmpty(snList)) {
            return Result.success(weeklyFlow);
        }
        // 整理返回数据
        List<Date> week = com.jetron.nb.common.util.DateUtils.getTimeInterval(new Date());//周一到周日的所有时间
        Date monday = com.jetron.nb.common.util.DateUtils.getMonday(new Date());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<AcsDeviceFlow> flows = mapper.selectWeeklyFlow(snList, dateFormat.format(monday));

        legend = flows.stream().map(AcsDeviceFlow::getSn).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        ;

        Map<String, List<AcsDeviceFlow>> weekFlows = flows.stream().collect(Collectors.groupingBy(AcsDeviceFlow::getDate));

        for (Object snObj : legend) {
            Map<String, Object> flowMap = new HashMap<>();
            List<String> snFlowList = new ArrayList<>();
            for (int i = 0; i < week.size(); i++) {
                Date date = week.get(i);
                String dateStr = dateFormat.format(date);
                List<AcsDeviceFlow> flowList = weekFlows.get(dateStr);
                if (flowList == null) {
                    snFlowList.add("0");
                    continue;
                }
                Map<String, AcsDeviceFlow> snFlow = flowList.stream().collect(Collectors.toMap(AcsDeviceFlow::getSn, a -> a, (k1, k2) -> k1));
                AcsDeviceFlow flow = snFlow.get(snObj.toString());
                if (flow == null) {
                    snFlowList.add("0");
                } else {
                    snFlowList.add(String.valueOf(flow.getDownFlow()));
                }
            }
            flowMap.put("sn", snObj.toString());
            flowMap.put("data", snFlowList);
            series.add(flowMap);
        }
        weeklyFlow.put("legend", legend);
        return Result.success(weeklyFlow);
    }

    public Result dataLineChart(String company) {

        Map<String, Object> chartData = new LinkedHashMap<>();
        Date end = new Date();
        Date begin = DateUtils.addHours(end, -4);

        List<AcsCaptureData> chartData1 = acsCaptureDataMapper.findChartData(company, begin, end);

        Map<String, List<AcsCaptureData>> snMap = chartData1.stream().collect(Collectors.groupingBy(AcsCaptureData::getSn));
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");

        List<String> timeList = new ArrayList<>();
        for (String sn : snMap.keySet()) {
            List<String> temperatureList = new ArrayList<>();
            List<String> ramList = new ArrayList<>();
            List<String> signalQualityList = new ArrayList<>();
            List<String> rssiList = new ArrayList<>();
            List<String> cpuList = new ArrayList<>();
            List<AcsCaptureData> captureList = snMap.get(sn);
            captureList = captureList.stream().sorted(Comparator.comparing(AcsCaptureData::getInsertTime)).collect(Collectors.toList());
            Date date = begin;
            int i = 0;
            do {
                if (timeList.size() < 24) {
                    timeList.add(dateFormat.format(date));
                }
                if (captureList.size() <= i ||
                        com.jetron.nb.common.util.DateUtils.diffMinute(captureList.get(i).getInsertTime(), date) >= 10) {
                    date = DateUtils.addMinutes(date, 10);
                    temperatureList.add("0");
                    ramList.add("0");
                    signalQualityList.add("0");
                    rssiList.add("0");
                    cpuList.add("0");
                    continue;
                }
                AcsCaptureData captureData = captureList.get(i);

                temperatureList.add(captureData.getDevTemp());
                ramList.add(captureData.getRam().replace("Mb", ""));
                signalQualityList.add(captureData.getSignalQuality());
                rssiList.add(AcsCaptureData.sigStrengthTransform(captureData.getRssi()));
                cpuList.add(captureData.getCpuRation().replace("%", ""));

                i++;
                date = DateUtils.addMinutes(date, 10);
            } while (date.compareTo(end) <= 0);
            Map<String, List<String>> map = new HashMap<>();
            map.put("temperatureList", temperatureList);
            map.put("ramList", ramList);
            map.put("signalQualityList", signalQualityList);
            map.put("rssiList", rssiList);
            map.put("cpuList", cpuList);
            chartData.put(sn, map);
        }
        chartData.put("snList", snMap.keySet());
        chartData.put("time", timeList);
        return Result.success(chartData);
    }

    public int deleteByPrimaryKey(Integer id) {
        return mapper.deleteByPrimaryKey(id);
    }


    public int insert(AcsDeviceFlow record) {
        return mapper.insert(record);
    }


    public int insertSelective(AcsDeviceFlow record) {
        return mapper.insertSelective(record);
    }


    public AcsDeviceFlow selectByPrimaryKey(Integer id) {
        return mapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(AcsDeviceFlow record) {
        return mapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(AcsDeviceFlow record) {
        return mapper.updateByPrimaryKey(record);
    }

    public Result flowStatisticsOfDashboard() {
        Map<String, Object> chartData = new LinkedHashMap<>();
        Date now = new Date();
        try {
            now = DateUtils.parseDate("2021-08-16", "yyyy-MM-dd");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date begin = DateUtils.addDays(now, -6);
        Date end = DateUtils.addSeconds(DateUtils.addDays(now, 1), -1);

        Integer userId = null;
        if (!MdcUtils.isAdmin()) {
            AcsUser currentAcsUser = MdcUtils.getCurrentAcsUser();
            userId = currentAcsUser.getId();
        }
        List<AcsDeviceFlow> userFlowWithDate = mapper.getUserFlowWithDate(userId, begin, end);
        List<Date> before7DayDate = com.jetron.nb.common.util.DateUtils.getBefore7DayDate(new Date());
        List<String> dateList = new ArrayList<>();
        for (int i = 0; i < before7DayDate.size(); i++) {
            dateList.add(DateFormatUtils.format(before7DayDate.get(i), "MM-dd"));
        }
        List<String> down = new ArrayList<>();
        List<String> up = new ArrayList<>();
        for (int i = 0; i < userFlowWithDate.size(); i++) {
            AcsDeviceFlow flow = userFlowWithDate.get(i);
            down.add(String.valueOf(flow.getDownFlow()));
            up.add(String.valueOf(flow.getUpFlow()));
        }
        chartData.put("dateList", dateList);
        chartData.put("down", down);
        chartData.put("up", up);
        return Result.success(chartData);
    }


}
