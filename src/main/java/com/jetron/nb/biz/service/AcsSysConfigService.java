package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.jetron.nb.dal.dao.AcsSysConfigMapper;
import com.jetron.nb.dal.po.AcsSysConfig;

import java.util.List;

@Service
public class AcsSysConfigService{

    @Resource
    private AcsSysConfigMapper acsSysConfigMapper;

    
    public int deleteByPrimaryKey(Integer id) {
        return acsSysConfigMapper.deleteByPrimaryKey(id);
    }

    
    public int insert(AcsSysConfig record) {
        return acsSysConfigMapper.insert(record);
    }

    
    public int insertSelective(AcsSysConfig record) {
        return acsSysConfigMapper.insertSelective(record);
    }

    
    public AcsSysConfig selectByPrimaryKey(Integer id) {
        return acsSysConfigMapper.selectByPrimaryKey(id);
    }

    
    public int updateByPrimaryKeySelective(AcsSysConfig record) {
        return acsSysConfigMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(AcsSysConfig record) {
        return acsSysConfigMapper.updateByPrimaryKey(record);
    }

    public void update(String name, String content, String describ) {
        if (!MdcUtils.isAdmin()) {
            throw new IotException(ApiCode.NO_AUTHORITY);
        }

        AcsSysConfig config = acsSysConfigMapper.getByName(name);

        if (config == null) {
            throw new IotException(ApiCode.SYSCONFIG_NOT_EXIST);
        }

        AcsSysConfig updateConfig = new AcsSysConfig();
        updateConfig.setId(config.getId());
        updateConfig.setContent(content);
        updateConfig.setDescrib(describ);

        acsSysConfigMapper.updateByPrimaryKeySelective(updateConfig);

    }

    public List<AcsSysConfig> list() {
        return acsSysConfigMapper.getAll();
    }

    public AcsSysConfig getByName(String name) {
        return acsSysConfigMapper.getByName(name);
    }

}
