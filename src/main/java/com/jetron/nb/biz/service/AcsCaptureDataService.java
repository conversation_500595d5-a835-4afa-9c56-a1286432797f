package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RedisDatabaseEnum;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.util.ResultUtils;
import com.jetron.nb.common.vo.DeviceOnlineInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.AcsCaptureDataMapper;
import com.jetron.nb.dal.dao.AcsDeviceMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsCaptureDataService {



    @Autowired
    private AcsCaptureDataMapper mapper;


    public List<AcsCaptureData> findLatestData(AcsCaptureData acsCaptureData){
        return mapper.findLatestData(acsCaptureData);
    }
}
