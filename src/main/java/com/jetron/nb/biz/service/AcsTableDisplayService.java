package com.jetron.nb.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsTableDisplayMapper;
import com.jetron.nb.dal.po.AcsTableDisplay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class AcsTableDisplayService {

    @Autowired
    private AcsTableDisplayMapper tableDisplayMapper;

    public QueryWrapper<AcsTableDisplay> getWrapper(AcsTableDisplay param) {
        QueryWrapper<AcsTableDisplay> wrapper = new QueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (param.getUserId() != null) {
            wrapper.eq("user_id",param.getUserId());
        }
        if (param.getFlag() != null) {
            wrapper.eq("flag",param.getFlag());
        }
        wrapper.eq("table_name", Constants.TableNames.USER_DEVICE);
        return wrapper;
    }

    public Result findList(AcsTableDisplay param) {
        QueryWrapper<AcsTableDisplay> wrapper = getWrapper(param);
        List<AcsTableDisplay> list = tableDisplayMapper.selectList(wrapper);
        return Result.success(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result updateColumn( List<String> columnsList,String table) {
        Integer id = MdcUtils.getCurrentAcsUser().getId();
        QueryWrapper<AcsTableDisplay> wrapper = new QueryWrapper<>();
        wrapper.eq("table_name", Constants.TableNames.USER_DEVICE);
        wrapper.eq("user_id",id);
        tableDisplayMapper.delete(wrapper);
        if (CollectionUtils.isEmpty(columnsList)) {
            return Result.success();
        }
        List<AcsTableDisplay> list = new ArrayList<>();
        for (int i = 0; i < columnsList.size(); i++) {
            AcsTableDisplay data = new AcsTableDisplay();
            data.setGmtCreate(new Date());
            data.setTableName(table);
            data.setColumnName(columnsList.get(i));
            data.setUserId(id);
            data.setFlag(Constants.NO_INT);
            list.add(data);
        }
        tableDisplayMapper.insertList(list);
        return Result.success(list);
    }
}
