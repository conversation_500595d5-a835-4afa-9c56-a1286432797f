package com.jetron.nb.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsDictionaryMapper;
import com.jetron.nb.dal.po.AcsDictionary;
import com.jetron.nb.dal.po.AcsDriver;
import com.jetron.nb.dal.po.AcsUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AcsDictionaryService {
    @Resource
    private AcsDictionaryMapper acsDictionaryMapper;

    public int insert(AcsDictionary acsDictionary) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role) {
            throw new IotException(ApiCode.NO_AUTHORITY);
        }
        return acsDictionaryMapper.insert(acsDictionary);
    }

    public List<AcsDictionary> findByKey(String key) {
        return acsDictionaryMapper.findByKey(key);
    }

    public int deleteByPrimaryKey(Integer id) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role) {
            throw new IotException(ApiCode.NO_AUTHORITY);
        }
        return acsDictionaryMapper.deleteByPrimaryKey(id);
    }

    public Map<String, Object> findAll(Integer page, Integer size) {
        List<AcsDictionary> result = acsDictionaryMapper.findAll();

        return PageUtils.toPageResult(result.size(), page, size, result);
    }

    public Map<String, Object> findList(Integer page, Integer size,AcsDictionary acsDictionary) {
        acsDictionary.setLimit(size);
        acsDictionary.setOffset((page - 1) * size);

        List<AcsDictionary> result = acsDictionaryMapper.findPage(acsDictionary);
        int count = acsDictionaryMapper.count(acsDictionary);

        return PageUtils.toPageResult(count, page, size, result);
    }
}
