package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.Topic;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 *
 * Date: 2020-03-01
 * Version: 1.0.0
 */
@Slf4j
@Service
public class AcsTopicService {
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public String getSubscribeDeviceTopic(String sn) {
        String name = getUserName(sn);
        if (org.springframework.util.StringUtils.isEmpty(name)) {
            return null;
        }
        return String.format("$queue/" + Topic.DEVICE_TOPIC.name, name, sn);
    }

    public String getSubscribeUpgradeDeviceTopic(String sn) {
        String name = getUserName(sn);
        if (org.springframework.util.StringUtils.isEmpty(name)) {
            return null;
        }
        return String.format("$queue/" + Topic.DEVICE_TOPIC_UPGRADE.name, name, sn, "up");
    }

    public String getPublishDeviceTopic(String sn) {
        String name = getUserName(sn);
        return String.format(Topic.DEVICE_TOPIC.name, name, sn);
    }

    public String getPublishUpgradeDeviceTopic(String sn) {
        String name = getUserName(sn);
        return String.format(Topic.DEVICE_TOPIC_UPGRADE.name, name, sn, "down");
    }

    public String getSubscribeAcsTopic() {
        return "$queue/" + Topic.ACS_SYNC_TOPIC.name;
    }

    public String getPublishAcsTopic() {
        return Topic.ACS_SYNC_TOPIC.name;
    }

    public String getSubscribeUserTopic(String userName) {
        return String.format("$queue/" + Topic.USER_TOPIC_PUBLISH.name, userName);
    }

    public String getPublishUserTopic(String userName) {
        return String.format(Topic.USER_TOPIC_PUBLISH.name, userName);
    }

    private String getUserName(String sn) {
        String name = (String) redisTemplate.opsForHash().get(snKey(sn), "username");
        if (StringUtils.isBlank(name)) {
            name = acsUserDeviceService.getBelongUserName(sn);
            if (StringUtils.isNoneBlank(name)) {
                redisTemplate.opsForHash().put(snKey(sn), "username", name);
            }
        }

        if (StringUtils.isBlank(name)) {
            log.error("[AcsTopicService#getUserName] name is null, sn: {}", sn);
        }
        return name;
    }

    /**
     * 删除此序列号在redis中的username 哈希
     * @param sn
     */
    public void delUserName(String sn) {
        String name = (String) redisTemplate.opsForHash().get(snKey(sn), "username");
        if ( !StringUtils.isBlank(name) ) {
            redisTemplate.opsForHash().put(snKey(sn), "username", name);
            redisTemplate.opsForHash().delete(snKey(sn),"username");
        }
    }


    private String snKey(String sn) {
        return "info_" + sn;
    }

}
