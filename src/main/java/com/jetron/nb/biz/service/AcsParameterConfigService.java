package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.AcsParameterConfigMapper;
import com.jetron.nb.dal.po.AcsParameterConfig;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AcsParameterConfigService {
    @Resource
    private AcsParameterConfigMapper acsParameterConfigMapper;

    @Autowired
    private AcsUserDeviceService acsUserDeviceService;

    @Autowired
    private AcsUserService acsUserService;

    public int insert(AcsParameterConfig acsParameterConfig) {
        acsParameterConfig.setInsertTime(new Date());
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        acsParameterConfig.setCompany(currentUser.getCompany());
        return acsParameterConfigMapper.insert(acsParameterConfig);
    }

    public Map<String, Object> findAll(Integer page, Integer size) {
        List<AcsParameterConfig> result = acsParameterConfigMapper.findAll();
        return PageUtils.toPageResult(result.size(), page, size, result);
    }

    public Map<String, Object> findGroubByModel(AcsParameterConfig acsParameterConfig,Integer page, Integer size) {
        List<AcsParameterConfig> result = acsParameterConfigMapper.findGroupByCompanyModel(acsParameterConfig);
        int count = acsParameterConfigMapper.findCount(acsParameterConfig);
        return PageUtils.toPageResult(count, page, size, result);
    }

    public List<AcsParameterConfig> findList(AcsParameterConfig acsParameterConfig) {
        return acsParameterConfigMapper.findList(acsParameterConfig);
    }

    public Map<String, Object> findPage(AcsParameterConfig acsParameterConfig, Integer page, Integer size) {
        List<AcsParameterConfig> result = acsParameterConfigMapper.findList(acsParameterConfig);
        int count = acsParameterConfigMapper.findCount(acsParameterConfig);
        return PageUtils.toPageResult(count, page, size, result);
    }

    public Map<String, Object> findByCompany(String company, Integer page, Integer size) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role) {
            company = currentUser.getCompany();
        }
        if (StringUtils.isEmpty(company)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }

        List<AcsParameterConfig> result = acsParameterConfigMapper.findByCompany(company);
        return PageUtils.toPageResult(result.size(), page, size, result);
    }

    public List<AcsParameterConfig> findByCompany(String company) {
        if (StringUtils.isEmpty(company)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
        return acsParameterConfigMapper.findByCompany(company);
    }

    public int deleteByPrimaryKey(int id) {
        return acsParameterConfigMapper.deleteByPrimaryKey(id);
    }

    public AcsParameterConfig findById(Integer id) {
        if (id == null) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
        return acsParameterConfigMapper.findById(id);
    }

    public AcsParameterConfig findByParameterKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
        return acsParameterConfigMapper.findByParameterKey(key);
    }

    public int updateByPrimaryKey(AcsParameterConfig acsParameterConfig) {
        acsParameterConfig.setInsertTime(new Date());
        return acsParameterConfigMapper.updateByPrimaryKey(acsParameterConfig);
    }

    public List<AcsParameterConfig> findBySn(String sn) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice == null) {
            return null;
        }
        AcsUser acsUser = acsUserService.selectByPrimaryKey(acsUserDevice.getBelongTo());
        List<AcsParameterConfig> acsParameterConfigs = findByCompany(acsUser.getCompany());
        if (acsParameterConfigs.size() <= 0) {
            return null;
        }
        return acsParameterConfigs.stream().filter( x -> acsUserDevice.getModel().equals(x.getParameterKey())).collect(Collectors.toList());
    }
}
