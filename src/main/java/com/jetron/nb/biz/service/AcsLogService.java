package com.jetron.nb.biz.service;

import com.google.common.util.concurrent.Uninterruptibles;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.AcsUserMapper;
import com.jetron.nb.dal.po.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.jetron.nb.dal.dao.AcsLogMapper;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class AcsLogService {

    @Resource
    private AcsLogMapper acsLogMapper;
    @Resource
    private AcsUserMapper acsUserMapper;

    public Map<String, Object> seach(LogFilter filter, Integer page, Integer size)  {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }

        filter.setLimit(size);
        filter.setOffset((page - 1) * size);

        // 系统管理员可以看到所有日志，其他可以看到自己和下级日志
        if (!MdcUtils.isAdmin()) {
            Integer currentUserId = MdcUtils.getCurrentAcsUser().getId();
            List<Integer> userIdList = Lists.newArrayList();
            userIdList.add(currentUserId);

            UserFilter userFilter = new UserFilter();
            userFilter.setParentId(MdcUtils.getCurrentAcsUser().getId());
            List<AcsUser> children = acsUserMapper.getByFilter(userFilter);
            userIdList.addAll(children.stream().map(AcsUser::getId).collect(Collectors.toList()));

            filter.setUserIdList(userIdList);
        }

        List<AcsLog> result = acsLogMapper.getByFilter(filter);
        int total = acsLogMapper.countByFilter(filter);

        return PageUtils.toPageResult(total, page, size, result);

    }

    public int deleteByPrimaryKey(Integer id) {
        return acsLogMapper.deleteByPrimaryKey(id);
    }

    public int insert(AcsLog record) {
        return acsLogMapper.insert(record);
    }

    public int insertSelective(AcsLog record) {
        return acsLogMapper.insertSelective(record);
    }

    public AcsLog selectByPrimaryKey(Integer id) {
        return acsLogMapper.selectByPrimaryKey(id);
    }

    public int updateByPrimaryKeySelective(AcsLog record) {
        return acsLogMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(AcsLog record) {
        return acsLogMapper.updateByPrimaryKey(record);
    }

    public void appendLog(AcsUserDevice device, LogType logType, String logSeg) {
        LogFilter filter = new LogFilter();
        filter.setUserIdList(Arrays.asList(device.getBelongTo()));
        filter.setLogType(logType.type);
        filter.setOffset(0);
        filter.setLimit(5);

        List<AcsLog> logList = acsLogMapper.getByFilter(filter);
        String format = String.format("(%s,%s,%s)", device.getName(), device.getSn(), device.getModel());

        //找到记录
        AcsLog log = null;
        for (AcsLog tmp : logList) {
            String content = tmp.getContent();
            if (StringUtils.isNoneBlank(content) && content.contains(format)) {
                log = tmp;
                break;
            }
        }

        // acs更新日志
        int tryCount = 0;
        while (log != null && tryCount++ < 5) {
            Integer curVersion = log.getLogVersion();
            String newContent = log.getContent() + String.format("\n(%s,%s,%s), ", device.getSn(), logSeg, DateTime.now().toString("Y-M-d HH:mm:ss"));

            int count = acsLogMapper.appendLog(log.getId(), curVersion, newContent);
            if (count > 0) {
                break;
            }

            Uninterruptibles.sleepUninterruptibly(ThreadLocalRandom.current().nextInt(50),
                    TimeUnit.MILLISECONDS);
            log = acsLogMapper.selectByPrimaryKey(log.getId());
        }
    }

    public void delList(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        acsLogMapper.delList(ids);
    }

    public void delByDate(Date startDate ,Date endDate) {
        if (startDate == null || endDate == null) {
            return;
        }
        acsLogMapper.delByDate(startDate,endDate);
    }
}

