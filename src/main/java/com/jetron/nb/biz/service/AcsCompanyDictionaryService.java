package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsCompanyDictionaryMapper;

import com.jetron.nb.dal.po.AcsCompanyDictionary;
import com.jetron.nb.dal.po.AcsUser;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AcsCompanyDictionaryService {
    @Resource
    private AcsCompanyDictionaryMapper mapper;

    public List<AcsCompanyDictionary> findList(AcsCompanyDictionary dict) {
        return mapper.findList(dict);
    }

    public AcsCompanyDictionary findOne(AcsCompanyDictionary dict) {
        List<AcsCompanyDictionary> list = mapper.findList(dict);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public Map<String, Object> findPage(Integer page, Integer size, AcsCompanyDictionary dict) {
        List<AcsCompanyDictionary> result = mapper.findList(dict);
        int count = mapper.findCount(dict);
        return PageUtils.toPageResult(count, page, size, result);
    }

    public Map<String, Object> findConvertPage(Integer page, Integer size, AcsCompanyDictionary dict) {
        List<AcsCompanyDictionary> result = mapper.findList(dict);
        int count = mapper.findCount(dict);
        result = convertVal(result);
        return PageUtils.toPageResult(count, page, size, result);
    }
    
    public Result insertList(List<AcsCompanyDictionary> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Result.success();
        }
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        String company = currentUser.getCompany();
        Date date = new Date();
        for (int i = 0; i < list.size(); i++) {
            AcsCompanyDictionary dictionary = list.get(i);
            dictionary.setCompany(company);
            dictionary.setGmtCreate(date);
        }
        mapper.insertList(list);
        return Result.success();
    }

    public Result save(AcsCompanyDictionary dict) {
        if (dict == null) {
            return Result.success();
        }
        List<AcsCompanyDictionary> list = findList(dict);
        if (CollectionUtils.isEmpty(list)) {
            return insertList(Arrays.asList(dict));
        } else {
            AcsCompanyDictionary dictionary = list.get(0);
            dictionary.setDictValue(dict.getDictValue());
            return update(dictionary);
        }
    }

    public Result update(AcsCompanyDictionary dict) {
        if (dict == null) {
            return Result.success();
        }
        dict.setGmtModify(new Date());
        mapper.update(dict);
        return Result.success();
    }

    public Result delete(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Result.success();
        }
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role) {
            throw new IotException(ApiCode.NO_AUTHORITY);
        }
        mapper.delete(ids);
        return Result.success();
    }


    public List<AcsCompanyDictionary> convertVal(List<AcsCompanyDictionary> list) {
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                AcsCompanyDictionary dictionary = list.get(i);
                String dictValue = dictionary.getDictValue();
                if (Constants.CompanyDict.DICT_TYPE_FLOW.equals(dictionary.getDictType())) {
                    int val = Integer.parseInt(dictValue);
                    if (val < 0) {
                        dictValue = "上月" + Math.abs(val) + "号";
                    } else {
                        dictValue = "本月" + Math.abs(val) + "号";
                    }
                }
                dictionary.setDictValue(dictValue);
            }
        }
        return list;
    }
   
}
