package com.jetron.nb.biz.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.dal.dao.AcsDriverUpgradeMapper;
import com.jetron.nb.dal.po.AcsDriverUpgrade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class AcsDriverUpgradeService {

    @Autowired
    private AcsDriverUpgradeMapper mapper;

    public QueryWrapper<AcsDriverUpgrade> wrapper(AcsDriverUpgrade param) {
        QueryWrapper<AcsDriverUpgrade> wrapper = new QueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.eq("company",param.getCompany());
        }
        if (param.getDeviceId() != null) {
            wrapper.eq("device_id",param.getDeviceId());
        }
        if (param.getStatus() != null) {
            wrapper.eq("status",param.getStatus());
        }
        if (param.getStatusLt() != null) {
            wrapper.lt("status",param.getStatusLt());
        }
        if (StringUtils.isNotBlank(param.getDevName())) {
            wrapper.eq("dev_name",param.getDevName());
        }
        return wrapper;
    }



    public AcsDriverUpgrade find(AcsDriverUpgrade param) {
        return mapper.selectOne(wrapper(param));
    }

    public AcsDriverUpgrade findLast(AcsDriverUpgrade param) {
        QueryWrapper<AcsDriverUpgrade> wrapper = wrapper(param);
        wrapper.orderByDesc("gmt_create").last("limit 1");
        return mapper.selectOne(wrapper);
    }

    public List<AcsDriverUpgrade> list(AcsDriverUpgrade param) {
        return mapper.selectList(wrapper(param));
    }

    public List<AcsDriverUpgrade> selectCurrentStatus(AcsDriverUpgrade param) {
        return mapper.selectCurrentStatus(param);
    }


    public void add(AcsDriverUpgrade param) {
        mapper.insert(param);
    }

    public void save(AcsDriverUpgrade param) {
        mapper.updateById(param);
    }

    public void updateStatus(AcsDriverUpgrade param,int status) {
        UpdateWrapper<AcsDriverUpgrade> wrapper = new UpdateWrapper<>();
        if (param == null) {
            return;
        }
        if (!CollectionUtils.isEmpty(param.getIdList())) {
            wrapper.in("id",param.getIdList());
        }
        if (param.getId() != null) {
            wrapper.eq("id",param.getId());
        }
        if (param.getDeviceId() != null) {
            wrapper.eq("device_id",param.getDeviceId());
        }
        if (param.getStatusEq() != null) {
            wrapper.eq("status",param.getStatusEq());
        }
        if (param.getStatusLt() != null) {
            wrapper.lt("status",param.getStatusLt());
        }
        if (param.getDevName() != null) {
            wrapper.eq("dev_name",param.getDevName());
        }
        wrapper.set("status",status).set("gmt_modify",new Date());
        mapper.update(null,wrapper);
    }

    public void forceComplete(Integer deviceId) {
        AcsDriverUpgrade param = new AcsDriverUpgrade();
        param.setDeviceId(deviceId);
        this.updateStatus(param, Constants.DriverUpgradeStatus.complete);
    }

    public void deleteList(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        mapper.deleteBatchIds(list);
    }


}
