package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.service.mqtt.AcsMqttOnlineService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.AccountInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.dao.AcsCaptureDataMapper;
import com.jetron.nb.dal.dao.AcsKanbanTitleMapper;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsDeviceFlowMapper;
import org.springframework.util.CollectionUtils;
import sun.java2d.pipe.SpanShapeRenderer;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AcsDeviceFlowService {

    @Resource
    private AcsUserDeviceService acsUserDeviceService;
    @Resource
    private AcsUserService acsUserService;
    @Resource
    private AcsDeviceFlowMapper acsDeviceFlowMapper;

    @Autowired
    private AcsCaptureDataMapper acsCaptureDataMapper;

    @Autowired
    private AcsMqttOnlineService onlineService;

    @Autowired
    private AcsCompanyDictionaryService companyDictionaryService;

    @Autowired
    private AcsAlarmService alarmService;
    @Autowired
    private AcsParameterConfigService parameterConfigService;
    @Autowired
    private AcsDeviceHeartService heartService;
    @Autowired
    private AcsKanbanTitleMapper kanbanTitleMapper;
    @Autowired
    private AcsDeviceFlowMonthService flowMonthService;

    /**
     * 流量表中保存的流量是总数。当天流量（正确值） = 当天流量总数 - 昨天流量总数（若无昨天流量则减去上一次流量）
     * @return
     */
    public static List<AcsDeviceFlow> getTrueList(List<AcsDeviceFlow> deviceFlow, List<AcsDeviceFlow> lastFlow) {
        List<AcsDeviceFlow> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceFlow)) {
            return dataList;
        }
        Map<String, AcsDeviceFlow> lastMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(lastFlow)) {
            lastMap = lastFlow.stream().collect(Collectors.toMap(AcsDeviceFlow::getSn, obj -> obj,(key1,key2) -> key1));
        }
        deviceFlow = deviceFlow.stream().sorted(Comparator.comparing(AcsDeviceFlow::getSn))
                .sorted(Comparator.comparing(AcsDeviceFlow::getDate))
                .collect(Collectors.toList());

        Map<String,AcsDeviceFlow> currentSNFlow = new HashMap<>();
        for (int i = 0; i < deviceFlow.size(); i++) {
            AcsDeviceFlow flow = deviceFlow.get(i);
            AcsDeviceFlow data = JSONObject.parseObject(JSONObject.toJSONString(flow),AcsDeviceFlow.class);
            String snTemp = flow.getSn();
            AcsDeviceFlow lastFlowTemp = currentSNFlow.get(snTemp);
            if (lastFlowTemp == null) {
                lastFlowTemp = lastMap.get(snTemp);
            }
            if (lastFlowTemp == null) {
                lastFlowTemp = new AcsDeviceFlow();
                lastFlowTemp.setUpFlow(0L);
                lastFlowTemp.setDownFlow(0L);
                lastFlowTemp.setSumFlow(0L);
            }
            // 如果当前流量比上一流量大，则相减。否则视为当前流量为从0上传，不需要减去上一流量
            Long sum = flow.getUpFlow() + flow.getDownFlow();
            if (sum >= (lastFlowTemp.getDownFlow() + lastFlowTemp.getUpFlow())) {
                data.setSumFlow(sum - (lastFlowTemp.getDownFlow() + lastFlowTemp.getUpFlow()));
                data.setDownFlow(flow.getDownFlow() - lastFlowTemp.getDownFlow());
                data.setUpFlow(flow.getUpFlow() - lastFlowTemp.getUpFlow());
            } else {
                data.setSumFlow(data.getUpFlow() + data.getDownFlow());
            }
            currentSNFlow.put(snTemp,flow);
            dataList.add(data);
        }
        return dataList;
    }

    public AcsDeviceFlow getTodayRecord(String sn, String date) {
        return acsDeviceFlowMapper.getTodayRecord(sn, date);
    }

    public List<AcsDeviceFlow> getDeviceFlow(String sn, Date start, Date endDate) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        if (acsUserDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if ((currentUser.getRole() == RoleEnum.USER_ADMIN.role && !Objects.equals(acsUserDevice.getBelongTo(), currentUser.getId()))
                || (currentUser.getRole() == RoleEnum.USER.role && !Objects.equals(acsUserDevice.getBelongTo(), currentUser.getParentId()))) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }
        AcsDeviceFlow param = new AcsDeviceFlow();
        param.setSn(sn);
        param.setStart(start);
        param.setEnd(endDate);
        return acsDeviceFlowMapper.getDeviceFlow(param);
    }

    public List<AcsDeviceFlow> findList(AcsDeviceFlow param) {
        return acsDeviceFlowMapper.getDeviceFlow(param);
    }

    /**
     * 流量表中保存的流量是总数。当天流量（正确值） = 当天流量总数 - 昨天流量总数（若无昨天流量则减去上一次流量）
     * sn 不传则查询所有设备
     * @return
     */
    public List<AcsDeviceFlow> findTrueList(AcsDeviceFlow param) {
        List<AcsDeviceFlow> dataList = new ArrayList<>();
        List<AcsDeviceFlow> deviceFlow = null;
        if (StringUtils.isNotBlank(param.getSn())) {
            deviceFlow = getDeviceFlow(param.getSn(),param.getStart(),param.getEnd());
        } else {
            deviceFlow = findList(param);
        }
        if (CollectionUtils.isEmpty(deviceFlow)) {
            return dataList;
        }
        param.setEnd(null);
        List<AcsDeviceFlow> lastFlow = findLastFlow(param);
        return getTrueList(deviceFlow,lastFlow);
    }

    public List<AcsDeviceFlow> findLastFlow(AcsDeviceFlow param) {
        return acsDeviceFlowMapper.findLastFlow(param);
    }

    public List<UserFlow> getUserFlow(Integer userId, String dateStr) {
        try {
            AcsUser acsUser = acsUserService.selectByPrimaryKey(userId);
            if (acsUser == null) {
                throw new IotException(ApiCode.USER_NOT_EXIST);
            }

            AcsUser currentUser = MdcUtils.getCurrentAcsUser();
            if (!MdcUtils.isAdmin() && !Objects.equals(userId, currentUser.getId())) {
                throw new IotException(ApiCode.USER_NOT_EXIST);
            }

            Date date = DateUtils.parseDate(dateStr, "YYYY-MM");
            Date startDate = date;
            Date endDate = new DateTime(date).plusMonths(1).toDate();

            return acsDeviceFlowMapper.getUserFlow(userId, startDate, endDate);
        } catch (ParseException e) {
            log.error("[AcsDeviceFlowService#getUserFlow] parse date error, {}", dateStr, e);
            throw new IotException(ApiCode.BAD_REQUEST);
        }
    }

    public Result weeklyFlow(String company) {
        Map<String, List<Object>> weeklyFlow = new LinkedHashMap<>();
        List<Object> legend = new ArrayList<>();
        List<Object> series = new ArrayList<>();
        weeklyFlow.put("week", Arrays.asList(new String[]{"周一", "周二", "周三", "周四", "周五", "周六", "周日"}));
        weeklyFlow.put("legend", legend);
        weeklyFlow.put("series", series);

        // 获取公司下的 sn
        List<String> snList = acsUserDeviceService.findSnByCompany(company);
        if (CollectionUtils.isEmpty(snList)) {
            return Result.success(weeklyFlow);
        }
        // 整理返回数据
        List<Date> week = com.jetron.nb.common.util.DateUtils.getTimeInterval(new Date());//周一到周日的所有时间
        Date monday = com.jetron.nb.common.util.DateUtils.getMonday(new Date());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<AcsDeviceFlow> flows = acsDeviceFlowMapper.selectWeeklyFlow(snList, dateFormat.format(monday));

        legend = flows.stream().map(AcsDeviceFlow::getSn).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        ;

        Map<String, List<AcsDeviceFlow>> weekFlows = flows.stream().collect(Collectors.groupingBy(AcsDeviceFlow::getDate));

        for (Object snObj : legend) {
            Map<String, Object> flowMap = new HashMap<>();
            List<String> snFlowList = new ArrayList<>();
            for (int i = 0; i < week.size(); i++) {
                Date date = week.get(i);
                String dateStr = dateFormat.format(date);
                List<AcsDeviceFlow> flowList = weekFlows.get(dateStr);
                if (flowList == null) {
                    snFlowList.add("0");
                    continue;
                }
                Map<String, AcsDeviceFlow> snFlow = flowList.stream().collect(Collectors.toMap(AcsDeviceFlow::getSn, a -> a, (k1, k2) -> k1));
                AcsDeviceFlow flow = snFlow.get(snObj.toString());
                if (flow == null) {
                    snFlowList.add("0");
                } else {
                    snFlowList.add(String.valueOf(flow.getDownFlow()));
                }
            }
            flowMap.put("sn", snObj.toString());
            flowMap.put("data", snFlowList);
            series.add(flowMap);
        }
        weeklyFlow.put("legend", legend);
        return Result.success(weeklyFlow);
    }

    public Result dataLineChart(String company) {

        Map<String, Object> chartData = new LinkedHashMap<>();
        Date end = new Date();
        Date begin = DateUtils.addHours(end, -4);

        List<AcsCaptureData> chartData1 = acsCaptureDataMapper.findChartData(company, begin, end);

        Map<String, List<AcsCaptureData>> snMap = chartData1.stream().collect(Collectors.groupingBy(AcsCaptureData::getSn));
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");

        List<String> timeList = new ArrayList<>();
        for (String sn : snMap.keySet()) {
            List<String> temperatureList = new ArrayList<>();
            List<String> ramList = new ArrayList<>();
            List<String> signalQualityList = new ArrayList<>();
            List<String> rssiList = new ArrayList<>();
            List<String> cpuList = new ArrayList<>();
            List<AcsCaptureData> captureList = snMap.get(sn);
            captureList = captureList.stream().sorted(Comparator.comparing(AcsCaptureData::getInsertTime)).collect(Collectors.toList());
            Date date = begin;
            int i = 0;
            do {
                if (timeList.size() < 24) {
                    timeList.add(dateFormat.format(date));
                }
                if (captureList.size() <= i ||
                        com.jetron.nb.common.util.DateUtils.diffMinute(captureList.get(i).getInsertTime(), date) >= 10) {
                    date = DateUtils.addMinutes(date, 10);
                    temperatureList.add("0");
                    ramList.add("0");
                    signalQualityList.add("0");
                    rssiList.add("0");
                    cpuList.add("0");
                    continue;
                }
                AcsCaptureData captureData = captureList.get(i);

                temperatureList.add(captureData.getDevTemp());
                ramList.add(captureData.getRam().replace("Mb", ""));
                signalQualityList.add(captureData.getSignalQuality());
                rssiList.add(AcsCaptureData.sigStrengthTransform(captureData.getRssi()));
                cpuList.add(captureData.getCpuRation().replace("%", ""));

                i++;
                date = DateUtils.addMinutes(date, 10);
            } while (date.compareTo(end) <= 0);
            Map<String, List<String>> map = new HashMap<>();
            map.put("temperatureList", temperatureList);
            map.put("ramList", ramList);
            map.put("signalQualityList", signalQualityList);
            map.put("rssiList", rssiList);
            map.put("cpuList", cpuList);
            chartData.put(sn, map);
        }
        chartData.put("snList", snMap.keySet());
        chartData.put("time", timeList);
        return Result.success(chartData);
    }

    public int deleteByPrimaryKey(Integer id) {
        return acsDeviceFlowMapper.deleteByPrimaryKey(id);
    }

    public int insert(AcsDeviceFlow record) {
        return acsDeviceFlowMapper.insert(record);
    }

    public int insertSelective(AcsDeviceFlow record) {
        return acsDeviceFlowMapper.insertSelective(record);
    }

    public AcsDeviceFlow selectByPrimaryKey(Integer id) {
        return acsDeviceFlowMapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(AcsDeviceFlow record) {
        return acsDeviceFlowMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(AcsDeviceFlow record) {
        return acsDeviceFlowMapper.updateByPrimaryKey(record);
    }

    public Result flowStatisticsOfDashboard() {
        Map<String, Object> chartData = new LinkedHashMap<>();
        List<String> dateList = new ArrayList<>();
        List<String> down = new ArrayList<>();
        List<String> up = new ArrayList<>();
        chartData.put("dateList", dateList);
        chartData.put("down", down);
        chartData.put("up", up);

        Date now = new Date();

        Date begin = DateUtils.addDays(now, -6);
        begin = com.jetron.nb.common.util.DateUtils.getDawn(begin);
        Date end =  com.jetron.nb.common.util.DateUtils.getDayEnd(now);

        Integer userId = null;
        /*if (!MdcUtils.isAdmin()) {
            AcsUser currentAcsUser = MdcUtils.getCurrentAcsUser();
            userId = currentAcsUser.getId();
        }*/
        List<AcsDeviceFlow> userFlowWithDate = acsDeviceFlowMapper.getUserFlowWithDate(userId, begin, end);
        if (CollectionUtils.isEmpty(userFlowWithDate)) {
            return Result.success(chartData);
        }
        AcsDeviceFlow param = new AcsDeviceFlow();
        param.setStart(begin);
        param.setUserId(userId);
        List<AcsDeviceFlow> lastFlow = findLastFlow(param);
        userFlowWithDate = getTrueList(userFlowWithDate, lastFlow);

        List<Date> before7DayDate = com.jetron.nb.common.util.DateUtils.getBefore7DayDate(new Date());

        for (int i = 0; i < before7DayDate.size(); i++) {
            dateList.add(DateFormatUtils.format(before7DayDate.get(i), "MM-dd"));
        }

        for (int i = 0; i < dateList.size(); i++) {
            Date date = DateUtils.addDays(now, -6 + i);
            String downTemp = "0";
            String upTemp = "0";
            List<AcsDeviceFlow> collect = userFlowWithDate.stream().filter(obj -> DateUtils.isSameDay(date, obj.getGmtCreate())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                long downSum = collect.stream().mapToLong(AcsDeviceFlow::getDownFlow).sum();
                long upSum = collect.stream().mapToLong(AcsDeviceFlow::getUpFlow).sum();
                downTemp = String.valueOf(downSum / 1024 / 1024);
                upTemp = String.valueOf(upSum / 1024 / 1024);
            }
            down.add(downTemp);
            up.add(upTemp);
        }

        return Result.success(chartData);
    }

    public Result kanbanTitleQuery(String visitorId) {
        AcsKanbanTitle title = new AcsKanbanTitle();
        title.setVisitorId(visitorId);
        AcsKanbanTitle one = kanbanTitleMapper.findOne(title);
        return Result.success(one);
    }

    public Result kanbanTitleSave(String visitorId,String title) {
        AcsKanbanTitle param = new AcsKanbanTitle();
        param.setVisitorId(visitorId);
        AcsKanbanTitle one = kanbanTitleMapper.findOne(param);
        if (one == null) {
            param.setTitle(title);
            param.setGmtCreate(new Date());
            kanbanTitleMapper.insert(param);
        } else {
            one.setTitle(title);
            kanbanTitleMapper.update(one);
        }
        return Result.success();
    }

    public Result deviceDataLineChart(String sn, Date begin, Date end) {
        List<String> online = new ArrayList<>();
        List<String> onlineDate = new ArrayList<>();

        Map<String, List<String>> map1 = mqttOnLine(sn, begin, end);
        online = map1.get("online");
        onlineDate = map1.get("onlineDate");

        AcsCaptureData dataParam = new AcsCaptureData();
        dataParam.setSn(sn);
        dataParam.setStart(begin);
        dataParam.setEnd(end);
        List<AcsCaptureData> bySnAndDate = acsCaptureDataMapper.findBySnAndDate(dataParam);
        List<String> temperatureList = new ArrayList<>();
        List<String> ramList = new ArrayList<>();
        List<String> signalQualityList = new ArrayList<>();
        List<String> rssiList = new ArrayList<>();
        List<String> cpuList = new ArrayList<>();
        List<String> dateList = new ArrayList<>();
        List<String> rsrqList = new ArrayList<>();

        bySnAndDate = bySnAndDate.stream().sorted(Comparator.comparing(AcsCaptureData::getInsertTime)).collect(Collectors.toList());
        for (int i = 0; i < bySnAndDate.size(); i++) {
            AcsCaptureData captureData = bySnAndDate.get(i);
            if (StringUtils.isNotBlank(captureData.getDevTemp())) {
                temperatureList.add(captureData.getDevTemp());
            } else {
                temperatureList.add(null);
            }
            if (StringUtils.isNotBlank(captureData.getRam())) {
                ramList.add(captureData.getRam().replace("Mb", ""));
            } else {
                ramList.add(null);
            }
            if (StringUtils.isNotBlank(captureData.getSignalQuality())) {
                signalQualityList.add(captureData.getSignalQuality());
            } else {
                signalQualityList.add(null);
            }
            if (StringUtils.isNotBlank(captureData.getRsrq())) {
                rsrqList.add(captureData.getRsrq().replace("dB", ""));
            } else {
                rsrqList.add(null);
            }
            if (StringUtils.isNotBlank(captureData.getRssi())) {
                rssiList.add(AcsCaptureData.sigStrengthTransform(captureData.getRssi()));
            } else {
                rssiList.add(null);
            }
            if (StringUtils.isNotBlank(captureData.getCpuRation())) {
                cpuList.add(captureData.getCpuRation().replace("%", ""));
            } else {
                cpuList.add(null);
            }

            dateList.add(DateFormatUtils.format(captureData.getInsertTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        Map<String, List<String>> map = new HashMap<>();
        map.put("temperatureList", temperatureList);
        map.put("ramList", ramList);
        map.put("signalQualityList", signalQualityList);
        map.put("rssiList", rssiList);
        map.put("cpuList", cpuList);
        map.put("dateList", dateList);
        map.put("online", online);
        map.put("onlineDate", onlineDate);
        map.put("rsrqList", rsrqList);
        return Result.success(map);
    }

    private Map<String,List<String>>  mqttOnLine(String sn, Date begin, Date end) {
        AcsMqttOnline param = new AcsMqttOnline();
        param.setSn(sn);
        param.setStart(begin);
        param.setEnd(end);
        List<AcsMqttOnline> list = onlineService.findList2(param);
        List<String> online = new ArrayList<>();
        List<String> onlineDate = new ArrayList<>();

        AcsMqttOnline latest = onlineService.findLatest(sn,begin);
        if (latest != null) {
            online.add(""+latest.getStatus());
            onlineDate.add(DateFormatUtils.format(begin, "yyyy-MM-dd HH:mm:ss"));
        }
        if (CollectionUtils.isEmpty(list)) {
            // 如果所选时间段没有数据，则查询该设备最新的状态，视为设备当前的状态
            if (latest != null) {
                online.add(""+latest.getStatus());
                onlineDate.add(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
        } else {
            list.sort(Comparator.comparing(AcsMqttOnline::getInsertTime));
            for (int i = 0; i < list.size(); i++) {
                AcsMqttOnline heart = list.get(i);
                online.add("" + heart.getStatus());
                onlineDate.add(DateFormatUtils.format(heart.getInsertTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            // 在线时长数据，如果截至时间大于当前时间，那么添加一个当前的数据点
            if (end.compareTo(new Date()) > 0) {
                online.add("" + list.get(list.size() - 1).getStatus());
                onlineDate.add(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
            // 截至时间大于最后数据时间并且小于当前时间的情况
            if (end.compareTo(list.get(list.size() -1).getInsertTime()) > 0 && end.compareTo(new Date()) < 0) {
                online.add("" + list.get(list.size() - 1).getStatus());
                onlineDate.add(DateFormatUtils.format(end, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        Map<String, List<String>> map = new HashMap<>();
        map.put("online", online);
        map.put("onlineDate", onlineDate);
        return map;
    }

    private Map<String,List<String>>  heartOnline(String sn, Date begin, Date end) {
        AcsDeviceHeart param = new AcsDeviceHeart();
        param.setSn(sn);
        param.setStart(begin);
        param.setEnd(end);
        List<AcsDeviceHeart> list = heartService.findList(param);
        List<String> online = new ArrayList<>();
        List<String> onlineDate = new ArrayList<>();

        AcsDeviceHeart latest = heartService.findLatest(sn,begin);
        if (latest != null) {
            online.add(""+latest.getStatus());
            onlineDate.add(DateFormatUtils.format(begin, "yyyy-MM-dd HH:mm:ss"));
        }
        if (CollectionUtils.isEmpty(list)) {
            // 如果所选时间段没有数据，则查询该设备最新的状态，视为设备当前的状态
            if (latest != null) {
                online.add(""+latest.getStatus());
                onlineDate.add(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
        } else {
            list.sort(Comparator.comparing(AcsDeviceHeart::getInsertTime));
            for (int i = 0; i < list.size(); i++) {
                AcsDeviceHeart heart = list.get(i);
                online.add("" + heart.getStatus());
                onlineDate.add(DateFormatUtils.format(heart.getInsertTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            // 在线时长数据，如果截至时间大于当前时间，那么添加一个当前的数据点
            if (end.compareTo(new Date()) > 0) {
                online.add("" + list.get(list.size() - 1).getStatus());
                onlineDate.add(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
            // 截至时间大于最后数据时间并且小于当前时间的情况
            if (end.compareTo(list.get(list.size() -1).getInsertTime()) > 0 && end.compareTo(new Date()) < 0) {
                online.add("" + list.get(list.size() - 1).getStatus());
                onlineDate.add(DateFormatUtils.format(end, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        Map<String, List<String>> map = new HashMap<>();
        map.put("online", online);
        map.put("onlineDate", onlineDate);
        return map;
    }


    public Result flowEarlyWarn() {
        List<JSONObject>  dataList = new ArrayList<>();
        // 获取流量计算起始时间
        AcsCompanyDictionary dictParam = new AcsCompanyDictionary();
        dictParam.setDictType(Constants.CompanyDict.DICT_TYPE_FLOW);
        dictParam.setDictLabel(Constants.CompanyDict.DICT_lABEL_FLOW);
        AcsUser user = MdcUtils.getCurrentAcsUser();
        if (user != null) {
            dictParam.setCompany(user.getCompany());
        }
        List<AcsCompanyDictionary> dictList = companyDictionaryService.findList(dictParam);
        Map<String, String> companyDictMap = dictList.stream().collect(Collectors
                .toMap(AcsCompanyDictionary::getCompany, AcsCompanyDictionary::getDictValue, (k1, k2) -> k1));
        // 上月第一天
        Date lastMonthFirstDay = com.jetron.nb.common.util.DateUtils.getLastMonthFirstDay();
        // 查询两月流量
        List<AcsDeviceFlowMonth> flowList = flowMonthService.findList(new AcsDeviceFlowMonth().setStart(lastMonthFirstDay));


        Map<String, List<AcsDeviceFlowMonth>> flowMap = flowList.stream().collect(Collectors.groupingBy(AcsDeviceFlowMonth::getSn));

        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        List<AcsUserDevice> list = acsUserDeviceService.findListWithUser(deviceFilter);

        AcsParameterConfig configParam = new AcsParameterConfig();
        configParam.setParameterCode("flow");
        List<AcsParameterConfig> parameterConfigList = parameterConfigService.findList(configParam);
        // 获得每个设备的信息，与流量套餐，与阈值
        List<JSONObject>  snList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            AcsUserDevice device = list.get(i);
            String company = device.getUser().getCompany();
            String model = device.getModel();
            Double threshold = null;
            for (int j = 0; j < parameterConfigList.size(); j++) {
                AcsParameterConfig config = parameterConfigList.get(j);
                if (StringUtils.equals(config.getCompany(),company) && StringUtils.equals(model,config.getParameterKey())) {
                    threshold = config.getParameterValue();
                }
            }
            JSONObject snJSon  = new JSONObject();
            snJSon.put("sn",device.getSn());
            snJSon.put("alias",device.getAlias());
            snJSon.put("ip",device.getTrueIp());
            snJSon.put("flow",device.getFlow());
            snJSon.put("threshold",threshold);
            snJSon.put("company",company);
            snList.add(snJSon);
        }
        for (int i = 0; i < snList.size(); i++) {
            JSONObject snJson = snList.get(i);
            String company = snJson.getString("company");
            String sn = snJson.getString("sn");
            Double threshold = snJson.getDouble("threshold");// 阈值（80%）
            Double flowPackage = snJson.getDouble("flow");// 流量（100）单位 M
            if (!flowMap.containsKey(sn)) {
                continue;
            }
            String dictValueStr = companyDictMap.get(company);
            Integer dictValue = StringUtils.isNotBlank(dictValueStr) ? Integer.valueOf(dictValueStr) : null;
            String currentMonthStr = com.jetron.nb.common.util.DateUtils.getCurrentMonth(dictValue);
            // 计算当月总流量
            List<AcsDeviceFlowMonth> snFlowList = flowMap.get(sn);
            List<AcsDeviceFlowMonth> collect = snFlowList.stream().filter(obj -> currentMonthStr.equals(obj.getDate()))
                    .collect(Collectors.toList());
            Long monthFlow = 0L;
            if (!CollectionUtils.isEmpty(collect)) {
                monthFlow = collect.get(0).getUpFlow();
            }
            monthFlow = monthFlow / 1024 / 1024;
            // 判断是否超过报警阈值
            if (threshold != null && flowPackage != null
                    && threshold * flowPackage <= monthFlow) {
                snJson.put("currentFlow",Math.floor(monthFlow));
                dataList.add(snJson);
            }
        }
        return Result.success(dataList);
    }


    /**
     * 旧
     * @return
     */
    public Result flowEarlyWarn2() {
        List<JSONObject>  dataList = new ArrayList<>();
        // 获取流量计算起始时间
        Integer dictValue = null;
        AcsCompanyDictionary dictParam = new AcsCompanyDictionary();
        dictParam.setDictType(Constants.CompanyDict.DICT_TYPE_FLOW);
        dictParam.setDictLabel(Constants.CompanyDict.DICT_lABEL_FLOW);
        AcsUser user = MdcUtils.getCurrentAcsUser();
        if (user != null) {
            dictParam.setCompany(user.getCompany());
        }
        AcsCompanyDictionary one = companyDictionaryService.findOne(dictParam);
        if (one != null) {
            dictValue = Integer.valueOf(one.getDictValue());
        }
        Date currentMonthStartDate = com.jetron.nb.common.util.DateUtils.getStartDay(dictValue);
        Date lastMonthEndDate = DateUtils.addSeconds(currentMonthStartDate,-1);
        Map<String, List<AcsDeviceFlow>> flowMap = null;
        // 查询当月所有流量
        AcsDeviceFlow flowParam = new AcsDeviceFlow();
        flowParam.setStart(currentMonthStartDate);
        List<AcsDeviceFlow> flowList = this.findList(flowParam);
        AcsDeviceFlow param = new AcsDeviceFlow();
        param.setStart(lastMonthEndDate);
        List<AcsDeviceFlow> lastList = this.findLastFlow(param);
        // 获取计划后的正确值
        flowList = getTrueList(flowList, lastList);
        if (CollectionUtils.isEmpty(flowList)) {
            return Result.success(dataList);
        }
        flowMap = flowList.stream().collect(Collectors.groupingBy(AcsDeviceFlow::getSn));

        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        /*if (!MdcUtils.isAdmin()) {
            deviceFilter.setBelongTo(MdcUtils.getCurrentAcsUser().getId());
        }*/
        List<AcsUserDevice> list = acsUserDeviceService.findListWithUser(deviceFilter);

        AcsParameterConfig configParam = new AcsParameterConfig();
        configParam.setParameterCode("flow");
        List<AcsParameterConfig> parameterConfigList = parameterConfigService.findList(configParam);
        // 获得每个设备的信息，与流量套餐，与阈值
        List<JSONObject>  snList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            AcsUserDevice device = list.get(i);
            String company = device.getUser().getCompany();
            String model = device.getModel();
            Double threshold = null;
            for (int j = 0; j < parameterConfigList.size(); j++) {
                AcsParameterConfig config = parameterConfigList.get(j);
                if (StringUtils.equals(config.getCompany(),company) && StringUtils.equals(model,config.getParameterKey())) {
                    threshold = config.getParameterValue();
                }
            }
            JSONObject snJSon  = new JSONObject();
            snJSon.put("sn",device.getSn());
            snJSon.put("alias",device.getAlias());
            snJSon.put("ip",device.getTrueIp());
            snJSon.put("flow",device.getFlow());
            snJSon.put("threshold",threshold);
            snList.add(snJSon);
        }
        for (int i = 0; i < snList.size(); i++) {
            JSONObject snJson = snList.get(i);
            String sn = snJson.getString("sn");
            Double threshold = snJson.getDouble("threshold");// 阈值（80%）
            Double flowPackage = snJson.getDouble("flow");// 流量（100）单位 M
            if (!flowMap.containsKey(sn)) {
                continue;
            }
            // 计算当月总流量
            Long monthFlow = 0L;
            List<AcsDeviceFlow> snFlowList = flowMap.get(sn);
            monthFlow = snFlowList.stream().mapToLong(AcsDeviceFlow::getSumFlow).sum();
            monthFlow = monthFlow / 1024 / 1024;
            // 判断是否超过报警阈值
            if (threshold != null && flowPackage != null
                    && threshold * flowPackage <= monthFlow) {
                snJson.put("currentFlow",Math.floor(monthFlow));
                dataList.add(snJson);
            }
        }
        return Result.success(dataList);
    }

    public Map<String, Object> onOffLineTable(Integer page, Integer size,String sn,Date begin, Date end) {
        AcsDeviceHeart param = new AcsDeviceHeart();
        param.setStart(begin);
        param.setEnd(end);
        param.setSn(sn);
        // 查询所有数
        return heartService.findPageWithUserDevice(page,size,param);
    }
}
