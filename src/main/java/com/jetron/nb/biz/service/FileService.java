package com.jetron.nb.biz.service;

import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.biz.oss.LocalFileClient;
import com.jetron.nb.biz.oss.ObsFileClient;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

@Service
@Slf4j
public class FileService {

    @Autowired
    private LocalFileClient localFileClient;

    @Value("${app.output}")
    private String appDir;

    @Value("${app.deployType}")
    private String deployType;

    public Result chunkDownload(HttpServletRequest request, HttpServletResponse response,
                                String root, String name) {
        RandomAccessFile randomAccessFile = null;
        try {
            File file = null;
            String filePath = root + "/" + name;
            appDir = null;
            if ("local".equals(deployType)) {
                LocalFileClient local = (LocalFileClient) FileClientFactory.getFileClient("local");
                file = new File(appDir + "/" + filePath);
            } else {
                ObsFileClient client = (ObsFileClient) FileClientFactory.getFileClient("net");
                InputStream fileStream = client.getFileStream(filePath);
                file = new File(appDir + "/" + filePath);
                if (!file.exists()) {
                    if (!file.getParentFile().exists()) {
                        file.getParentFile().mkdirs();
                    }
                    file.createNewFile();
                }
                FileUtils.copyInputStreamToFile(fileStream, file);
            }
            String range = request.getHeader("Range");
            // 开始下载位置
            long startByte = 0;
            // 结束下载位置
            long endByte = file.length() - 1;
            // 有range的话
            if (range != null && range.contains("bytes=") && range.contains("-")) {
                // http状态码要为206：表示获取部分内容
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                range = range.substring(range.lastIndexOf("=") + 1).trim();
                String[] ranges = range.split("-");
                try {
                    // 判断range的类型
                    if (ranges.length == 1) {
                        // 类型一：bytes=-2343
                        if (range.startsWith("-")) {
                            endByte = Long.parseLong(ranges[0]);
                        } else if (range.endsWith("-")) {
                            // 类型二：bytes=2343-
                            startByte = Long.parseLong(ranges[0]);
                        }
                    } else if (ranges.length == 2) {
                        // 类型三：bytes=22-2343
                        startByte = Long.parseLong(ranges[0]);
                        endByte = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    startByte = 0;
                    endByte = file.length() - 1;
                    log.error("Range Occur Error,Message:{}", e.getLocalizedMessage());
                }
            }
            // 要下载的长度
            long contentLength = endByte - startByte + 1;
            if (contentLength <= 0L) {
                return Result.toResult(ApiCode.BAD_REQUEST);
            }
            OutputStream fos = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(fos);

            response.addHeader("Access-Contro1-A11ow-0rigin", "*");
            // 支持断点续传，获取部分字节内容：
            response.setHeader("Accept-Ranges", "bytes");
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-Length", String.valueOf(contentLength));
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            response.setHeader("Content-disposition", "inline;filename=" + new String(name.getBytes("UTF-8"), "ISO-8859-1"));
            // Content-Range，格式为：[要下载的开始位置]-[结束位置]/[文件总大小]
            response.setHeader("Content-Range", "bytes " + startByte + "-" + endByte + "/" + file.length());
            randomAccessFile = new RandomAccessFile(file, "r");
            long transmitted = 0;
            byte[] buff = new byte[4096];
            int len = 0;
            randomAccessFile.seek(startByte);
            // warning：判断是否到了最后不足4096（buff的length）个byte这个逻辑（(transmitted + len) <= contentLength）要放前面
            // 不然会会先读取randomAccessFile，造成后面读取位置出错;
            while ((transmitted + len) <= contentLength && (len = randomAccessFile.read(buff)) != -1) {
                bos.write(buff, 0, len);
                transmitted += len;
            }
            // 处理不足buff.length部分
            if (transmitted < contentLength) {
                len = randomAccessFile.read(buff, 0, (int) (contentLength - transmitted));
                bos.write(buff, 0, len);
                transmitted += len;
            }
            bos.flush();
            response.flushBuffer();
            randomAccessFile.close();
            if (!"local".equals(deployType)) {
                file.delete();
            }
        } catch (Exception e) {
            log.error("[FileController#chunkDownload] exception, message: {}", e);
            return Result.toResult(ApiCode.SERVER_ERROR);
        } finally {
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (IOException e) {
                    log.error("[FileController#randomAccessFile.close] exception, message: {}", e);
                }
            }
        }
        return Result.success();
    }
}
