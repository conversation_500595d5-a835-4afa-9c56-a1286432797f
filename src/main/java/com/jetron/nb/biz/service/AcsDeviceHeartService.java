package com.jetron.nb.biz.service;

import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.AcsDeviceHeartMapper;
import com.jetron.nb.dal.dao.mqtt.AcsMqttOnlineMapper;
import com.jetron.nb.dal.po.AcsDeviceHeart;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AcsDeviceHeartService {
    @Resource
    private AcsDeviceHeartMapper heartMapper;

    @Transactional
    public int insert(AcsDeviceHeart deviceHeart) {
        return heartMapper.insert(deviceHeart);
    }
    @Transactional
    public int insertList(List<AcsDeviceHeart> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return heartMapper.insertList(list);
    }

    public Map<String, Object> findPage(Integer page, Integer size, AcsDeviceHeart param) {
        param.setLimit(size);
        param.setOffset((page - 1) * size);
        // 查询所有数
        List<AcsDeviceHeart> list = findList(param);
        int count = findCount(param);
        return PageUtils.toPageResult(count, page, size, list);
    }

    public Map<String, Object> findPageWithUserDevice(Integer page, Integer size, AcsDeviceHeart param) {
        param.setLimit(size);
        param.setOffset((page - 1) * size);
        // 查询所有数
        List<AcsDeviceHeart> list = findListWithUserDevice(param);
        int count = findCount(param);
        return PageUtils.toPageResult(count, page, size, list);
    }

    public List<AcsDeviceHeart> findList(AcsDeviceHeart deviceHeart) {
        return heartMapper.findList(deviceHeart);
    }

    public List<AcsDeviceHeart> findListWithUserDevice(AcsDeviceHeart deviceHeart) {
        return heartMapper.findListWithUserDevice(deviceHeart);
    }

    public int findCount(AcsDeviceHeart deviceHeart) {
        return heartMapper.findCount(deviceHeart);
    }

    public AcsDeviceHeart findLatest(String sn, Date begin) {
        return heartMapper.findLatest(sn,begin);
    }

    public List<AcsDeviceHeart> findAllLatest(Date begin) {
        return heartMapper.findAllLatest(begin);
    }
}
