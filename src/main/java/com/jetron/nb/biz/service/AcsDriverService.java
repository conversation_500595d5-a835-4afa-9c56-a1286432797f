package com.jetron.nb.biz.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jetron.nb.biz.oss.FileClientUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.dal.dao.AcsDriverMapper;
import com.jetron.nb.dal.po.AcsDriver;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.web.common.config.NMSConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Service
public class AcsDriverService {

    @Autowired
    private AcsDriverMapper mapper;

    public QueryWrapper<AcsDriver> wrapper(AcsDriver param) {
        QueryWrapper<AcsDriver> wrapper = new QueryWrapper<AcsDriver>();
        if (param == null) {
            return wrapper;
        }
        if (param.getId() != null) {
            wrapper.eq("id",param.getId());
        }
        if (!CollectionUtils.isEmpty(param.getIdList())) {
            wrapper.in("id",param.getIdList());
        }
        if (StringUtils.isNotBlank(param.getCompany())) {
            wrapper.eq("company",param.getCompany());
        }
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.like("name",param.getName());
        }
        if (StringUtils.isNotBlank(param.getProtocol())) {
            wrapper.eq("protocol",param.getProtocol());
        }
        return wrapper;
    }

    public Map<String, Object> page(Integer pageNum, Integer pageSize, AcsDriver param) {
        Page<AcsDriver> page = new Page<>(pageNum,pageSize);

        AcsUser currentAcsUser = MdcUtils.getCurrentAcsUser();
        param.setCompany(currentAcsUser.getCompany());
        QueryWrapper<AcsDriver> wrapper = wrapper(param);

        Long count = mapper.selectCount(wrapper);
        Page<AcsDriver> drivePage = mapper.selectPage(page, wrapper);
        List<AcsDriver> records = drivePage.getRecords();
        return PageUtils.toPageResult(Math.toIntExact(count),pageNum,pageSize,records);
    }

    public AcsDriver find(AcsDriver param) {
        return mapper.selectOne(wrapper(param));
    }

    public List<AcsDriver> list(AcsDriver param) {
        return mapper.selectList(wrapper(param));
    }

    public void add(String name, String protocol, MultipartFile file) {
        String path = FileClientUtils.saveFile(file, NMSConfig.deployType,"driver/");
        AcsDriver drive = new AcsDriver();
        drive.setCompany(MdcUtils.getCurrentAcsUser().getCompany());
        drive.setName(name);
        drive.setProtocol(protocol);
        drive.setPath(path);
        mapper.insert(drive);
    }

    public void save(AcsDriver param) {
        mapper.updateById(param);
    }

    public void deleteList(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        mapper.deleteBatchIds(list);
    }


}
