package com.jetron.nb.biz.service;

import com.google.common.base.Splitter;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.oss.FileClient;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.FileUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.ConfigInfo;
import com.jetron.nb.dal.dao.AcsConfigShareMapper;
import com.jetron.nb.dal.dao.AcsConfigUpgradeMapper;
import com.jetron.nb.dal.dao.AcsUserDeviceMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.jetron.nb.dal.dao.AcsConfigMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsConfigService {

    private static final String UPLOAD_DIR = "/Users/<USER>/config/%s";

    @Resource
    private AcsConfigMapper acsConfigMapper;
    @Resource
    private AcsUserDeviceMapper acsUserDeviceMapper;
    @Resource
    public AcsUserDeviceService userDeviceService;
    @Resource
    private AcsConfigUpgradeMapper acsConfigUpgradeMapper;
    @Resource
    private MqttMessageHandler mqttMessageHandler;
    @Resource
    private AcsConfigShareMapper acsConfigShareMapper;

    @Value("${app.deployType}")
    private String deployType;

    public void upload(String name, String model, String describ, MultipartFile file) {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        this.checkName(name, currentUser.getId());
        String filePath = this.saveFile(file);

        AcsConfig newConfig = new AcsConfig();
        newConfig.setName(name);
        newConfig.setModel(model);
        newConfig.setDescrib(describ);
        newConfig.setBelongTo(currentUser.getId());
        newConfig.setPath(filePath);
        newConfig.setCompany(currentUser.getCompany());
        if (MdcUtils.isAdmin()) {
            newConfig.setType(0);
        } else {
            newConfig.setType(1);
        }

        acsConfigMapper.insertSelective(newConfig);
    }

    private void checkName(String name, Integer userId) {
        ConfigFilter filter = new ConfigFilter();
        filter.setBelongTo(userId);
        filter.setName(name);
        filter.setIncludeSys(true);
        List<AcsConfig> configList = acsConfigMapper.getByFilter(filter).stream()
                .filter(x->x.getName().equals(name)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(configList)) {
            throw new IotException(ApiCode.CONFIG_ALREADY_EXIST);
        }
    }

    private String saveFile(MultipartFile file) {
        try {
            FileClient fileClient;
            if ("local".equals(deployType)) {
                fileClient = FileClientFactory.getFileClient("local");
            } else {
                fileClient = FileClientFactory.getFileClient("net");
            }

            String ext = FileUtils.getExtName(file.getOriginalFilename());
            String filePath = fileClient.upload(file.getInputStream(), "config/", ext);

            return filePath;
        } catch (IOException e) {
            log.error("[AcsConfigService#saveFile] error, {}", e);
            throw new IotException(ApiCode.CONFIG_UPLOAD_FAILED);
        }
    }

    public Map<String, Object> search(String name, String model, Integer page, Integer size) {
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = 20;
        }

        ConfigFilter configFilter = new ConfigFilter();
        configFilter.setName(name);
        configFilter.setModel(model);
        configFilter.setIncludeSys(true);
        configFilter.setOffset((page - 1) * size);
        configFilter.setLimit(size);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        configFilter.setBelongTo(currentUser.getId());

        List<AcsConfig> devices = acsConfigMapper.getByFilter(configFilter);

        // 如果是用户管理员，除了查看自己上传的配置外，还可以查看超级管理员分享给他的配置
        if (currentUser.getRole() == RoleEnum.USER_ADMIN.role) {
            devices = devices.stream().filter( d -> currentUser.getCompany().equals(d.getCompany())).collect(Collectors.toList());
            devices.addAll(adminConfigShare(currentUser.getCompany(), name, model));
        }

        List<ConfigInfo> deviceInfos = toInfo(devices);
        int total = acsConfigMapper.countByFilter(configFilter);

        return PageUtils.toPageResult(total, page, size, deviceInfos);

    }

    /**
     *  超级管理员分享的配置
     * @param company 公司名
     * @return 配置
     */
    private List<AcsConfig> adminConfigShare(String company, String name, String model) {
        List<AcsConfig> acsConfigs = new ArrayList<>();
        List<AcsConfigShare> acsConfigShares = acsConfigShareMapper.findByCompany(company);
        if (acsConfigShares.size() <= 0) {
            return acsConfigs;
        }
        List<Integer> ids = acsConfigShares.stream().map(AcsConfigShare::getConfigId).collect(Collectors.toList());
        acsConfigs = acsConfigMapper.selectByIdList(ids);
        if (!StringUtils.isEmpty(name)) {
            acsConfigs = acsConfigs.stream().filter( x -> name.equals(x.getName())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(model)) {
            acsConfigs = acsConfigs.stream().filter( x -> model.equals(x.getModel())).collect(Collectors.toList());
        }
        return acsConfigs;
    }

    private List<ConfigInfo> toInfo(List<AcsConfig> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return Collections.EMPTY_LIST;
        }

        List<ConfigInfo> configInfos = Lists.newArrayList();
        for (AcsConfig config : devices) {
            ConfigInfo info = new ConfigInfo();
            BeanUtils.copyProperties(config, info);

            configInfos.add(info);
        }
        return configInfos;
    }


    @Transactional
    public void upgrade(Integer configId, String deviceIds) {
        AcsConfig config = acsConfigMapper.selectByPrimaryKey(configId);

        StringBuilder sb = new StringBuilder("升级配置：")
                .append("(" + config.getName() + "," + config.getModel() + "," + config.getDescrib() + ")\n");

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (config == null ||
                (config.getType() != 0 && !Objects.equals(config.getBelongTo(), currentUser.getId()))) {
            throw new IotException(ApiCode.CONFIG_NOT_EXIST);
        }

        List<Integer> deviceIdList = Splitter.on(",").splitToList(deviceIds)
                .stream()
                .map(e->Integer.valueOf(e))
                .collect(Collectors.toList());
        // 配置升级一次不能超过100台
        if (deviceIdList.size() > 100) {
            throw new IotException(ApiCode.DEVICE_UPGRADE_COUNT_EXCEED_MAX);
        }

        UserDeviceFilter userDeviceFilter = new UserDeviceFilter();
        userDeviceFilter.setDeviceIds(deviceIdList);
        if (!MdcUtils.isAdmin()) {
            userDeviceFilter.setBelongTo(currentUser.getId());
        }

        List<AcsUserDevice> userDeviceList = userDeviceService.findList(userDeviceFilter);
        if (userDeviceList.size() != deviceIdList.size()) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        sb.append(",升级设备：");
        for(AcsUserDevice device : userDeviceList) {
            sb.append("(" + device.getName() + "," + device.getSn() + "," + device.getModel() + ")\n");
        }

        sb.append(",升级结果：");
        MdcUtils.appendDetailLog(sb.toString());

        List<AcsConfigUpgrade> insertRecords = Lists.newArrayList();
        for (Integer deviceId : deviceIdList) {
            AcsConfigUpgrade upgrade = new AcsConfigUpgrade();
            upgrade.setConfigId(configId);
            upgrade.setDeviceId(deviceId);
            upgrade.setBelongTo(currentUser.getId());
            upgrade.setStatus(0);

            insertRecords.add(upgrade);
        }

        acsConfigUpgradeMapper.batchInsert(insertRecords);
        userDeviceService.setUpgradeStatus(deviceIdList, 1);

        String url = null;
        if ("local".equals(deployType)) {
            url = FileClientFactory.getFileClient("local").getUrl(config.getPath());
        } else {
            url = FileClientFactory.getFileClient("net").getUrl(config.getPath());
        }
//        List<String> snList = userDeviceList.stream().map(AcsUserDevice::getSn).collect(Collectors.toList());
        Map<String, Boolean> snMap = userDeviceList.stream()
                .collect(Collectors.toMap(AcsUserDevice::getSn, AcsUserDevice::getUpgradeDevice));
        mqttMessageHandler.configUpgrade(snMap, config.getName(), url);

        log.info("[AcsConfigService#upgrade] success!");
    }


    @Transactional
    public void delete(Integer configId) {
        AcsConfig config = acsConfigMapper.selectByPrimaryKey(configId);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (config == null) {
            throw new IotException(ApiCode.CONFIG_NOT_EXIST);
        }
        if (!MdcUtils.isAdmin() && !Objects.equals(config.getBelongTo(), currentUser.getId())){
            throw new IotException(ApiCode.CONFIG_VALID_USER);
        }

        int count1 = acsConfigUpgradeMapper.deleteConfigId(configId);
        int count2 = acsConfigMapper.deleteByPrimaryKey(configId);

        if ("local".equals(deployType)) {
            FileClientFactory.getFileClient("local").delete(config.getPath());
        } else {
            FileClientFactory.getFileClient("net").delete(config.getPath());
        }

        log.info("delete config success, name: {}, {}, {}", config.getName(), count1, count2);
    }

    public int deleteByPrimaryKey(Integer id) {
        return acsConfigMapper.deleteByPrimaryKey(id);
    }

    
    public int insert(AcsConfig record) {
        return acsConfigMapper.insert(record);
    }

    
    public int insertSelective(AcsConfig record) {
        return acsConfigMapper.insertSelective(record);
    }

    
    public AcsConfig selectByPrimaryKey(Integer id) {
        return acsConfigMapper.selectByPrimaryKey(id);
    }

    
    public int updateByPrimaryKeySelective(AcsConfig record) {
        return acsConfigMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(AcsConfig record) {
        return acsConfigMapper.updateByPrimaryKey(record);
    }

    public void insertConfigShare(AcsConfigShare share) {

        List<AcsConfigShare> list = new ArrayList<>();
        list.add(share);
        acsConfigShareMapper.insertList(list);
    }

    public List<AcsConfigShare> searchConfigShare(AcsConfigShare share) {
        return acsConfigShareMapper.findList(share);
    }

}
