package com.jetron.nb.biz.service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolService {
    private volatile static ExecutorService executorService = null;

    public ThreadPoolService() {

    }

    public static ExecutorService getInstance() {
        if (executorService == null) {
            synchronized (ThreadPoolService.class) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(2,3,1000, TimeUnit.MILLISECONDS,
                            new LinkedBlockingDeque<Runnable>(1024), new ThreadFactoryBuilder().setNameFormat("nms-%d").build(),
                            new ThreadPoolExecutor.DiscardOldestPolicy());
                }
            }
        }

        return executorService;
    }
}
