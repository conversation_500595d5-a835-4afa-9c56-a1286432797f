package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.jetron.nb.IotPlatformApplication;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.mqtt.processor.BindIPProcessor;
import com.jetron.nb.biz.mqtt.processor.LANIPProcessor;
import com.jetron.nb.biz.mqtt.processor.NTPProcessor;
import com.jetron.nb.biz.mqtt.processor.NetworkMonitorProcessor;
import com.jetron.nb.biz.oss.BootstrapConstsLocal;
import com.jetron.nb.biz.oss.FileClient;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.biz.service.mqtt.AcsMqttService;
import com.jetron.nb.common.constant.*;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.*;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.common.vo.UserDeviceInfo;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.dao.*;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AcsUserDeviceService {
    @Value("${heartbeat.interval}")
    private Integer heartBeat;
    @Value("${iotPlatform.url}")
    private String platformUrl;
    @Value("${app.deployType}")
    private String deployType;

    @Value("${cert.dir}")
    private String certDir;

    public static final int DEVICE_NORMAL = 0;
    public static final int DEVICE_ALARM = 1;

    public static String certFile = "client.crt";
    public static String keyFile = "client.key";
    public static String caFile = "root-ca.crt";

    @Resource
    private AcsUserDeviceMapper acsUserDeviceMapper;
    @Autowired
    private AcsUserMapper acsUserMapper;
    @Autowired
    private AcsDeviceMapper acsDeviceMapper;
    @Autowired
    private AcsUserService acsUserService;
    @Autowired
    private MqttMessageHandler mqttMessageHandler;
    @Autowired
    private AcsMqttService acsMqttService;
    @Autowired
    private AcsTopicService acsTopicService;
    @Autowired
    private MqttClientManager mqttClientManager;
    @Autowired
    private RedisServiceImpl redisService;
    @Autowired
    AcsTopicService topicService;
    @Autowired
    ContainerErrorService containerErrorService;
    @Autowired
    private AcsCaptureDataService captureDataService;
    @Autowired
    private AcsRealDataService realDataService;
    @Autowired
    private AcsDeviceService acsDeviceService;
    @Autowired
    private AcsPortMappingMapper portMappingMapper;
    @Autowired
    private AcsNatMapper natMapper;
    @Autowired
    private AcsPortMappingDeviceMapper portMappingDeviceMapper;
    @Autowired
    private AcsDeviceTestReportMapper testReportMapper;

    private static ThreadPoolExecutor executor;
    static {
        executor = new ThreadPoolExecutor(1, 2, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1000)
                , new ThreadFactoryBuilder().setNameFormat("device-%d").build());
    }

    @PostConstruct
    public void init() throws IOException {
        IotPlatformApplication.addPreHaltTask(new Runnable() {
            @Override
            public void run() {
                try {
                    Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
                    executor.shutdown();
                    while(!executor.isTerminated()) {
                        log.info("[AcsUserDeviceService#preHaltTask] not finished yet, active task count: {}", executor.getActiveCount());
                        executor.awaitTermination(1, TimeUnit.SECONDS);
                    }
                    log.info("[AcsUserDeviceService#preHaltTask] end");
                } catch (Exception e) {
                    log.info("[AcsUserDeviceService#preHaltTask] failed, {}", e);
                }
            }
        });
    }

    public Map<String, Object> search(UserDeviceFilter deviceFilter, String name, Integer page, Integer size) {
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = 20;
        }


        // 设置belongTo
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            deviceFilter.setBelongTo(currentUser.getId());
        } else if (currentUser.getRole().intValue() == RoleEnum.USER.role) {
            deviceFilter.setBelongTo(currentUser.getParentId());
        } else if (StringUtils.isNotBlank(name)) {
            UserFilter userFilter = new UserFilter();
            userFilter.setName(name);
            userFilter.setNameFuzzy(true);
            List<AcsUser> acsUsers = acsUserMapper.getByFilter(userFilter);
            if (CollectionUtils.isEmpty(acsUsers)) {
                return PageUtils.toPageResult(0, page, size, Collections.emptyList());
            }

            List<Integer> userIds = acsUsers.stream().map(u -> u.getId()).distinct().collect(Collectors.toList());
            deviceFilter.setBelongToList(userIds);
        }

        deviceFilter.setOffset((page - 1) * size);
        deviceFilter.setLimit(size);

        List<AcsUserDevice> devices = this.findListCaptureData(deviceFilter);
        int total = acsUserDeviceMapper.countByFilter(deviceFilter);

        List<UserDeviceInfo> deviceInfos = toDeviceInfo(devices);
        return PageUtils.toPageResult(total, page, size, deviceInfos);

    }

    public List<AcsUserDevice> findList(UserDeviceFilter deviceFilter) {
        return acsUserDeviceMapper.getByFilter(deviceFilter);
    }

//    @Transactional(rollbackFor = Exception.class)
    public List<AcsUserDevice> findListWithUser(UserDeviceFilter deviceFilter) {
        return acsUserDeviceMapper.findListWithUser(deviceFilter);
    }

    public List<AcsUserDevice> findListWithPoint(UserDeviceFilter deviceFilter) {
        return acsUserDeviceMapper.findListWithPoint(deviceFilter);
    }

    public List<AcsUserDevice> findListCaptureData(UserDeviceFilter deviceFilter) {
        List<AcsUserDevice> list = this.findList(deviceFilter);
        List<AcsRealData> realList = realDataService.list(new AcsRealData());
        Map<String, AcsRealData> collect =
                realList.stream().collect(Collectors.toMap(AcsRealData::getSn, obj -> obj));
        for (int i = 0; i < list.size(); i++) {
            AcsUserDevice device = list.get(i);
            String sn = device.getSn();
            if (collect.containsKey(sn)) {
                AcsRealData data = collect.get(sn);
                device.setSignalQuality(data.getRsrq());
            }
        }
        return list;
    }

    public List<UserDeviceInfo> toDeviceInfo(List<AcsUserDevice> deviceList) {
        // 通过EMQX查询在线设备SN；异步执行数据读取
        FutureTask<Result> clientsOnline = new FutureTask<>(new Callable<Result>() {
            @Override
            public Result call() throws Exception {
                return MqttUtils.getClinets();
            }
        });
        executor.execute(clientsOnline);

        List<UserDeviceInfo> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(deviceList)) {
            return result;
        }

        // 获取用户信息
        List<Integer> userIds = deviceList.stream().map(AcsUserDevice::getBelongTo)
                .distinct().collect(Collectors.toList());
        UserFilter filter = new UserFilter();
        filter.setUserIds(userIds);
        List<AcsUser> users = acsUserMapper.getByFilter(filter);
        Map<Integer, AcsUser> userMap = users.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
        Date current = new Date();
        for (AcsUserDevice userDevice : deviceList) {
            UserDeviceInfo info = JSONObject.parseObject(JSONObject.toJSONString(userDevice), UserDeviceInfo.class);
            // 用户信息
            AcsUser user = userMap.get(userDevice.getBelongTo());
            info.setUsername(user.getName());
            info.setCompany(user.getCompany());
            info.setDeviceStatus(userDevice.getStatus());
            info.setSignal5G(userDevice.getSignal5g());
            // 设备状态 ，0 不在线
            info.setStatus(0);
            // info.setOnlineTime(status == 0 ? 0 : userDevice.getOnlineTime());
            Date loginDate = userDevice.getLoginDate() == null ? current : userDevice.getLoginDate();
            // 如果loginDate没有相关时间值，表示设备离线或NMS新版本更新后设备状态未改变，使用原来的onlineTime表示在线时长
            info.setOnlineTime(userDevice.getLoginDate() == null ? userDevice.getOnlineTime() : Math.round((current.getTime() - loginDate.getTime()) / 1000));

            // 设备访问地址
            info.setDeviceUrl(getHostUrl(userDevice));
            result.add(info);
        }
        try {
            Result result1 = clientsOnline.get();
            if (ResultUtils.isSuccess(result1)) {
                List<MqttUtils.Client> clientList = (List<MqttUtils.Client>) result1.getData();
                List<String> snList = new ArrayList<>();
                for (int i = 0; i < clientList.size(); i++) {
                    MqttUtils.Client client = clientList.get(i);
                    String sn = MqttUtils.analyseSN(client.getClientid());
                    if (client.getConnected()!= null && client.getConnected() && !snList.contains(sn)) {
                        snList.add(sn);
                    }
                }
                for (int i = 0; i < result.size(); i++) {
                    UserDeviceInfo deviceInfo = result.get(i);
                    String sn = deviceInfo.getSn();
                    if (snList.contains(sn)) {
                        deviceInfo.setStatus(1);
                    }
                }
            }
        } catch (InterruptedException e) {
            log.error("" + e);
        } catch (ExecutionException e) {
            log.error("" + e);
        }

        return result;
    }

    public Result exportUserDeviceExcel(UserDeviceFilter filter, HttpServletRequest request, HttpServletResponse resp) {
        // 设置belongTo
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            filter.setBelongTo(currentUser.getId());
        } else if (currentUser.getRole().intValue() == RoleEnum.USER.role) {
            filter.setBelongTo(currentUser.getParentId());
        }
        if (filter.getModel() != null && filter.getModel().equals("all")) {
            filter.setModel(null);
        }

        List<AcsUserDevice> list = this.findList(filter);
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtils.newResult.fail("无符合条件记录！");
        }
        List<UserDeviceInfo> userDeviceInfos = toDeviceInfo(list);
        List<Map<String, Object>> beanMaps = new ArrayList<>();
        try {
            beanMaps = ExcelUtils.getBeanMaps(userDeviceInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (int i = 0; i < beanMaps.size(); i++) {
            Map<String, Object> map = beanMaps.get(i);
            Integer status = (Integer)map.get("status");
            Integer deviceStatus = (Integer) map.get("deviceStatus");
            map.put("status",(int)status == 0 ? "离线" : "在线");
            map.put("deviceStatus",(int)deviceStatus == 0 ? "正常" : "报警");
        }
        String[] keys = new String[]{
                "name","sn","model","subIp","dumIp","trueIp","status","onlineTime","verNo"
                ,"hardVerNo","signalNum","signal5G","describ","temperature"
                ,"cellId","imsi","sinr","networkType","deviceStatus","company"
                ,"username","alias","position","flow","villageNum","iccid","signalQuality"};
        String[] heads = new String[]{
                "网关名称","序列号","型号","子网地址","虚拟IP","SIM卡IP","在线状态","在线时长","软件版本"
                ,"硬件版本","移动通信号码","信号强度RSRP","描述","设备温度","小区号","IMSI","信噪比SINR","网络类型"
                ,"设备状态","公司","用户","别名","位置描述","流量套餐(M)","物理小区号","ICCID","信号质量RSRQ"};
        String fileName = "设备导出";
        try {
            ExcelUtils.exportExcel(resp,request,beanMaps,keys,heads,fileName);
        } catch (IOException e) {
            log.error("" + e);
            return Result.toResult(ApiCode.SERVER_ERROR);
        }
        return Result.success();
    }

    public List<AcsUserDevice> getByDeviceIds(List<Integer> deviceIds) {
        return acsUserDeviceMapper.getByDeviceIds(deviceIds);
    }

    public List<AcsUserDevice> getByUserId(Integer userId) {
        return acsUserDeviceMapper.getByUserId(userId);
    }

    public JSONArray getBaseDevice(Integer deviceId) {
        String baseDev = acsUserDeviceMapper.getBaseDev(deviceId);
        if (StringUtils.isNotBlank(baseDev)) {
            JSONArray array = JSON.parseArray(baseDev);
            for (int i = 0; i < array.size(); i++) {
                JSONObject dev = array.getJSONObject(i);
                dev.put("userDeviceId",deviceId);
            }
            return array;
        }
        return new JSONArray();
    }

    public Map<String, Object> activate(String sn, String username, String passwd) {
        List<AcsDevice> deviceList = acsDeviceMapper.getBySnList(Arrays.asList(sn));
        if (CollectionUtils.isEmpty(deviceList) ||
                !deviceList.get(0).getSn().equals(sn)) { //做大小写判断
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        AcsUser loginUser = new AcsUser();
        loginUser.setName(username);
        loginUser.setPasswd(passwd);
        AcsUser user = acsUserService.login(loginUser);

        String devicePasswd = PasswdUtils.getRandomChar(8);
        AcsUserDevice acsUserDevice = acsUserDeviceMapper.getBySn(sn);
        if (acsUserDevice != null && !Objects.equals(user.getId(), acsUserDevice.getBelongTo())) {
            throw new IotException(ApiCode.DEVICE_ALREADY_ACTIVATED);
        } else if (acsUserDevice == null) {
            AcsUserDevice newDevice = new AcsUserDevice();
            newDevice.setBelongTo(user.getId());
            newDevice.setSn(sn);
            newDevice.setModel(deviceList.get(0).getModel());
            newDevice.setDevicePasswd(Md5Utils.getMD5(devicePasswd));
            newDevice.setHostPrefix(UUID.randomUUID().toString().replaceAll("-", ""));
            this.insertSelective(newDevice);
        } else {
            AcsUserDevice updateDevice = new AcsUserDevice();
            updateDevice.setId(acsUserDevice.getId());
            updateDevice.setDevicePasswd(Md5Utils.getMD5(devicePasswd));
            updateDevice.setBelongTo(user.getId());
            this.updateByPrimaryKeySelective(updateDevice);
        }

        // 为设备创建单独的MQTT账户密码
        createMqttUserForGateway(sn, devicePasswd);

        // 发布订阅消息
        mqttMessageHandler.activate(sn, username);

        Map<String, Object> resultMap = certDownload();
        resultMap.put("data", devicePasswd);
        return resultMap;
    }

    public String activateDevice(String sn, String username, String passwd) {
        Map<String, Object> map = activate(sn, username, passwd);

        return map.get("data").toString();
    }

    public void createMqttUserForGateway(String sn, String password) {
        try {
            acsMqttService.createAcsMqttUser(sn, password);
        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    public void auth(String sn, String passwd) {
        AcsUserDevice userDevice = this.getBySn(sn);
        if (userDevice == null || !Objects.equals(userDevice.getDevicePasswd(), passwd)) {
            throw new IotException(ApiCode.DEVICE_AUTH_FAILED);
        }
    }

    public AcsUserDevice getBySn(String sn) {
        return acsUserDeviceMapper.getBySn(sn);
    }

    public String getBelongUserName(String sn) {
        AcsUserDevice device = acsUserDeviceMapper.getBySn(sn);
        if (device == null) {
            return null;
        }

        AcsUser user = acsUserMapper.selectByPrimaryKey(device.getBelongTo());
        if (user == null) {
            return null;
        }

        return user.getName();
    }

    public List<String> getVernoList() {
        List<String> list = acsUserDeviceMapper.getVernoList();
        return list;
    }

    public void log(Integer deviceId) {
        AcsUserDevice userDevice = acsUserDeviceMapper.selectByPrimaryKey(deviceId);
        this.validateDevice(userDevice);

        mqttMessageHandler.deviceLog(userDevice.getSn(), userDevice.getUpgradeDevice());
    }

    public void logUpload(String sn, MultipartFile file) {
        AcsUserDevice userDevice = acsUserDeviceMapper.getBySn(sn);
        if (userDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        String filepath = null;
        try {
            FileClient fileClient;
            if ("local".equals(deployType)) {
                fileClient = FileClientFactory.getFileClient("local");
                if (StringUtils.isNotBlank(userDevice.getPath())) {
                    fileClient.delete(userDevice.getPath());
                }
            } else {
                fileClient = FileClientFactory.getFileClient("net");
            }
            if (StringUtils.isNotBlank(userDevice.getPath()) && userDevice.getPath().contains(".tar.gz")) {
                String relativePath =
                        FilenameUtils.getFullPath(userDevice.getPath()) +
                                FilenameUtils.getBaseName(FilenameUtils.getBaseName(userDevice.getPath()));
                fileClient.delete(relativePath);
            }

            String ext = com.jetron.nb.common.util.FileUtils.getExtName(file.getOriginalFilename());
            filepath = fileClient.upload(file.getInputStream(), "log/", ext);
        } catch (IOException e) {
            log.error("[AcsUserDeviceService#logUpload] error, {}", e);
            throw new IotException(ApiCode.DEVICE_LOG_UPLOAD_FAILED);
        }

        AcsUserDevice updateUserDevice = new AcsUserDevice();
        updateUserDevice.setId(userDevice.getId());
        updateUserDevice.setPath(filepath);
        this.updateByPrimaryKeySelective(updateUserDevice);

        log.info("[AcsUserDeviceService#logUpload] success, sn: {}, file: {}, path:{}", sn, file.getOriginalFilename(), filepath);
    }

    public boolean logExist(Integer deviceId) {
        AcsUserDevice userDevice = acsUserDeviceMapper.selectByPrimaryKey(deviceId);
        if (userDevice != null && StringUtils.isNotBlank(userDevice.getPath())) {
            if ("local".equals(deployType)) {
                return FileClientFactory.getFileClient("local").exist(userDevice.getPath());
            } else {
                return FileClientFactory.getFileClient("net").exist(userDevice.getPath());
            }
        }

        return false;
    }

    public void logDownload(HttpServletResponse response, Integer deviceId) {
        AcsUserDevice userDevice = acsUserDeviceMapper.selectByPrimaryKey(deviceId);
        if (userDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        String path = userDevice.getPath();
        if (StringUtils.isBlank(path)) {
            throw new IotException(ApiCode.DEVICE_LOG_NOT_EXIST);
        }

        String filename = userDevice.getSn() + "_" + userDevice.getAlias() + "_" + FileUtils.getExtName(path);
        try {
            if ("local".equals(deployType)) {
                FileClientFactory.getFileClient("local").download(response, userDevice.getPath(), filename);
            } else {
                FileClientFactory.getFileClient("net").download(response, userDevice.getPath(), filename);
            }
        } catch (Exception e) {
            log.error("[AcsUserDeviceService#logUpload] error, {}", e);
            throw new IotException(ApiCode.DEVICE_LOG_DOWNLOAD_FAILED);
        }
    }

    private void validateDevice(AcsUserDevice userDevice) {
        if (userDevice == null) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (!MdcUtils.isAdmin() && userDevice.getBelongTo() != currentUser.getId()) {
            throw new IotException(ApiCode.DEVICE_NOT_EXIST);
        }
    }

    public int insertSelective(AcsUserDevice record) {
        record.setGmtCreate(new Date());
        return acsUserDeviceMapper.insertSelective(record);
    }

    public AcsUserDevice selectByPrimaryKey(Integer id) {
        return acsUserDeviceMapper.selectByPrimaryKey(id);
    }

    public int updateByPrimaryKeySelective(AcsUserDevice record) {
        record.setGmtModify(new Date());
        return acsUserDeviceMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(AcsUserDevice record) {
        record.setGmtModify(new Date());
        return acsUserDeviceMapper.updateByPrimaryKey(record);
    }

    public void deleteByUserId(Integer userId) {
        UserDeviceFilter filter = new UserDeviceFilter();
        filter.setUserId(userId);
        List<AcsUserDevice> filter1 = acsUserDeviceMapper.getByFilter(filter);
        if (!CollectionUtils.isEmpty(filter1)) {
            for (int i = 0; i < filter1.size(); i++) {
                String sn = filter1.get(i).getSn();
                String deviceTopic = acsTopicService.getSubscribeDeviceTopic(sn);
                mqttClientManager.unsubscribe(deviceTopic);
                String upgradeDeviceTopic = acsTopicService.getSubscribeUpgradeDeviceTopic(sn);
                mqttClientManager.unsubscribe(upgradeDeviceTopic);
            }
        }
        acsUserDeviceMapper.deleteByUserId(userId);
    }


    public void deleteByPrimaryKey(Integer id) {
        AcsUserDevice device = acsUserDeviceMapper.selectByPrimaryKey(id);
        if (device == null) {
            return;
        } else {
            String deviceTopic = acsTopicService.getSubscribeDeviceTopic(device.getSn());
            mqttClientManager.unsubscribe(deviceTopic);
            String upgradeDeviceTopic = acsTopicService.getSubscribeUpgradeDeviceTopic(device.getSn());
            mqttClientManager.unsubscribe(upgradeDeviceTopic);
        }
        acsUserDeviceMapper.deleteByPrimaryKey(id);

        // 重置MQTT密码，适配新的无控制台网关入网激活
        acsDeviceService.initMqttAccount(device.getSn());
    }

    public List<AcsUserDevice> getIncreasedDevice(Date begin, Date end) {
        return acsUserDeviceMapper.getIncreasedDevice(begin, end);
    }

    public List<AcsUserDevice> getDeletedDevice(Date begin, Date end) {
        List<AcsUserDevice> deletedList = acsUserDeviceMapper.getDeletedDevice(begin, end);
        List<String> deletedSn = deletedList.stream().map(x -> x.getSn()).collect(Collectors.toList());

        UserDeviceFilter filter = new UserDeviceFilter();
        filter.setSnList(deletedSn);
        List<String> reAddedSnList = acsUserDeviceMapper.getByFilter(filter).stream()
                .map(x -> x.getSn()).collect(Collectors.toList());

        return deletedList.stream().filter(x -> !reAddedSnList.contains(x.getSn())).collect(Collectors.toList());
    }

    private String getHostUrl(AcsUserDevice userDevice) {
        String hostPrefix = userDevice.getHostPrefix();
        if (StringUtils.isBlank(hostPrefix)) {
            hostPrefix = UUID.randomUUID().toString().replaceAll("-", "");
            AcsUserDevice updateDevice = new AcsUserDevice();
            updateDevice.setId(userDevice.getId());
            updateDevice.setHostPrefix(hostPrefix);
            this.updateByPrimaryKeySelective(updateDevice);
        }

        String[] subUrls = platformUrl.split("//");
        return subUrls[0] + "//" + hostPrefix + "." + subUrls[1];
    }

    public void releaseUpgradeStatus(List<Integer> deviceIds) {
        if (!CollectionUtils.isEmpty(deviceIds)) {
            acsUserDeviceMapper.releaseUpgradeStatus(deviceIds);
        }
    }

    public List<AcsUserDevice> selectAllNotDelete() {
        return acsUserDeviceMapper.selectAllNotDelete();
    }

    /**
     * 获取某一公司下用户（包含冻结，不包含删除），所有sn序列号
     *
     * @param company
     * @return
     */
    public List<String> findSnByCompany(String company) {
        if (StringUtils.isEmpty(company)) {
            return new ArrayList<>();
        }
        // 获取公司下的 sn
        UserFilter userFilter = new UserFilter();
        userFilter.setCompany(company);
        List<AcsUser> accountList = acsUserService.findList(userFilter);
        if (CollectionUtils.isEmpty(accountList)) {
            return new ArrayList<>();
        }
        List<Integer> userIds = accountList.stream().map(AcsUser::getId).collect(Collectors.toList());
        UserDeviceFilter userDeviceFilter = new UserDeviceFilter();
        userDeviceFilter.setBelongToList(userIds);
        List<AcsUserDevice> list = this.findList(userDeviceFilter);
        List<String> snList = list.stream().map(AcsUserDevice::getSn).collect(Collectors.toList());
        return snList;
    }

    public Map<String, Object> certDownload() {
        Map<String, Object> resultMap = new HashMap<>();
        if ("local".equals(deployType)) {
            String urlPrefix = BootstrapConstsLocal.getServiceUrl() + "/file/downloadCert/" + certDir;
            String uuid = UUID.randomUUID().toString();
            String timestamp = String.valueOf(new Date().getTime());
            String urlSurfix = "?timestamp=" + timestamp + "&id=" + uuid;
            redisService.set(timestamp, uuid);
            redisService.expire(timestamp, 5 * 60);
            resultMap.put("cafile", urlPrefix + "/" + caFile + urlSurfix);
            resultMap.put("cert", urlPrefix + "/" + certFile + urlSurfix);
            resultMap.put("key", urlPrefix + "/" + keyFile + urlSurfix);
        } else {
            resultMap.put("cafile", FileClientFactory.getFileClient("net").getUrl(certDir + "/" + caFile));
            resultMap.put("cert", FileClientFactory.getFileClient("net").getUrl(certDir + "/" + certFile));
            resultMap.put("key", FileClientFactory.getFileClient("net").getUrl(certDir + "/" + keyFile));
        }
        resultMap.put("message", "成功");
        resultMap.put("code", 200);
        return resultMap;
    }

    public int countByFilter(UserDeviceFilter filter) {
        return acsUserDeviceMapper.countByFilter(filter);
    }

    @Transactional
    public int setUpgradeStatus(List<Integer> deviceIds, Integer status) {
        return acsUserDeviceMapper.setUpgradeStatus(deviceIds, status);
    }

    public Result deviceModel() {
        UserDeviceFilter filter = new UserDeviceFilter();
        UserDeviceFilter param = new UserDeviceFilter();
        /*if (!MdcUtils.isAdmin()) {
            AcsUser currentAcsUser = MdcUtils.getCurrentAcsUser();
            param.setBelongTo(currentAcsUser.getId());
        }*/
        List<JSONObject> all = acsUserDeviceMapper.selectModelGroup(param);
        int allNum = countByFilter(filter);
        filter.setOnline(1);
        int onlineNum = countByFilter(filter);
        Map<String, Object> result = new HashMap<>();
        result.put("list", all);
        result.put("allNum", allNum);
        result.put("onlineNum", onlineNum);
        result.put("offlineNum", allNum - onlineNum);
        return Result.success(result);
    }
    public Result onlineAndOffline() {
        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        deviceFilter.setOnline(0);
        List<AcsUserDevice> offline = this.findList(deviceFilter);
        deviceFilter.setOnline(1);
        List<AcsUserDevice> online = this.findList(deviceFilter);
        Map<String,Object> result = new HashMap();
        result.put("offline",offline);
        result.put("online",online);
        result.put("onlineNum",online.size());
        result.put("offlineNum",offline.size());
        result.put("allNum",offline.size() + online.size());
        return Result.success(result);
    }

    public Result sendMqtt(String sn, JSONObject param) {
        log.info("netWorkSetUp；sn:{},param:{}", sn, param.toJSONString());
        String deviceTopic = topicService.getPublishDeviceTopic(sn);
        // 获取网关信息
        AcsUserDevice userDevice = this.getBySn(sn);
        if (null != userDevice && null != userDevice.getUpgradeDevice() && userDevice.getUpgradeDevice()) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), param.toJSONString());
        } else {
            mqttClientManager.publish(deviceTopic, param.toJSONString());
        }
        return Result.success();
    }

    public Result bindIP(String sn,String bindIP,String enable){
        JSONObject param = new JSONObject();
        UserDeviceFilter bySn = new UserDeviceFilter();
        bySn.setSnEq(sn);
        List<AcsUserDevice> list = this.findList(bySn);
        AcsUserDevice userDevice = list.get(0);
        if ("0".equals(enable)) {
            userDevice.setBindIp(null);
            this.updateByPrimaryKey(userDevice);
            param.put("enable", enable);
            param.put("bindIP", "");
            param.put("cmd", MessageCmd.BIND_IP.cmd);
            return this.sendMqtt(sn, param);
        }
        UserDeviceFilter filter = new UserDeviceFilter();
        filter.setTrueIp(bindIP);
        filter.setNoEqSn(sn);
        Map<String, Object> map = this.search(filter,null,null,null);
        if (0 < (int)map.get("total")) {
            return Result.toResult(ApiCode.TRUE_IP_EXIST);
        }
        AcsUserDevice update = new AcsUserDevice();
        update.setBindIp(bindIP);
        update.setId(userDevice.getId());
        this.updateByPrimaryKeySelective(update);
        param.put("bindIP", bindIP);
        param.put("enable", enable);
        param.put("cmd", MessageCmd.BIND_IP.cmd);
        return this.sendMqtt(sn, param);
    }

    public Result networkMonitor(String sn,String monitor,String ip,String ipBackup,String probeInterval,int messageNum){
        JSONObject param = new JSONObject();
        param.put("enable", monitor);
        param.put("ip", ip);
        if (StringUtils.isNotBlank(ipBackup)) {
            param.put("ipBackup", ipBackup);
        }
        param.put("interval", probeInterval);
        param.put("counts", messageNum);
        param.put("cmd", MessageCmd.NETWORK_MONITOR.cmd);
        return this.sendMqtt(sn, param);
    }

    public Result setLANIP(String sn,String ip,String netmask){
        JSONObject param = new JSONObject();
        param.put("netmask", netmask);
        param.put("ip", ip);
        param.put("cmd", MessageCmd.LAN_IP_S.cmd);
        redisService.set(LANIPProcessor.getRedisKey(sn),param.toJSONString());
        return this.sendMqtt(sn, param);
    }

    public Result initNetworkSetUp(String sn){
        String s = redisService.get(NTPProcessor.getRedisKey(sn));
        String s1 = redisService.get(BindIPProcessor.getRedisKey(sn));
        String s2 = redisService.get(NetworkMonitorProcessor.getRedisKey(sn));
        String s3 = redisService.get(LANIPProcessor.getRedisKey(sn));
        Map<String,Object> map = new HashMap<>();
        map.put("ntpServer",JSONObject.parseObject(s));
        map.put("bindIP",JSONObject.parseObject(s1));
        map.put("networkMonitor",JSONObject.parseObject(s2));
        map.put("lanIP",JSONObject.parseObject(s3));
        return Result.success(map);
    }

    public Result networkDiagnosis(List<String> snList,Integer page, Integer size){
        List<ContainerError> errorList = containerErrorService.findList();
        Map<String, String> errorMap = errorList.stream().collect(Collectors.toMap(ContainerError::getCode, ContainerError::getAdvise));
        JSONObject data = new JSONObject();
        Map<String,Object> mqttData = new HashMap<>();
        Map<String,Object> vpnData = new HashMap<>();
        // 1.检查mqtt
        boolean mqttContainerBool = CommandUtils.dockerContainerRunning(Constants.MQTT_DOCKER_NAME);
        mqttData.put("mqttContainer",mqttContainerBool);
        mqttData.put("mqttPlugin",false);
        if (mqttContainerBool) {
            Result result = MqttUtils.mysqlPlugsRunning();
            if (ResultUtils.isSuccess(result)) {
                mqttData.put("mqttPlugin",true);
            } else {
                mqttData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_2));
                mqttData.put("mysqlErrorCode",Constants.ContainerErrorCode.ERR_CODE_2);
            }
        } else {
            boolean exist = CommandUtils.dockerContainerExist(Constants.MQTT_DOCKER_NAME);
            if (exist) {
                mqttData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_1));
                mqttData.put("mqttErrorCode",Constants.ContainerErrorCode.ERR_CODE_1);
            } else {
                mqttData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_7));
                mqttData.put("mqttErrorCode",Constants.ContainerErrorCode.ERR_CODE_7);
            }
        }
        // 2.检查vpn
        boolean vpnContainerBool = CommandUtils.dockerContainerRunning(Constants.VPN_DOCKER_NAME);
        vpnData.put("vpnContainer",vpnContainerBool);
        vpnData.put("vpnThread",false);
        if (vpnContainerBool) {
            String[] cmdArray = new String[]{"/bin/sh", "-c", "docker exec -i docker_openvpn sh -c 'ps aux'"};
            Result result = CommandUtils.execResult(cmdArray);
            if (ResultUtils.isSuccess(result)) {
                String resultStr = (String) result.getData();
                String[] split = resultStr.split("\n");
                for (int i = 0; i < split.length; i++) {
                    String line = split[i];
                    if (line.contains("/etc/openvpn/openvpn")) {
                        vpnData.put("vpnThread",true);
                    }
                }
            }
            if ((boolean)vpnData.get("vpnThread") == false) {
                vpnData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_4));
                vpnData.put("threadErrorCode",Constants.ContainerErrorCode.ERR_CODE_4);
            }
        } else {
            boolean exist = CommandUtils.dockerContainerExist(Constants.VPN_DOCKER_NAME);
            if (exist) {
                vpnData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_3));
                vpnData.put("vpnErrorCode",Constants.ContainerErrorCode.ERR_CODE_3);
            } else {
                vpnData.put("advise",errorMap.get(Constants.ContainerErrorCode.ERR_CODE_6));
                vpnData.put("vpnErrorCode",Constants.ContainerErrorCode.ERR_CODE_6);
            }
        }
        // 3.检查网关
        if (!CollectionUtils.isEmpty(snList)) {
            UserDeviceFilter filter = new UserDeviceFilter();
            filter.setSnList(snList);
            Map<String, Object> search = search(filter, null, page, size);
            List<UserDeviceInfo> records = (List<UserDeviceInfo>)search.get("records");
            Result result = MqttUtils.deviceSubscribe();
            List<MqttUtils.DeviceSubscribe> list = new ArrayList<>();
            List<MqttUtils.DeviceSubscribe> dataList = new ArrayList<>();
            if (ResultUtils.isSuccess(result)) {
                list = (List<MqttUtils.DeviceSubscribe>)result.getData();
            }
            Map<String, MqttUtils.DeviceSubscribe> collect = list.stream().collect(Collectors.toMap(MqttUtils.DeviceSubscribe::getSn, obj -> obj));
            for (int i = 0; i < records.size(); i++) {
                UserDeviceInfo deviceInfo = records.get(i);
                String sn = deviceInfo.getSn();
                MqttUtils.DeviceSubscribe deviceSubscribe = collect.get(sn);
                if (deviceSubscribe == null) {
                    deviceSubscribe = new MqttUtils.DeviceSubscribe();
                    deviceSubscribe.setSn(sn);
                    deviceSubscribe.setAdvise(errorMap.get(Constants.ContainerErrorCode.ERR_CODE_5));
                } else if (deviceSubscribe.getSubBool() == null || deviceSubscribe.getSubBool() == false
                    || deviceSubscribe.getPubBool() == null || deviceSubscribe.getPubBool() == false) {
                    deviceSubscribe.setAdvise(errorMap.get(Constants.ContainerErrorCode.ERR_CODE_5));
                }
                dataList.add(deviceSubscribe);
            }
            data.put("total",search.get("total"));
            data.put("page",search.get("page"));
            data.put("size",search.get("size"));
            data.put("records",dataList);
        }
        data.put("mqtt",mqttData);
        data.put("vpn",vpnData);
        return Result.success(data);
    }

    public Result oneClickRepair(String mqttErrorCode,String mysqlErrorCode,String vpnErrorCode,String threadErrorCode) {
        List<ContainerError> errorList = containerErrorService.findList();
        Map<String, ContainerError> errorMap = errorList.stream().collect(Collectors.toMap(ContainerError::getCode,obj -> obj));
        StringBuffer str = new StringBuffer();
        // 检查mqtt
        if (Constants.ContainerErrorCode.ERR_CODE_1.equals(mqttErrorCode)){
            String[] cmd = new String[]{"/bin/sh", "-c", errorMap.get(mqttErrorCode).getCommand()};
            Result result = CommandUtils.execThree(cmd);
            if (ResultUtils.isSuccess(result)) {
                str.append("重启mqtt成功！;");
                Result mysqlPlugin = MqttUtils.restartMysqlPlugin();
                if (ResultUtils.isSuccess(mysqlPlugin)) {
                    str.append("重启mqtt-mysql插件成功！;");
                } else {
                    str.append("重启mqtt-mysql插件失败！;");
                }
            } else {
                str.append("重启mqtt失败！;");
            }
        } else if (Constants.ContainerErrorCode.ERR_CODE_2.equals(mysqlErrorCode)) {
            Result mysqlPlugin = MqttUtils.restartMysqlPlugin();
            if (ResultUtils.isSuccess(mysqlPlugin)) {
                str.append("重启mqtt-mysql插件成功！;");
            } else {
                str.append("重启mqtt-mysql插件失败！;");
            }
        }
        // 检查vpn
        if (StringUtils.isNotBlank(vpnErrorCode)){
            String[] cmd = new String[]{"/bin/sh", "-c", errorMap.get(vpnErrorCode).getCommand()};
            Result containerResult = CommandUtils.execThree(cmd);
            if (ResultUtils.isSuccess(containerResult)) {
                str.append("重启vpn容器成功！");
                String[] cmd2 = new String[]{"/bin/sh", "-c", errorMap.get(Constants.ContainerErrorCode.ERR_CODE_4).getCommand()};
                Result vpnResult = CommandUtils.execThree(cmd2);
                if (ResultUtils.isSuccess(vpnResult)) {
                    str.append("重启vpn进程成功！;");
                } else {
                    str.append("重启vpn进程失败！;");
                }
            } else {
                str.append("重启vpn容器失败！;");
            }
        } else if (Constants.ContainerErrorCode.ERR_CODE_4.equals(threadErrorCode)) {
            String[] cmd2 = new String[]{"/bin/sh", "-c",  errorMap.get(Constants.ContainerErrorCode.ERR_CODE_4).getCommand()};
            Result vpnResult = CommandUtils.execThree(cmd2);
            if (ResultUtils.isSuccess(vpnResult)) {
                str.append("重启vpn进程成功！;");
            } else {
                str.append("重启vpn进程失败！;");
            }
        }
        return Result.success(str);
    }

    /**
     * 外部接口调用，
     * @param snList
     * @return
     */
    public Result findDeviceStatus(List<String> snList) {
        if (CollectionUtils.isEmpty(snList)) {
            return ResultUtils.success();
        }
        AcsUser acsUser = MdcUtils.getCurrentAcsUser();
        String company = acsUser.getCompany();
        UserDeviceFilter filter = new UserDeviceFilter();
        filter.setCompany(company);
        List<AcsUserDevice> list = findListWithUser(filter);
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtils.newResult.fail("当前用户的设备数为0");
        }
        Map<String, AcsUserDevice> map = list.stream().collect(Collectors.toMap(AcsUserDevice::getSn, obj -> obj));
        List<String> snList22 = list.stream().map(AcsUserDevice::getSn).collect(Collectors.toList());
        List<AcsUserDevice> data = new ArrayList<>();
        for (int i = 0; i < snList.size(); i++) {
            String sn = snList.get(i);
            if (!snList22.contains(sn)) {
                return ResultUtils.newResult.fail("当前用户无设备权限；sn:" + sn);
            }
            data.add(map.get(sn));
        }
        List<UserDeviceInfo> userDeviceInfos = toDeviceInfo(data);
        List<UserDeviceInfo> data2 = new ArrayList<>();
        for (int i = 0; i < userDeviceInfos.size(); i++) {
            UserDeviceInfo userDeviceInfo = userDeviceInfos.get(i);
            UserDeviceInfo temp  = new UserDeviceInfo();
            temp.setStatus(userDeviceInfo.getStatus());
            temp.setDeviceStatus(userDeviceInfo.getDeviceStatus());
            temp.setSn(userDeviceInfo.getSn());
            data2.add(temp);
        }
        return ResultUtils.success(data2);
    }

    public LambdaQueryWrapper<AcsPortMapping> getPortMappingWrapper(AcsPortMapping param) {
        LambdaQueryWrapper<AcsPortMapping> wrapper = new LambdaQueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.eq(AcsPortMapping::getName,param.getName());
        }
        if (!CollectionUtils.isEmpty(param.getNameList())) {
            wrapper.in(AcsPortMapping::getName,param.getNameList());
        }
        if (!CollectionUtils.isEmpty(param.getIdList())) {
            wrapper.in(AcsPortMapping::getId,param.getIdList());
        }
        return wrapper;
    }

//    public LambdaQueryWrapper<AcsNatInfo> getNatWrapper(AcsNatInfo param) {
//        LambdaQueryWrapper<AcsNatInfo> wrapper = new LambdaQueryWrapper<>();
//        if (param == null) {
//            return wrapper;
//        }
//        if (StringUtils.isNotBlank(param.getSn())) {
//            wrapper.eq(AcsNatInfo::getSn,param.getSn());
//        }
//
//        return wrapper;
//    }

    public List<AcsPortMapping> findPortMappingList(AcsPortMapping param) {
        List<AcsPortMapping> records = portMappingMapper.selectList( getPortMappingWrapper(param));
        return records;
    }


    public Map<String, Object> portMappingPage(AcsPortMapping param,Integer page,Integer size) {
        Page<AcsPortMapping> page2 = new Page(page,size);
        Page<AcsPortMapping> portMappingPage = portMappingMapper.selectPage(page2, getPortMappingWrapper(param));
        List<AcsPortMapping> records = portMappingPage.getRecords();
        long total = portMappingPage.getTotal();
        return PageUtils.toPageResult((int)total,page,size,records);
    }

//    public Map<String, Object> natPage(AcsNatInfo param, Integer page, Integer size) {
//        Page<AcsNatInfo> page2 = new Page(page,size);
//        Page<AcsNatInfo> portMappingPage = natMapper.selectPage(page2, getNatWrapper(param));
//        List<AcsNatInfo> records = portMappingPage.getRecords();
//        long total = portMappingPage.getTotal();
//        return PageUtils.toPageResult((int)total,page,size,records);
//    }

    /**
     * 向网关发送 端口映射配置
     * @param param
     */
    public Result savePortMapping(AcsPortMapping param) {
        List<AcsPortMapping> list = portMappingMapper.selectList(getPortMappingWrapper(param));
        if (!CollectionUtils.isEmpty(list)) {
            return ResultUtils.newResult.fail("名称不可重复");
        }
        param.setId(IdWorker.getIdStr());
        param.setGmtCreate(new Date());
        portMappingMapper.insert(param);
        return Result.success();
    }

    public void delPortMapping(String id) {
        portMappingMapper.deleteById(id);
    }

    /**
     * 推送端口映射数据至网关（新增、删除）
     * @param json 数据
     * @param type 推送方式  add、del
     * @return
     */
    public Result sendPortMapping(JSONObject json, String type) {
        List<String> snList = new ArrayList(Arrays.asList(json.getString("snList").split(",")));
        List<String> nameList = new ArrayList(Arrays.asList(json.getString("nameList").split(",")));

        AcsPortMapping param = new AcsPortMapping();
        param.setNameList(nameList);
        List<AcsPortMapping> list = portMappingMapper.selectList(getPortMappingWrapper(param));
        for (int i = 0; i < snList.size(); i++) {
            String sn = snList.get(i);
            List<AcsUserDevice> listWithUser = this.findListWithUser(new UserDeviceFilter().setSn(sn));
            String name = listWithUser.get(0).getUser().getName();
            // 获取第一条映射（现在只支持一条）， 封装端口映射推送消息体
            AcsPortMapping acsPortMapping = list.get(0);
            String[] split = acsPortMapping.getProtocol().split(",");
            JSONObject message = new JSONObject();
            message.put("displayName", acsPortMapping.getName());
            if ("add".equals(type)) {
                message.put("enable", acsPortMapping.getStatus());
                message.put("protocol", split);
                message.put("srcPort", acsPortMapping.getGatewayPort());
                message.put("destIP", acsPortMapping.getDevIp());
                message.put("destPort", acsPortMapping.getDevPort());
            }

            message.put("cmd",MessageCmd.PORT_MAPPING_DOWN.cmd);
            message.put("type",type);

            String topic = String.format(Topic.DEVICE_TOPIC.name,name,sn);
            // 推送mqtt消息
            mqttClientManager.publish(topic,message.toJSONString());

            String upgradeTopic = String.format(Topic.DEVICE_TOPIC_UPGRADE.name, name, sn, "down");
            mqttClientManager.publish(upgradeTopic, message.toJSONString());
        }
        return Result.success();
    }

    @Transactional
    public void insertPortMappingDeviceList(List<AcsPortMappingDevice> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }
        LambdaUpdateWrapper<AcsPortMappingDevice> wrapper = new LambdaUpdateWrapper<>();
        String sn = addList.get(0).getSn();
        List<String> nameList = addList.stream().map(AcsPortMappingDevice::getName).collect(Collectors.toList());
        wrapper.eq(AcsPortMappingDevice::getSn,sn)
                .in(AcsPortMappingDevice::getName,nameList);
        // 如果有重复的，先删除，在新增
        portMappingDeviceMapper.delete(wrapper);
        portMappingDeviceMapper.insertList(addList);
    }

    @Transactional
    public void delPortMappingDeviceList(String sn,List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return;
        }
        LambdaUpdateWrapper<AcsPortMappingDevice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AcsPortMappingDevice::getSn,sn)
                .in(AcsPortMappingDevice::getName,nameList);
        // 删除
        portMappingDeviceMapper.delete(wrapper);
    }

    public LambdaQueryWrapper<AcsPortMappingDevice> getPortMappingDeviceWrapper(AcsPortMappingDevice param) {
        LambdaQueryWrapper<AcsPortMappingDevice> wrapper = new LambdaQueryWrapper<>();
        if (param == null) {
            return wrapper;
        }
        if (StringUtils.isNotBlank(param.getSn())) {
            wrapper.eq(AcsPortMappingDevice::getSn,param.getSn());
        }
        return wrapper;
    }


    public Map<String, Object> portMappingDevicePage(AcsPortMappingDevice param,Integer page,Integer size) {
        Page<AcsPortMappingDevice> page2 = new Page(page,size);
        Page<AcsPortMappingDevice> portMappingPage = portMappingDeviceMapper.selectPage(page2, getPortMappingDeviceWrapper(param));
        List<AcsPortMappingDevice> records = portMappingPage.getRecords();
        long total = portMappingPage.getTotal();
        return PageUtils.toPageResult((int)total,page,size,records);
    }

    public Result testrecord(String sn,String token,String json) {
        String token2 = null;
        try {
            token2 = EncryptUtils.getToken(sn);
        } catch (Exception e) {
            log.error("生成token异常:{}",e);
            return ResultUtils.fail();
        }
        if (!StringUtils.equals(token,token2)) {
            return Result.toResult(ApiCode.DEVICE_AUTH_FAILED);
        }
        JSONObject json2 = JSONObject.parseObject(json);
        AcsDeviceTestReport report = new AcsDeviceTestReport();
        report.setId(IdWorker.getIdStr());
        report.setSn(sn)
                .setJson(json2)
                .setGmtCreate(new Date());
        testReportMapper.insert(report);
        return Result.success();
    }

    /**
     * 推送查询接口，向网关发送获取网关端口映射的指令
     * @param sn 网关sn
     * @return Result
     */
    public Result sendPortMappingGet(String sn) {
        List<AcsUserDevice> listWithUser = this.findListWithUser(new UserDeviceFilter().setSn(sn));
        String name = listWithUser.get(0).getUser().getName();
        // 获取第一条映射（现在只支持一条）， 封装端口映射推送消息体
        JSONObject message = new JSONObject();
        message.put("cmd",MessageCmd.PORT_MAPPING_DOWN.cmd);
        message.put("type", Constants.NAT_TYPE_GET);
        // 推送消息
        String topic = String.format(Topic.DEVICE_TOPIC.name, name, sn);
        mqttClientManager.publish(topic,message.toJSONString());
        return Result.success();
    }

    /**
     * 同步网关端口映射至数据库
     * @param sn sn号
     * @param portMappingDevices 映射集合
     */
    public void updatePortMappingDeviceList(String sn, List<AcsPortMappingDevice> portMappingDevices) {
        AcsPortMappingDevice acsPortMappingDevice = new AcsPortMappingDevice().setSn(sn);
        // 获取数据库中已经绑定的映射
        List<AcsPortMappingDevice> portMappingDevicesFromDb = portMappingDeviceMapper.selectList(getPortMappingDeviceWrapper(acsPortMappingDevice));
        // 提取数据库和网关数据两者的端口映射名称（用于比较）
        List<String> portMappingFromDb = portMappingDevicesFromDb.stream()
                .map(AcsPortMappingDevice::getName).collect(Collectors.toList());
        List<String> portMappingFromGateway = portMappingDevices.stream()
                .map(AcsPortMappingDevice::getName).collect(Collectors.toList());
        // 获取需要删除的
        List<String> removeNames = portMappingFromDb.stream()
                .filter(port -> !portMappingFromGateway.contains(port))
                .collect(Collectors.toList());
        // 同步操作
        if (!CollectionUtils.isEmpty(removeNames)) {
            // 删除端口映射数据
            delPortMappingDeviceList(sn, removeNames);
        }
        if (!CollectionUtils.isEmpty(portMappingDevices)) {
            // 新增或修改端口映射数据
            insertPortMappingDeviceList(portMappingDevices);
        }
        log.info("updatePortMappingDeviceList  date,删除：{}，新增修改：{}", removeNames.size(), portMappingDevices.size());
    }
}
