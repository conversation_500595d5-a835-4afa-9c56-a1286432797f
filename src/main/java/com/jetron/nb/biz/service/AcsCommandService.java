package com.jetron.nb.biz.service;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.processor.ControlProcessor;
import com.jetron.nb.common.constant.MessageCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *  用于处理向网关等发送控制指令
 */
@Service
@Slf4j
public class AcsCommandService {
    @Autowired
    private ControlProcessor controlProcessor;

    public void restartDevice(String sn) {
        IotMessage iotMessage = new IotMessage();
        iotMessage.setCmd(MessageCmd.RESTART_DEVICE.cmd);
        iotMessage.setSn(sn);

        publish(sn, JSON.toJSONString(iotMessage));
    }

    public void resetDevice(String sn) {
        IotMessage iotMessage = new IotMessage();
        iotMessage.setCmd(MessageCmd.RESET_DEVICE.cmd);
        iotMessage.setSn(sn);

        publish(sn, JSON.toJSONString(iotMessage));
    }

    private void publish(String sn, String content) {
        controlProcessor.publish(sn, content);
    }
}
