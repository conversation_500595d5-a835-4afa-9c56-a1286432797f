package com.jetron.nb.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jetron.nb.dal.dao.OpenInterfaceMapper;
import com.jetron.nb.dal.po.OpenInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.Wrapper;
import java.util.List;

@Service
@Slf4j
public class OpenInterfaceService {

    @Autowired
    OpenInterfaceMapper mapper;


    public List<OpenInterface> findList(OpenInterface param) {
        QueryWrapper<OpenInterface> wrapper = new QueryWrapper<>();
        return mapper.selectList(wrapper);
    }



}
