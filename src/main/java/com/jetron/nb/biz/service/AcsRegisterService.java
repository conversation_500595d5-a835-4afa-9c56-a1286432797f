package com.jetron.nb.biz.service;

import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;


@Service
@Slf4j
public class AcsRegisterService {

    @Value("${app.licensePath}")
    private String licensePath;

    /**
     *  导入license文件
     * @param file license文件
     */
    public boolean importLicense(MultipartFile file) {
        String filename = Objects.requireNonNull(file.getOriginalFilename()).toLowerCase();
        if (!filename.endsWith(".nms")) {
            throw new IotException(ApiCode.DEVICE_FILE_TYPE_NOT_SUPPORT);
        }

        String path = licensePath + "/license.nms";
        try {
            file.transferTo(new File(path));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("注册文件导入失败！ \n" + e.toString());
        }
        return false;
    }

}
