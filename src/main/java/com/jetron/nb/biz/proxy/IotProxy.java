package com.jetron.nb.biz.proxy;

import com.google.common.base.Joiner;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.dal.dao.AcsUserDeviceMapper;
import com.jetron.nb.dal.po.AcsUserDevice;
import com.jetron.nb.web.common.util.ThreadContextUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.Map;

/**
 *
 * Date: 2020-02-01
 * Version: 1.0.0
 */
@Slf4j
@Component
public class IotProxy {

    private OkHttpClient client;

    @Autowired
    private OkHttpClientFactory okHttpClientFactory;
    @Autowired
    private AcsUserDeviceMapper acsUserDeviceMapper;

    @PostConstruct
    public void init() {
        client = okHttpClientFactory.getOkHttpClient();
    }

    public void visit(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        try {
            Request.Builder builder = new Request.Builder();
            builder = fillHeader(servletRequest, builder);
            builder = fillUrl(servletRequest, builder);
            builder = fillMethodAndBody(servletRequest, builder);

            Request request = builder.build();
            Response response = client.newCall(request).execute();
            fillResponse(servletResponse, response);
        } catch (Exception e) {
            log.error("[IotProxy#visit] error, {}", e);
            throw new IotException(ApiCode.SERVER_ERROR);
        }

    }

    private Request.Builder fillHeader(HttpServletRequest request, Request.Builder builder) {
        Enumeration<String> names = request.getHeaderNames();
        if (names != null) {
            while (names.hasMoreElements()) {
                String headerName = names.nextElement();
                if ("host".equalsIgnoreCase(headerName)) {
                    names.nextElement();
                    continue;
                }

                if (request.getHeaders(headerName) != null) {
                    Enumeration<String> headerValues = request.getHeaders(headerName);
                    while (headerValues.hasMoreElements()) {
                        builder.addHeader(headerName, headerValues.nextElement());
                    }
                }
            }
        }

        return builder;
    }

    private Request.Builder fillUrl(HttpServletRequest request, Request.Builder builder) {
        String host=request.getHeader("Host");

        String prefix = host.split("\\.")[0];
        AcsUserDevice acsUserDevice = acsUserDeviceMapper.getByHostPrefix(prefix);
        if (acsUserDevice == null || StringUtils.isBlank(acsUserDevice.getHostPrefix())) {
            log.error("[IotProxy#fillUrl] error, can't get hostPrefix, ip: {}", ThreadContextUtils.getRemoteIp());
            throw new IotException(ApiCode.SERVER_ERROR);
        }

        StringBuilder sb = new StringBuilder()
                .append("http://")
                .append(acsUserDevice.getDumIp())
                .append(request.getRequestURI());

        Map<String, String[]> parameters = request.getParameterMap();
        if (!CollectionUtils.isEmpty(parameters)) {
            sb.append("?");
            boolean first = true;
            for (String name : parameters.keySet()) {
                if (!first) {
                    sb.append("&");
                } else {
                    first = false;
                }

                sb.append(name).append("=").append(Joiner.on(",").join(parameters.get(name)));
            }
        }

        return builder.url(sb.toString());
    }

    private Request.Builder fillMethodAndBody(HttpServletRequest request, Request.Builder builder) throws IOException {
        String method = request.getMethod();
        if ("POST".equalsIgnoreCase(method)) {
            String contentType = request.getContentType();
            if (StringUtils.isNoneBlank(contentType)) {
                contentType = "*";
            }

            MediaType mediaType = MediaType.parse(contentType);
            RequestBody requestBody = RequestBody.create(mediaType, IOUtils.toString(request.getInputStream(), Charset.forName("utf-8")));
            builder = builder.post(requestBody);
        } else {
            builder = builder.get();
        }

        return builder;
    }

    private void fillResponse(HttpServletResponse servletResponse, Response response) throws IOException {
        servletResponse.setStatus(response.code());

        //header
        Headers headers = response.headers();
        if (!CollectionUtils.isEmpty(headers.names())) {
            for (String name : headers.names()) {
                if (!CollectionUtils.isEmpty(headers.values(name))) {
                    for (String value : headers.values(name)) {
                        servletResponse.addHeader(name, value);
                    }
                }
            }
        }

        //body
        ResponseBody responseBody = response.body();
        servletResponse.getOutputStream().write(responseBody.bytes());
        servletResponse.getOutputStream().flush();
    }

}
