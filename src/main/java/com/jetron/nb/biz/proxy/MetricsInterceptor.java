package com.jetron.nb.biz.proxy;


import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 *
 * Date: 2019-12-20
 * Version: 1.0.0
 */
@Slf4j
@Component
public class MetricsInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = chain.proceed(request);

        String url = request.url().toString();
        String code = String.valueOf(response.code());

        log.info("Visit {}, response code: {}", url, code);

        return response;
    }
}
