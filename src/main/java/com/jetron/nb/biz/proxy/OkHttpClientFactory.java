package com.jetron.nb.biz.proxy;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionSpec;
import okhttp3.CookieJar;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.*;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 * Date: 2020-01-06
 * Version: 1.0.0
 */
@Component
@Slf4j
public class OkHttpClientFactory {
    private static final int CONNECT_TIMEOUT = 5 * 60;
    private static final int READ_TIMEOUT = 5 * 60;

    @Autowired
    private MetricsInterceptor metricsInterceptor;

    public OkHttpClient getOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .cookieJar(CookieJar.NO_COOKIES)
                .connectionSpecs(Arrays.asList(ConnectionSpec.MODERN_TLS, ConnectionSpec.CLEARTEXT, ConnectionSpec.COMPATIBLE_TLS, ConnectionSpec.RESTRICTED_TLS))
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(metricsInterceptor);

        return builder.build();
    }
}
