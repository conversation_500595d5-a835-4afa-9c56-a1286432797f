package com.jetron.nb.biz.oss;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class BootstrapConsts {
    @Value("${file.oss.endPoint}")
    private String endPoint;

    @Value("${file.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${file.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${file.oss.bucketName}")
    private String bucketName;;

    @Value("${file.oss.rootDir}")
    private String rootDir;


    /**
     * OSS地址（不同服务器，地址不同）
     */
    public static String end_point;
    /**
     * OSS键id（去OSS控制台获取）
     */
    public static String access_key_id;
    /**
     * OSS秘钥（去OSS控制台获取）
     */
    public static String access_key_secret;
    /**
     * OSS桶名称（这个是自己创建bucket时候的命名）
     */
    public static String bucket_name;
    /**
     * OSS根目录
     */
    public static String root_dir;

    @PostConstruct
    private void initial() {
        end_point = endPoint;
        access_key_id = accessKeyId;
        access_key_secret = accessKeySecret;
        bucket_name = bucketName;
        root_dir = rootDir;
    }
}
