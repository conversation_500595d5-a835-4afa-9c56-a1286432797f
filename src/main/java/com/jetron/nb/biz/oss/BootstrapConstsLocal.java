package com.jetron.nb.biz.oss;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 *  本地部署版 使用
 */
@Slf4j
@Component
public class BootstrapConstsLocal {
    private static String loalIp;
    private static  String appPort;

    @Value("${server.port}")
    private String port;

    @PostConstruct
    private void initial() {
        try {
            // 获取系统环境变量 SERVER_IP ，该变量在镜像制作时设置
            loalIp = StringUtils.isEmpty(System.getenv("SERVER_IP")) ? "localhost" : System.getenv("SERVER_IP");
            appPort = port;
            log.info("BootstrapConsts, localIp: {}, appPort: {}", loalIp, appPort);
        } catch (Exception e) {
            log.error("BootstrapConsts initial failed", e);
        }
    }

    public static String getServiceUrl() {
        return String.format("http://%s:%s", loalIp, appPort);
    }
}
