package com.jetron.nb.biz.oss;

import com.jetron.nb.common.util.Md5Utils;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;

import com.obs.services.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  华为云obs
 */
@Slf4j
public class ObsFileClient implements FileClient {
    private static ObsFileClient obsFileClient;
    public static final Integer INPUT_BUFFER_SIZE = 8192;

    private ObsClient getClient() {
        ObsConfiguration config = new ObsConfiguration();
        config.setSocketTimeout(30000);
        config.setConnectionTimeout(10000);
        config.setEndPoint(BootstrapConsts.end_point);
        return new ObsClient(BootstrapConsts.access_key_id, BootstrapConsts.access_key_secret, config);
    }

    public static FileClient create() {
        if (null == obsFileClient) {
            synchronized (ObsFileClient.class) {
                if (null == obsFileClient) {
                    obsFileClient = new ObsFileClient();
                }
            }
        }
        return obsFileClient;
    }

    @Override
    public InputStream getFileStream(String filename) {
        ObsClient obsClient = getClient();
        ObsObject obsObject = obsClient.getObject(BootstrapConsts.bucket_name, filename, null);
        return obsObject.getObjectContent();
    }

    @Override
    public String upload(InputStream stream, String rootPath, String ext) {
        ObsClient obsClient = getClient();

        String fileName = createFileName(ext);
        String key = generateKey(fileName);
        String filepath = rootPath + key + ext;
        PutObjectResult putResult = obsClient.putObject(BootstrapConsts.bucket_name, filepath, stream);
        try {
            obsClient.close();
        } catch (Exception e) {
            log.error(e.toString());
        }
        return StringUtils.isNotBlank(putResult.getEtag()) ? filepath : null;
    }

    @Override
    public void download(HttpServletResponse response, String filePath, String filename) {
        try (InputStream fis = getFileStream(filePath); OutputStream fos = response.getOutputStream(); BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + new String(filename.getBytes("UTF-8"), "ISO-8859-1"));
            int bytesRead = 0;
            byte[] buffer = new byte[INPUT_BUFFER_SIZE];
            while((bytesRead = fis.read(buffer, 0, INPUT_BUFFER_SIZE)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void delete(String filePath) {
        ObsClient obsClient1 = getClient();
        try {
            DeleteObjectsRequest request = new DeleteObjectsRequest();
            request.setBucketName(BootstrapConsts.bucket_name);
            request.setQuiet(false);
            // 此处filePath应该为文件名，为同阿里oss保持一致不做修改；华为云obs删除桶名及文件名(objectKey)
            KeyAndVersion[] kvs = new KeyAndVersion[1];
            kvs[0] = new KeyAndVersion(filePath);
            request.setKeyAndVersions(kvs);

            DeleteObjectsResult deleteObjectsResult = obsClient1.deleteObjects(request);

            if (deleteObjectsResult.getErrorResults().size() > 0) {
                StringBuilder sb = new StringBuilder();
                sb.append("删除文件出错! 文件名： ").append(filePath).append("\n");
                for (DeleteObjectsResult.ErrorResult error : deleteObjectsResult.getErrorResults()) {
                    sb.append(error).append("\n");
                }
                log.error(sb.toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                obsClient1.close();
            }
            catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean exist(String filePath) {
        ObsClient obsClient1 = getClient();
        return obsClient1.doesObjectExist(BootstrapConsts.bucket_name, filePath);
    }

    @Override
    public String getUrl(String key) {
//        ObsClient obsClient1 = getClient();
//        try {
//            TemporarySignatureRequest req = new TemporarySignatureRequest(HttpMethodEnum.GET, 300);
//            req.setBucketName(BootstrapConsts.bucket_name);
//            req.setObjectKey(key);
//            TemporarySignatureResponse res = obsClient1.createTemporarySignature(req);
//            return res.getSignedUrl();
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(e.toString());
//        } finally {
//            try {
//                obsClient1.close();
//            }
//            catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return null;

        // 由于当前5G网关不支持https下载，因此需要返回本地下载链接
        return String.format("%s/file/download/%s", BootstrapConstsLocal.getServiceUrl(), key);
    }

    private String createFileName(String ext) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return simpleDateFormat .format(new Date()) + (int)(Math.random() * 900 + 100) + ext;
    }

    private String generateKey(String filename) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(filename);
        buffer.append(Math.random());
        return Md5Utils.getMD5(buffer.toString());
    }
}
