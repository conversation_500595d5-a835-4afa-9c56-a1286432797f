package com.jetron.nb.biz.oss;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.jetron.nb.common.util.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class OSSFileClient implements FileClient {
    public static final Integer INPUT_BUFFER_SIZE = 8192;
    private static final char PATH_SEPARATOR = '/';
    private static OSSFileClient ossFileClient;

    private OSSClient getClient() {

        return new OSSClient(BootstrapConsts.end_point, BootstrapConsts.access_key_id, BootstrapConsts.access_key_secret);
    }

    public static FileClient create() {
        if (null == ossFileClient) {
            synchronized (OSSFileClient.class) {
                if (null == ossFileClient) {
                    ossFileClient = new OSSFileClient();
                }
            }
        }
        return ossFileClient;
    }

    @Override
    public InputStream getFileStream(String code) {
        OSSClient ossClient = getClient();
        OSSObject ossObject = ossClient.getObject(BootstrapConsts.bucket_name, BootstrapConsts.root_dir + PATH_SEPARATOR + code);
        return ossObject.getObjectContent();
    }

    @Override
    public String upload(InputStream stream, String rootPath, String ext) {
        OSSClient ossClient = getClient();

        String fileName = createFileName(ext);
        String key = generateKey(fileName);
        String filepath = rootPath + key + ext;
        PutObjectResult putResult = ossClient.putObject(BootstrapConsts.bucket_name, filepath, stream);
        ossClient.shutdown();
        return StringUtils.isNotBlank(putResult.getETag()) ? filepath : null;
    }

    @Override
    public void download(HttpServletResponse response, String filePath, String filename) {
        OSSClient ossClient = getClient();
        OSSObject ossObject = ossClient.getObject(BootstrapConsts.bucket_name, filePath);
        try (InputStream fis = ossObject.getObjectContent(); OutputStream fos = response.getOutputStream(); BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + new String(filename.getBytes("UTF-8"), "ISO-8859-1"));
            int bytesRead = 0;
            byte[] buffer = new byte[INPUT_BUFFER_SIZE];
            while((bytesRead = fis.read(buffer, 0, INPUT_BUFFER_SIZE)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        } catch (Exception e){
        }
        ossClient.shutdown();
    }

    @Override
    public void delete(String filePath) {
        OSSClient aliOssClient = getClient();
        boolean flag = aliOssClient.doesObjectExist(BootstrapConsts.bucket_name, filePath);
        if(false == flag) {
            log.info("**********************The object represented by key:{} does not exist in bucket:{}.**********************", filePath, BootstrapConsts.bucket_name);
        }
        aliOssClient.deleteObject(BootstrapConsts.bucket_name, filePath);

    }

    @Override
    public boolean exist(String filePath) {
        OSSClient aliOssClient = getClient();
        return aliOssClient.doesObjectExist(BootstrapConsts.bucket_name, filePath);
    }

    @Override
    public String getUrl(String filePath) {
        OSSClient ossClient = getClient();
        //设置URL过期时间为30分钟
        Date expiration = new Date(System.currentTimeMillis() + 1800L * 1000);
        //生成URL
        URL url = ossClient.generatePresignedUrl(BootstrapConsts.bucket_name, filePath, expiration);
        if(url != null) {
            return url.toString();
        }
        return null;
    }

    private String createFileName(String ext) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return simpleDateFormat .format(new Date()) + (int)(Math.random() * 900 + 100) + ext;
    }

    private String generateKey(String filename) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(filename);
        buffer.append(Math.random());
        return Md5Utils.getMD5(buffer.toString());
    }
}
