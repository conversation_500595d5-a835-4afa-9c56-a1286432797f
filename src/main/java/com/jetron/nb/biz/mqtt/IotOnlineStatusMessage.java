package com.jetron.nb.biz.mqtt;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *  订阅 $SYS/brokers/+/clients/+/+ 主题，推送的消息转换为IotOnlineStatusMessage类
 */
@Setter
@Getter
public class IotOnlineStatusMessage implements Serializable {
    private static final long serialVersionUID = -3049202924052439112L;
    // connected
    String username;
    long ts;
    int sockport;
    int proto_ver;
    String proto_name;
    int keepalive;
    String ipaddress;
    int expiry_interval;
    long connected_at;
    int connack;
    String clientid;
    boolean clean_start;

    // disconnected
    String reason;
    long disconnected_at;

}
