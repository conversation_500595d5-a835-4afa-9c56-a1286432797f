package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.service.AcsDeviceHeartService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.AcsDeviceHeart;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.DEVICE_HEARTBEAT, describ = "心跳")
public class HeartProcessor implements MessageProcessor {

    @Value("${heartbeat.interval}")
    private Integer heartBeat;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private MqttMessageHandler mqttMessageHandler;
    @Autowired
    private RedisServiceImpl redisService;
    @Autowired
    private AcsDeviceHeartService heartService;

    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();

        try {
            AcsUserDevice userDevice = acsUserDeviceService.getBySn(sn);
            if (userDevice == null) {
                log.error("[HeartProcessor#process] no userDevice is found, messageID: {}, message : {}", message.getId(), JSONObject.toJSONString(message));
                return;
            }
            AcsDeviceHeart deviceHeart = new AcsDeviceHeart();
            deviceHeart.setSn(sn)
                    .setInsertTime(new Date())
                    .setStatus(Constants.YES_INT);

            Integer onlineTime = userDevice.getOnlineTime();
            long lastHeartBeatTime = userDevice.getLastHbTime().getTime();
            long now = DateTime.now().getMillis();
            int interval = (int)((now - lastHeartBeatTime) / 1000);
            if (interval > heartBeat + 5) {
                log.warn("[HeartProcessor#process] detected device offline, lastHeartBeatTime: {}",
                        new DateTime(lastHeartBeatTime).toString("yyyy-MM-dd HH:mm:ss"));
                // onlineTime = 0;
                // 重新连接,增加上线记录
                heartService.insert(deviceHeart);
            }
            onlineTime = onlineTime  + Math.round((now - lastHeartBeatTime) / 1000);
            redisService.set(AcsDeviceHeart.getHeartRedisKey(sn),JSONObject.toJSONString(deviceHeart));

            AcsUserDevice updateDevice = new AcsUserDevice();
            updateDevice.setId(userDevice.getId());
            updateDevice.setLastHbTime(new Date(now));
            updateDevice.setOnlineTime(onlineTime);
            updateDevice.setSignal5g(message.getSignal5G());
            updateDevice.setUpgradeDevice(message.getUpgradeTopic());
            acsUserDeviceService.updateByPrimaryKeySelective(updateDevice);
            mqttMessageHandler.heartReply(sn, message.getUpgradeTopic());
            log.info("[HeartProcessor#process] process heart success, message: {}, sn: {}", message.getId(), sn);
        } catch (Exception e) {
            log.error("[HeartProcessor#process] process error, message ID: {}, error info: {}, sn: {}", message.getId(), e.toString(), sn);
        }
    }


}