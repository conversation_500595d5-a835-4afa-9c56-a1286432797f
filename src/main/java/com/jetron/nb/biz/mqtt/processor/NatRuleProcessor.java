package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsNatManageService;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.AcsNatRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * NAT规则配置
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.NAT_RULE_CALLBACK, describ = "NAT规则配置")
public class NatRuleProcessor implements MessageProcessor {

    @Autowired
    private RedisServiceImpl redisService;

    @Autowired
    private AcsNatManageService natManageService;

    @Override
    public void process(IotMessage message) {
        // 1、封装数据
        String sn = message.getSn();
        String type = message.getType();
        Integer code = message.getCode();
        String info = message.getMessage();
        List<String> displayNameArray = message.getDisplayName();
        Integer total = message.getTotal();
        JSONArray natMap = message.getNatMap();
        JSONObject json = new JSONObject();
        String displayName = "";
        if (displayNameArray!= null && !displayNameArray.isEmpty()) {
            displayName = displayNameArray.get(0);
            json.put("displayName", displayName);
        }
        json.put("type", type);
        json.put("code", code);
        json.put("message", info);
        json.put("total", total);
        json.put("natMap", natMap);
        log.info("NAT规则配置参数：{}", json);
        // 2、存储/更新记录
        if (StringUtils.isNotBlank(sn)) {
            redisService.set(getRedisKey(sn, type), json.toJSONString());
            log.info("NAT规则存储数据至redis：{}", json);
            // 3、如果类型是删除或新增，则更新数据；如果是查询成功，去同步信息
            if (0 == code && Constants.NAT_TYPE_ADD.equals(type)) {
                // 如果是新增规则成功
                AcsNatRule natRuleByDisplayName = natManageService.getNatRuleByDisplayName(displayName);
                if (null != natRuleByDisplayName) {
                    natManageService.updateNatStatus(natRuleByDisplayName.getId(), Constants.NAT_RLUE_STATUS_NORMAL);
                }
                log.info("新增NAT规则查询数据：{}", message.getSn());
            } else if ((0 == code && Constants.NAT_TYPE_DEL.equals(type)) || (1 == code && Constants.NAT_TYPE_ADD.equals(type))) {
                // 删除NAT规则成功  或者  新增数据失败
                AcsNatRule natRuleByDisplayName = natManageService.getNatRuleByDisplayName(displayName);
                if (null != natRuleByDisplayName) {
                    natManageService.delNatLog(natRuleByDisplayName.getId());
                }
                log.info("删除NAT规则查询数据：{}", message.getSn());
            } else if (0 == code && Constants.NAT_TYPE_GET.equals(type)) {
                // 如果查询成功，去同步信息
                // 将JSONArray转换为ArrayList<AcsNat>
                ArrayList<AcsNatRule> acsNatList = new ArrayList<>();
                for (int i = 0; i < natMap.size(); i++) {
                    JSONObject jsonObject = natMap.getJSONObject(i);
                    // 封装数据
                    AcsNatRule acsNat = JSON.toJavaObject(jsonObject, AcsNatRule.class);
                    String protocol = convertString(acsNat.getProtocol());
                    acsNat.setProtocol(protocol);
                    acsNatList.add(acsNat);
                }
                // 同步数据至数据库
                natManageService.syncNatRule(acsNatList, sn);
            }
            // 4、存入mysql日志中
//            AcsNatRuleLog acsNatRuleLog = new AcsNatRuleLog();
//            acsNatRuleLog.setSn(displayName);
//            acsNatRuleLog.setPushType(type);
//            acsNatRuleLog.setNatRuleJson(json);
//            acsNatRuleLog.setGmtCreate(new Date());
//            natManageService.addNatLog(acsNatRuleLog);
        }
    }

    // 封装数据
    public static String getRedisKey(String sn, String type) {
        return sn + "_" + type + "_" + "natServer";
    }

    // 转换字符串
    private String convertString(String protocol) {
        // 去掉首尾的方括号并去掉双引号
        return protocol.replaceAll("[\\[\\]\"]", "");
    }
}
