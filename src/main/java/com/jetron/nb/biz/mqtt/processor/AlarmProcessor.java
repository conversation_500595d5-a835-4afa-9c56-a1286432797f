package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsAlarmService;
import com.jetron.nb.biz.service.AcsParameterConfigService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.ThreadPoolService;
import com.jetron.nb.biz.task.AcsCaptureAlarmRunner;
import com.jetron.nb.biz.task.AcsCaptureDataRunner;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.constant.OperatorEnum;
import com.jetron.nb.common.util.DateUtils;
import com.jetron.nb.common.util.ParamUtils;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 阈值信息处理
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.THRESHOLD_ALARM, describ = "阈值信息处理")
public class AlarmProcessor implements MessageProcessor {
    @Autowired
    private AcsParameterConfigService acsParameterConfigService;

    @Resource
    private RedisServiceImpl redisService;

    @Autowired
    private AcsUserDeviceService deviceService;

    @Autowired
    private AcsAlarmService alarmService;


    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();
        if (StringUtils.isBlank(sn)) {
            return;
        }
        List<AcsParameterConfig> acsParameterConfigs = acsParameterConfigService.findBySn(sn);
        analysisData(message, acsParameterConfigs);
    }

    private void analysisData(IotMessage message, List<AcsParameterConfig> acsParameterConfigs) {
        // 将 message 数据存入到 数据库

        List<AcsCaptureData> acsCaptureDataList = getAcsCaptureData(message, acsParameterConfigs);
        AcsCaptureDataRunner acsCaptureDataRunner = new AcsCaptureDataRunner();
        acsCaptureDataRunner.setAcsCaptureDataList(acsCaptureDataList);
        ThreadPoolService.getInstance().execute(acsCaptureDataRunner);
        if (acsParameterConfigs == null || acsParameterConfigs.size() <= 0) {
            return;
        }
        // 异常的数据存入数据库
        List<AcsCaptureAlarm> acsCaptureAlarmList = calcAlarm(message, acsParameterConfigs);
        // 过滤异常数据，排除同一报警记录
        List<AcsCaptureAlarm> addList = redisFilter(acsCaptureAlarmList, message.getSn());
        AcsCaptureAlarmRunner acsCaptureAlarmRunner = new AcsCaptureAlarmRunner();
        acsCaptureAlarmRunner.setAcsCaptureAlarmList(addList);
        acsCaptureAlarmRunner.setSn(message.getSn());
        acsCaptureAlarmRunner.setAllList(acsCaptureAlarmList);
        ThreadPoolService.getInstance().execute(acsCaptureAlarmRunner);
        // 更新设备状态
        updateUserDevice(acsCaptureAlarmList, message.getSn());
    }

    private List<AcsCaptureAlarm> calcAlarm(IotMessage message, List<AcsParameterConfig> acsParameterConfigs) {
        List<AcsCaptureAlarm> acsCaptureAlarmList = new ArrayList<>();

        if (!StringUtils.isBlank(message.getTemperature())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "DevTemp".equals(x.getParameterCode())).collect(Collectors.toList());
            if (list.size() > 0 && !operatorData(message.getTemperature(), list.get(0))) {
                AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), message.getTemperature(), list.get(0));
                acsCaptureAlarmList.add(captureAlarmData);
            }
        }

        if (!StringUtils.isBlank(message.getSigStrength())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "rssi".equals(x.getParameterCode())).collect(Collectors.toList());
            String rssi = sigStrengthTransform(message.getSigStrength());
            if (!StringUtils.isBlank(rssi)) {
                if (list.size() > 0 && !operatorData(rssi, list.get(0))) {
                    AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), rssi, list.get(0));
                    acsCaptureAlarmList.add(captureAlarmData);
                }
            }
        }

        if (!StringUtils.isBlank(message.getSigQuality())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "SignalQuality".equals(x.getParameterCode())).collect(Collectors.toList());
            if (list.size() > 0 && !operatorData(message.getSigQuality(), list.get(0))) {
                AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), message.getSigQuality(), list.get(0));
                acsCaptureAlarmList.add(captureAlarmData);
            }
        }

        if (!StringUtils.isBlank(message.getAvailableMem())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "ram".equals(x.getParameterCode())).collect(Collectors.toList());
            String ram = ParamUtils.getNumeric(message.getAvailableMem());
            if (!StringUtils.isBlank(ram)) {
                if (list.size() > 0 && !operatorData(ram, list.get(0))) {
                    AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), ram, list.get(0));
                    acsCaptureAlarmList.add(captureAlarmData);
                }
            }
        }

        if (!StringUtils.isBlank(message.getCpuUsedRate())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "CpuRation".equals(x.getParameterCode())).collect(Collectors.toList());
            String cpuRation = ParamUtils.getNumeric(message.getCpuUsedRate());
            if (!StringUtils.isBlank(cpuRation)) {
                if (list.size() > 0 && !operatorData(cpuRation, list.get(0))) {
                    AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), cpuRation, list.get(0));
                    acsCaptureAlarmList.add(captureAlarmData);
                }
            }
        }
        if (!StringUtils.isBlank(message.getTcpConnections())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> Constants.PamameterCode.TCP_CONNECTIONS.equals(x.getParameterCode())).collect(Collectors.toList());
            String tcp = ParamUtils.getNumeric(message.getTcpConnections());
            if (!StringUtils.isBlank(tcp)) {
                if (list.size() > 0 && !operatorData(tcp, list.get(0))) {
                    AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), tcp, list.get(0));
                    acsCaptureAlarmList.add(captureAlarmData);
                }
            }
        }
        if (!StringUtils.isBlank(message.getUdpConnections())) {
            List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> Constants.PamameterCode.UDP_CONNECTIONS.equals(x.getParameterCode())).collect(Collectors.toList());
            String udp = ParamUtils.getNumeric(message.getUdpConnections());
            if (!StringUtils.isBlank(udp)) {
                if (list.size() > 0 && !operatorData(udp, list.get(0))) {
                    AcsCaptureAlarm captureAlarmData = getAcsCaptureAlarmData(message.getSn(), udp, list.get(0));
                    acsCaptureAlarmList.add(captureAlarmData);
                }
            }
        }
        return acsCaptureAlarmList;
    }

    private boolean operatorData(String cData, AcsParameterConfig acsParameterConfig) {
        try {
            double c = Double.parseDouble(cData);
            if (acsParameterConfig.getParameterOperator() == OperatorEnum.BIGGER.getKey()) {
                if (c > acsParameterConfig.getParameterValue()) {
                    return false;
                }
            }
            if (acsParameterConfig.getParameterOperator() == OperatorEnum.LESS.getKey()) {
                if (c < acsParameterConfig.getParameterValue()) {
                    return false;
                }
            }
            if (acsParameterConfig.getParameterOperator() == OperatorEnum.BETWEEN.getKey()) {
                if (c < acsParameterConfig.getParameterValue() || c > acsParameterConfig.getParameterValueMax()) {
                    return false;
                }
            }
            if (acsParameterConfig.getParameterOperator() == OperatorEnum.EQUAL.getKey()) {
                if (c == acsParameterConfig.getParameterValue()) {
                    return false;
                }
            }
            if (acsParameterConfig.getParameterOperator() == OperatorEnum.NOT_EQUAL.getKey()) {
                if (c != acsParameterConfig.getParameterValue()) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error(e.toString());
            return false;
        }

        return true;
    }

    private AcsCaptureAlarm getAcsCaptureAlarmData(String sn, String cData, AcsParameterConfig acsParameterConfig) {
        AcsCaptureAlarm acsCaptureAlarm = new AcsCaptureAlarm();
        acsCaptureAlarm.setSn(sn);
        acsCaptureAlarm.setInsertTime(new Date());
        acsCaptureAlarm.setParameterCode(acsParameterConfig.getParameterCode());
        if (acsParameterConfig.getParameterOperator() == OperatorEnum.BETWEEN.getKey()) {
            acsCaptureAlarm.setParameterValue(acsParameterConfig.getParameterValue() + "/" + acsParameterConfig.getParameterValueMax());
        } else {
            acsCaptureAlarm.setParameterValue(String.valueOf(acsParameterConfig.getParameterValue()));
        }
        acsCaptureAlarm.setCompany(acsParameterConfig.getCompany());
        acsCaptureAlarm.setParameterKey(acsParameterConfig.getParameterKey());
        acsCaptureAlarm.setParameterContent(acsParameterConfig.getParameterContent());
        double c;
        try {
            c = Double.parseDouble(cData);
            acsCaptureAlarm.setCurrentValue(String.valueOf(c));
        } catch (Exception e) {
            log.error(e.toString());
        }
        return acsCaptureAlarm;
    }

    private List<AcsCaptureData> getAcsCaptureData(IotMessage message,List<AcsParameterConfig> acsParameterConfigs) {
        String company = null;
        if (!CollectionUtils.isEmpty(acsParameterConfigs)) {
            AcsParameterConfig acsParameterConfig = acsParameterConfigs.get(0);
            company = acsParameterConfig.getCompany();
        } else {
            UserDeviceFilter filter = new UserDeviceFilter();
            filter.setSn(message.getSn());
            List<AcsUserDevice> listWithUser = deviceService.findListWithUser(filter);
            if (CollectionUtils.isEmpty(listWithUser)) {
                return new ArrayList<>();
            }
            company = listWithUser.get(0).getUser().getCompany();
        }
        AcsCaptureData acsCaptureData = new AcsCaptureData();
        acsCaptureData.setCpuRation(message.getCpuUsedRate());
        acsCaptureData.setDevTemp(message.getTemperature());
        acsCaptureData.setInsertTime(new Date());
        acsCaptureData.setRam(message.getAvailableMem());
        acsCaptureData.setRssi(message.getSigStrength());
        acsCaptureData.setSignalQuality(message.getSigQuality());
        acsCaptureData.setRsrq(message.getRsrq());
        acsCaptureData.setSn(message.getSn());
        acsCaptureData.setCompany(company);
        List<AcsCaptureData> list = new ArrayList<>();
        list.add(acsCaptureData);
        return list;
    }

    /**
     * 将信号强度转为对应的 数字
     *
     * @param sigStrength 信号强度
     * @return 信号强度对应的数字
     */
    private String sigStrengthTransform(String sigStrength) {
        String rssi = "0";
        sigStrength = sigStrength.toUpperCase();
        switch (sigStrength) {
            case "UNKNOWN":
                rssi = "0";
                break;
            case "BAD":
                rssi = "1";
                break;
            case "POOR":
                rssi = "2";
                break;
            case "MODERATE":
                rssi = "3";
                break;
            case "GOOD":
                rssi = "4";
                break;
            case "GREAT":
                rssi = "5";
                break;
            default:
                // DTU 和 Jenet网关信号数据不同，JENET网关的信号强度例如“90dBm”
                rssi = ParamUtils.getNumeric(sigStrength);
        }
        return rssi;
    }

    /**
     * 报警数据过滤，避免重复报警，报警后，未解除报警，不再有新的报警记录
     *
     * @param acsCaptureAlarmList 报警异常数据
     * @param sn                  SN号
     * @return 返回过滤数据
     */
    public List<AcsCaptureAlarm> redisFilter(List<AcsCaptureAlarm> acsCaptureAlarmList, String sn) {
        List<AcsCaptureAlarm> addList = new ArrayList<>();
        // 一次报警事件，只有一次报警记录。 排除多次报警数据
        /*List<AcsCaptureAlarm> list = alarmService.findActiveAlarm(new AcsCaptureAlarm().setSn(sn).setStatusNe(Constants.ALARM_STATUS3));
        List<String> alarmCodeList = list.stream().map(AcsCaptureAlarm::getParameterCode).collect(Collectors.toList());
        addList = acsCaptureAlarmList.stream()
                .filter(obj -> !alarmCodeList.contains(obj.getParameterCode()))
                .collect(Collectors.toList());*/

        String str = redisService.get(getAlarmCodeKey(sn));
        Map<String, String> redisMap = JSONObject.parseObject(str, HashMap.class);
        if (redisMap == null) {
            redisMap = new HashMap<>();
        }
        Map<String, String> newRedisMap = new HashMap<>();
        for (int i = acsCaptureAlarmList.size() - 1; i >= 0; i--) {
            AcsCaptureAlarm alarm = acsCaptureAlarmList.get(i);
            String parameterCode = alarm.getParameterCode();
            AlarmCacheInfo cacheInfo = JSONObject.parseObject(redisMap.get(parameterCode), AlarmCacheInfo.class);
            if (cacheInfo != null) {
                // 同一报警参数,只报警一次,如果已经报警，则不增加报警记录
                newRedisMap.put(parameterCode, JSONObject.toJSONString(cacheInfo));
            } else {
                addList.add(alarm);
                cacheInfo = new AlarmCacheInfo(parameterCode, new Date(), null);
                newRedisMap.put(parameterCode, JSONObject.toJSONString(cacheInfo));
            }
        }
        redisService.set(getAlarmCodeKey(sn), JSONObject.toJSONString(newRedisMap));
        return addList;
    }

    private void updateUserDevice(List<AcsCaptureAlarm> acsCaptureAlarmList, String sn) {
        // 判断是否更新设备状态为报警
        String alarmKey = getAlarmKey(sn);
        if (acsCaptureAlarmList.size() > 0) {
            AcsUserDevice device = deviceService.getBySn(sn);
            if (device != null && device.getStatus() == AcsUserDeviceService.DEVICE_NORMAL) {
                AcsUserDevice param = new AcsUserDevice();
                param.setStatus(AcsUserDeviceService.DEVICE_ALARM);
                param.setId(device.getId());
                deviceService.updateByPrimaryKeySelective(param);
            }
            redisService.set(alarmKey, String.valueOf(AcsUserDeviceService.DEVICE_ALARM));
            // redisService.expire(getAlarmKey(sn), 60);
        } else {
            String s = redisService.get(alarmKey);
            if ("1".equals(s)) {
                AcsUserDevice device = deviceService.getBySn(sn);
                if (device != null && device.getStatus() == AcsUserDeviceService.DEVICE_ALARM) {
                    AcsUserDevice param = new AcsUserDevice();
                    param.setStatus(AcsUserDeviceService.DEVICE_NORMAL);
                    param.setId(device.getId());
                    deviceService.updateByPrimaryKeySelective(param);
                }
            }
            redisService.expire(alarmKey, 0);
            redisService.expire(getAlarmCodeKey(sn), 0);
        }
    }

    private String getAlarmKey(String sn) {
        return "alarm_user_device_" + sn;
    }

    private String getAlarmCodeKey(String sn) {
        return "alarm_code_" + sn;
    }


    /**
     * 报警缓存信息。
     * 避免同一报警参数，同一报警值重复报警。
     * 要求同一报警参数，不同值报警
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AlarmCacheInfo implements Serializable {
        public String parameterCode;
        public Date time;
        public String value;
    }
}
