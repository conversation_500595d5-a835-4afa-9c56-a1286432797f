package com.jetron.nb.biz.mqtt.processor;

import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsDriverUpgradeService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsDriverUpgrade;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * IP绑定订阅
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.DRIVER_UPGRADE_RECEIVED, describ = "网关收到升级驱动响应")
public class DriverReveivedProcessor implements MessageProcessor {

    @Autowired
    private AcsUserDeviceService deviceService;
    @Autowired
    private AcsDriverUpgradeService driverUpgradeService;

    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();
        Integer code = message.getCode();
        AcsUserDevice bySn = deviceService.getBySn(sn);
        Integer deviceId = bySn.getId();

        AcsDriverUpgrade upgradeParam = new AcsDriverUpgrade();
        upgradeParam.setStatus(Constants.DriverUpgradeStatus.INT);
        upgradeParam.setDeviceId(deviceId);

        AcsDriverUpgrade one = driverUpgradeService.find(upgradeParam);
        if (one == null) {
            log.warn("DriverReveivedProcessor;{}","网关发送收到升级响应，但DriverUpgrade表中无待升级记录");
            return;
        }
        AcsDriverUpgrade updateParam = new AcsDriverUpgrade();
        updateParam.setId(one.getId());
        updateParam.setStatusEq(Constants.DriverUpgradeStatus.INT);
        driverUpgradeService.updateStatus(updateParam,code);
        log.info("DriverReveivedProcessor;网关发送收到升级响应成功：{}",sn);
    }


}
