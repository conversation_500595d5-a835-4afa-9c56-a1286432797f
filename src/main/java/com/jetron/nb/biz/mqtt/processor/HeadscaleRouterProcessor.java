package com.jetron.nb.biz.mqtt.processor;

import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.biz.service.AcsHeadscaleUserService;
import com.jetron.nb.biz.service.HeadscaleHybridService;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import com.jetron.nb.dal.po.AcsHeadscaleUser;
import com.jetron.nb.dal.po.HeadscaleNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * Headscale路由处理器
 * 处理启用/禁用Headscale路由的MQTT消息
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.ENABLE_HEADSCALE_ROUTER, describ = "启用Headscale路由")
public class HeadscaleRouterProcessor implements MessageProcessor {

    @Autowired
    private AcsUserDeviceService acsUserDeviceService;

    @Autowired
    private AcsUserService acsUserService;

    @Autowired
    private AcsHeadscaleUserService acsHeadscaleUserService;

    @Autowired
    private HeadscaleHybridService headscaleHybridService;

    @Autowired
    private AcsTopicService acsTopicService;

    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    @Override
    public void process(IotMessage message) {
        String sn = null;
        String router = null;

        try {
            // 获取设备SN号和路由信息
            sn = message.getSn();
            router = message.getRouter();

            if (StringUtils.isBlank(sn)) {
                log.warn("[HeadscaleRouterProcessor] SN is blank, ignoring message");
                return;
            }

            if (StringUtils.isBlank(router)) {
                log.warn("[HeadscaleRouterProcessor] Router is blank for SN: {}, ignoring message", sn);
                sendRouterResponse(sn, "", 1); // Send failure response
                return;
            }

            log.info("[HeadscaleRouterProcessor] Processing Headscale router request for SN: {}, router: {}", sn, router);

            // 通过SN号查找设备信息
            AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
            if (acsUserDevice == null) {
                log.warn("[HeadscaleRouterProcessor] Device not found for SN: {}", sn);
                sendRouterResponse(sn, router, 1); // Send failure response
                return;
            }

            // 通过设备信息查找用户信息
            AcsUser acsUser = acsUserService.selectByPrimaryKey(acsUserDevice.getBelongTo());
            if (acsUser == null) {
                log.warn("[HeadscaleRouterProcessor] User not found for device SN: {}", sn);
                sendRouterResponse(sn, router, 1); // Send failure response
                return;
            }

            // 查找对应的Headscale用户记录
            AcsHeadscaleUser acsHeadscaleUser = acsHeadscaleUserService.getByAcsUserId(acsUser.getId());
            if (acsHeadscaleUser == null) {
                log.warn("[HeadscaleRouterProcessor] Headscale user not found for ACS user: {} (device SN: {})",
                        acsUser.getName(), sn);
                sendRouterResponse(sn, router, 1); // Send failure response
                return;
            }

            String headscaleUsername = acsHeadscaleUser.getUsername();
            log.info("[HeadscaleRouterProcessor] Found Headscale user: {} for device SN: {}", headscaleUsername, sn);

            // 获取用户的节点列表以找到节点ID
            List<HeadscaleNode> nodes = headscaleHybridService.getNodesByUser(headscaleUsername);
            if (nodes == null || nodes.isEmpty()) {
                log.warn("[HeadscaleRouterProcessor] No nodes found for Headscale user: {} (device SN: {})",
                        headscaleUsername, sn);
                sendRouterResponse(sn, router, 1); // Send failure response
                return;
            }

            // 使用第一个节点（通常用户只有一个节点）
            HeadscaleNode node = nodes.get(0);
            String nodeId = node.getId();
            log.info("[HeadscaleRouterProcessor] Found node ID: {} for user: {} (device SN: {})",
                    nodeId, headscaleUsername, sn);

            // 调用Headscale API设置批准的路由
            List<String> routes = Arrays.asList(router);
            headscaleHybridService.setApprovedRoutes(nodeId, routes);

            log.info("[HeadscaleRouterProcessor] Successfully enabled route: {} for node: {} (device SN: {})",
                    router, nodeId, sn);

            // 发送成功响应
            sendRouterResponse(sn, router, 0);

        } catch (Exception e) {
            log.error("[HeadscaleRouterProcessor] Failed to process router request for SN: {}, router: {}",
                    sn, router, e);
            sendRouterResponse(sn, router != null ? router : "", 1); // Send failure response
        }
    }

    /**
     * 向设备发送路由设置结果响应
     */
    private void sendRouterResponse(String sn, String router, int result) {
        try {
            // 获取MQTT下发主题
            String upgradeDeviceTopic = acsTopicService.getPublishUpgradeDeviceTopic(sn);

            // 调用MqttMessageHandler下发路由设置结果
            mqttMessageHandler.sendHeadscaleRouterResponse(upgradeDeviceTopic, router, result, sn);

            log.info("[HeadscaleRouterProcessor] Successfully sent router response to device SN: {}, router: {}, result: {}",
                    sn, router, result);

        } catch (Exception e) {
            log.error("[HeadscaleRouterProcessor] Failed to send router response to device SN: {}, router: {}, result: {}",
                    sn, router, result, e);
        }
    }
}
