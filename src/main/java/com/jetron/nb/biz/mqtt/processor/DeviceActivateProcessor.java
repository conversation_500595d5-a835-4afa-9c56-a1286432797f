package com.jetron.nb.biz.mqtt.processor;

import com.jetron.nb.biz.mqtt.*;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.common.constant.MessageCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.DEVICE_ACTIVATE, describ = "设备激活")
public class DeviceActivateProcessor implements MessageProcessor {

    @Autowired
    private MqttClientManager mqttClientManager;
    @Autowired
    private AcsTopicService acsTopicService;

    @Override
    public void process(IotMessage message) {
        try {
            String topic = acsTopicService.getSubscribeDeviceTopic(message.getSn());
            if (StringUtils.isEmpty(topic)) {
                log.error("[DeviceActivateProcessor#process] error,topic is {}", topic );
                return;
            }
            if (message.getUpgradeTopic()) {
                String upgradeDeviceTopic = acsTopicService.getSubscribeUpgradeDeviceTopic(message.getSn());
                mqttClientManager.subscribe(upgradeDeviceTopic);
            } else {
                mqttClientManager.subscribe(topic);
            }
            log.info("[DeviceActivateProcessor#process] subscribe {} success, message: {}", topic, message.getId());
        } catch (Exception e) {
            log.error("[DeviceActivateProcessor#process] error, message ID: {}, error info: {}", message.getId(), e.toString());
        }

    }
}
