package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * IP绑定订阅
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.BIND_IP_S, describ = "IP绑定订阅")
public class BindIPProcessor implements MessageProcessor {

    @Autowired
    RedisServiceImpl redisService;

    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();
        String bindIP = message.getBindIP();
        String enable = message.getEnable();
        JSONObject json = new JSONObject();
        json.put("bindIP",bindIP);
        json.put("enable",enable);
        redisService.set(getRedisKey(sn),json.toJSONString());
    }

    public static String getRedisKey(String sn) {
        return sn + "_" + "bindIP";
    }

}
