package com.jetron.nb.biz.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.jetron.nb.IotPlatformApplication;
import com.jetron.nb.biz.oss.BootstrapConstsLocal;
import com.jetron.nb.biz.service.*;
import com.jetron.nb.biz.service.mqtt.AcsMqttOnlineService;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.constant.RedisDatabaseEnum;
import com.jetron.nb.common.constant.RedisKey;
import com.jetron.nb.common.util.MqttUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttMessageHandler {
    private static ThreadPoolExecutor executor;
    /**
     *  设备在线、离线订阅主题
     */
    private static final String ONLINE_STATUS_TOPIC = "$SYS/brokers/+/clients/+/+";
    /**
     * 不带控制台界面的网关设备注册、取消注册主题
     */
    private static final String GATEWAY_UNREGISTERED_TOPIC = "$queue/SN/+/unregistered/+";

    static {
        int cpuNum = Runtime.getRuntime().availableProcessors();
        executor = new ThreadPoolExecutor(cpuNum, cpuNum * 2, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100000)
                , new ThreadFactoryBuilder().setNameFormat("mqtt-%d").build());
    }

    @Autowired
    private MqttClientManager mqttClientManager;

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private RedisServiceImpl redisService;

    @Autowired
    private AcsUserService acsUserService;
    @Autowired
    private AcsTopicService topicService;
    @Autowired
    private AcsMqttOnlineService acsMqttOnlineService;
    @Autowired
    private AcsUserDeviceService userDeviceService;
    @Autowired
    private AcsDeviceService acsDeviceService;

    @Value("${iotPlatform.url}")
    private String iotUrl;

    @Value("${app.deployType}")
    private String deployType;
    @Autowired
    private AcsParameterConfigService acsParameterConfigService;
    @Autowired
    private AcsAlarmService acsAlarmService;


    @PostConstruct
    public void init() throws IOException {
        // 系统初始化时订阅所有topic，包括MQTT/ACS/SYNC，MQTT/公司username/config，$SYS/brokers/+/clients/+/+ 等
        subscribeTopics();

        IotPlatformApplication.addPreHaltTask(new Runnable() {
            @Override
            public void run() {
                try {
                    log.info("[MqttMessageHandler#preHaltTask] start");
                    unsubscribeTopics();
                    Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
                    executor.shutdown();
                    while(!executor.isTerminated()) {
                        log.info("[MqttMessageHandler#preHaltTask] not finished yet, active task count: {}", executor.getActiveCount());
                        executor.awaitTermination(1, TimeUnit.SECONDS);
                    }
                    ThreadPoolService.getInstance().shutdown();
                    log.info("[MqttMessageHandler#preHaltTask] end");
                } catch (Exception e) {
                    log.info("[MqttMessageHandler#preHaltTask] failed, {}", e);
                }
            }
        });
    }

    public void subscribeTopics() throws IOException {
        // 订阅所有设备的topic
        ScanOptions options = ScanOptions.scanOptions().count(50).build();
        Cursor<String> cursor = redisTemplate.opsForSet().scan(RedisKey.ALL_SN_SET.key, options);
        try {
            int cnt = 0;
            while (cursor.hasNext()) {
                String sn = cursor.next();
                if (StringUtils.isEmpty(sn)) {
                    continue;
                }
                String deviceTopic = topicService.getSubscribeDeviceTopic(sn);
                mqttClientManager.subscribe(deviceTopic);

                String upgradeDeviceTopic = topicService.getSubscribeUpgradeDeviceTopic(sn);
                mqttClientManager.subscribe(upgradeDeviceTopic);
                cnt++;
            }
            if (cnt == 0) {
                // 如果cnt = 0 认为redis重启并且丢失sn数据。
                SetOperations<String, String> operations = redisTemplate.opsForSet();
                UserDeviceFilter filter = new UserDeviceFilter();
                List<AcsUserDevice> list = userDeviceService.findList(filter);
                if (!CollectionUtils.isEmpty(list)) {
                    for (int i = 0; i < list.size(); i++) {
                        String sn = list.get(i).getSn();
                        String deviceTopic = topicService.getSubscribeDeviceTopic(sn);
                        mqttClientManager.subscribe(deviceTopic);
                        String upgradeDeviceTopic = topicService.getSubscribeUpgradeDeviceTopic(sn);
                        mqttClientManager.subscribe(upgradeDeviceTopic);
                        operations.add(RedisKey.ALL_SN_SET.key, sn);
                        cnt++;
                    }
                }
            }
            log.info("subscribe all device topic success! total: {}", cnt);
        } finally {
            cursor.close();
        }

        // 订阅Acs同步信息topic
        String acsTopic = topicService.getSubscribeAcsTopic();
        mqttClientManager.subscribe(acsTopic);
        log.info("subscribe acs sync topic success!");

        // 订阅系统主题：设备在线离线主题
        mqttClientManager.subscribe(ONLINE_STATUS_TOPIC);
        // 订阅网关未注册主题
        mqttClientManager.subscribe(GATEWAY_UNREGISTERED_TOPIC);

        List<String> names = acsUserService.getAllUserNames();
        for (String name : names) {
            String userTopic = topicService.getSubscribeUserTopic(name);
            mqttClientManager.subscribe(userTopic);
        }
        log.info("subscribe all user topic success! total: {}", names.size());
    }

    private void unsubscribeTopics() {
        try {
            // 取消订阅所有设备的topic
            ScanOptions options = ScanOptions.scanOptions().count(50).build();
            Cursor<String> cursor = null;
            try {
                cursor = redisTemplate.opsForSet().scan(RedisKey.ALL_SN_SET.key, options);
                int cnt = 0;
                while (cursor.hasNext()) {
                    String sn = cursor.next();
                    if (StringUtils.isEmpty(sn)) {
                        continue;
                    }

                    String deviceTopic = topicService.getSubscribeDeviceTopic(sn);
                    mqttClientManager.unsubscribe(deviceTopic);

                    String upgradeDeviceTopic = topicService.getSubscribeUpgradeDeviceTopic(sn);
                    mqttClientManager.unsubscribe(upgradeDeviceTopic);
                    cnt++;
                }
                log.info("unsubscribe all device topic success! total: {}", cnt);
            } finally {
                if (cursor != null) {
                    try {
                        cursor.close();
                    } catch (Exception e) {
                        log.warn("Failed to close cursor", e);
                    }
                }
            }

            // 取消订阅Acs同步信息topic
            String acsTopic = topicService.getSubscribeAcsTopic();
            mqttClientManager.unsubscribe(acsTopic);
            log.info("unsubscribe acs sync topic success!");

            // 取消订阅所有用户的topic
            List<String> names = acsUserService.getAllUserNames();
            for (String name : names) {
                String userTopic = topicService.getSubscribeUserTopic(name);
                mqttClientManager.unsubscribe(userTopic);
            }
            log.info("unsubscribe all user topic success! total: {}", names.size());
        } catch (Exception e) {
            log.error("Error during unsubscribe topics", e);
        }
    }

    public void handleArrivedMessage(final IotMessage message) {
        try {
            Integer cmd = message.getCmd();
            if (cmd == null) {
                log.error("[MqttMessageHandler#handleArrivedMessage] invalid message, message:{}", JSON.toJSONString(message));
                return;
            }

            final MessageProcessor processor = MessageProcessorFactory.getProcessor(cmd);
            if (processor == null) {
                log.warn("[MqttMessageHandler#handleArrivedMessage] unkown message type, message: {}", JSON.toJSONString(message));
                return;
            }

            executor.execute(new Runnable() {
                @Override
                public void run() {
                    processor.process(message);
                }
            });

            log.info("[MqttMessageHandler#handleArrivedMessage] message is submitted, id: {}, message: {}",
                    message.getId(), JSON.toJSONString(message));
        } catch (Exception e) {
            log.error("[MqttMessageHandler#handleArrivedMessage] failed, message: {}", JSON.toJSONString(message));
        }

    }

    public void handlerOnlineStatusMessage(final String message, final String topic) {
        IotOnlineStatusMessage onlineStatusMessage = JSON.parseObject(message, IotOnlineStatusMessage.class);
        int status = 0;
        String sn = onlineStatusMessage.getClientid();
        if (onlineStatusMessage.getClientid().contains(Constants.MQTT_ONLINE_PUB_PREFIX) || onlineStatusMessage.getClientid().contains(Constants.MQTT_ONLINE_SUB_PREFIX)) {
            sn = onlineStatusMessage.getClientid().substring(5);
        }

        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        deviceFilter.setSn(sn);
        List<AcsUserDevice> list = userDeviceService.findListWithUser(deviceFilter);
        AcsUserDevice bySn = CollectionUtils.isEmpty(list) ? null : list.get(0);
        // 收到在线信息，clientid存入redis；设备下线，删除clientid
        if (onlineStatusMessage.getConnected_at() > 0) {
            redisService.set(onlineStatusMessage.getClientid(),message, RedisDatabaseEnum.ONLINE_STATUS.getKey());
            status = 1;
            if (bySn != null && !onlineStatusMessage.getClientid().contains(Constants.MQTT_ONLINE_PUB_PREFIX)) {
                AcsUserDevice param = new AcsUserDevice();
                param.setId(bySn.getId());
                param.setOffLine(1);
                param.setLoginDate(new Date());
                userDeviceService.updateByPrimaryKeySelective(param);
            }
        } else {
            redisService.delete(onlineStatusMessage.getClientid(), RedisDatabaseEnum.ONLINE_STATUS.getKey());
            // 设备离线删除报警信息
            // 如果是带idPub标识的客户端断线，不处理离线报警、在线时长重置等逻辑，与设备详情里设备上下线同时采用idSub或SN为客户端标识
            if (bySn != null && !onlineStatusMessage.getClientid().contains(Constants.MQTT_ONLINE_PUB_PREFIX)) {
                AcsUserDevice param = new AcsUserDevice();
                param.setStatus(0);
                param.setId(bySn.getId());
                param.setOnlineTime(0);
                long now = DateTime.now().getMillis();
                param.setLastHbTime(new Date(now));
                param.setOffLine(1);
                param.setLoginDate(null);
                userDeviceService.updateByPrimaryKeySelective(param);

                // 增加离线报警
                String company = bySn.getUser().getCompany();
                String model = bySn.getModel();
                List<AcsCaptureAlarm> captureAlarmList = new ArrayList<>();
                AcsParameterConfig parameterConfig = new AcsParameterConfig();
                parameterConfig.setParameterCode(Constants.PamameterCode.OFFLINE);
                List<AcsParameterConfig> parameterConfigList = acsParameterConfigService.findList(parameterConfig);
                if (!CollectionUtils.isEmpty(parameterConfigList)) {
                    for (int j = 0; j < parameterConfigList.size(); j++) {
                        AcsParameterConfig config = parameterConfigList.get(j);
                        if (org.apache.commons.lang3.StringUtils.equals(company,config.getCompany()) &&
                                org.apache.commons.lang3.StringUtils.equals(model,config.getParameterKey())) {
                            AcsCaptureAlarm acsCaptureAlarm = new AcsCaptureAlarm();
                            acsCaptureAlarm.setSn(bySn.getSn());
                            acsCaptureAlarm.setInsertTime(new Date());
                            acsCaptureAlarm.setParameterCode(config.getParameterCode());
                            acsCaptureAlarm.setCompany(config.getCompany());
                            acsCaptureAlarm.setParameterKey(config.getParameterKey());
                            acsCaptureAlarm.setParameterContent(config.getParameterContent());
                            acsCaptureAlarm.setCurrentValue(Constants.NO);
                            acsCaptureAlarm.setParameterValue(String.valueOf(config.getParameterValue()));
                            acsCaptureAlarm.setIp("0");
                            captureAlarmList.add(acsCaptureAlarm);
                            acsAlarmService.insertList(captureAlarmList);
                            break;
                        }
                    }

                }
            }

        }

        // 记录设备断线离线时间
        AcsMqttOnline acsMqttOnline = new AcsMqttOnline();
        acsMqttOnline.setStatus(status);
        acsMqttOnline.setSn(onlineStatusMessage.getClientid());
        acsMqttOnline.setInsertTime(new Date());
        acsMqttOnlineService.insert(acsMqttOnline);
    }

    /**
     * 设备激活
     * @param sn
     * @param username
     */
    public void activate(String sn, String username) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.DEVICE_ACTIVATE.cmd);
        message.setSn(sn);
        try {
            topicService.delUserName(sn);
            String deviceTopic = topicService.getSubscribeDeviceTopic(sn);
            String userTopic = topicService.getSubscribeUserTopic(username);
            mqttClientManager.subscribe(deviceTopic);
            mqttClientManager.subscribe(userTopic);
            // 新版本升级设备主题
            String upgradeDeviceTopic = topicService.getSubscribeUpgradeDeviceTopic(sn);
            mqttClientManager.subscribe(upgradeDeviceTopic);
        } catch (Exception e) {
            log.info("[MqttMessageHandler#activate] subscribe error, {}", sn + "  " + e.toString());
        }
        mqttClientManager.publish(topicService.getPublishAcsTopic(), JSON.toJSONString(message));
        redisTemplate.opsForSet().add(RedisKey.ALL_SN_SET.key, sn);

        log.info("[MqttMessageHandler#activate] publish activate message success, {}", message);
    }

    /**
     * 设备升级
     * @param snMap snMap
     * @param name
     */
    public void firmwareUpgrade(Map<String, Boolean> snMap, String name, String url) {
        snMap.forEach((sn, isUpgrade) -> {
            IotMessage message = new IotMessage();
            message.setCmd(MessageCmd.FIRMWARE_UPGRADE.cmd);
            message.setUrl(url);
            if (null != isUpgrade && isUpgrade) {
                mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), JSON.toJSONString(message));
            } else {
                mqttClientManager.publish(topicService.getPublishDeviceTopic(sn), JSON.toJSONString(message));
            }
            log.info("[MqttMessageHandler#firmwareUpgrade] firmware upgrade, sn: {}, firmware: {}, url: {}", sn, name, url);
        });
    }

    public Result sendMqtt(String sn, JSONObject param, Boolean isUpgrade) {
        log.info("netWorkSetUp；sn:{},param:{}", sn, param.toJSONString());
        String deviceTopic = topicService.getPublishDeviceTopic(sn);
        if (null != isUpgrade && isUpgrade) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), param.toJSONString());
        } else {
            mqttClientManager.publish(deviceTopic, param.toJSONString());
        }
        return Result.success();
    }

    /**
     * 设备日志
     *
     * @param sn
     * @param isUpgrade
     */
    public void deviceLog(String sn, Boolean isUpgrade) {
        if ("local".equals(deployType)) {
            iotUrl = BootstrapConstsLocal.getServiceUrl();
        }
        String urlPattern = iotUrl + "/device/logUpload?sn=%s";
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.DEVICE_LOG_UPLOAD.cmd);

        String url = String.format(urlPattern, sn);
        message.setUrl(url);

        String topic = topicService.getPublishDeviceTopic(sn);
        String msgContent = JSON.toJSONString(message);
        if (null != isUpgrade && isUpgrade) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), msgContent);
        } else {
            mqttClientManager.publish(topic, msgContent);
        }
        log.info("[MqttMessageHandler#deviceLog] publish device log message success, {}", msgContent);
    }

    /**
     * 配置升级
     * @param snMap
     * @param configName
     */
    public void configUpgrade(Map<String, Boolean> snMap, String configName, String url) {
        snMap.forEach((sn, isUpgrade) -> {
            IotMessage message = new IotMessage();
            message.setCmd(MessageCmd.DEVICE_CONFIG_UPGRADE.cmd);
            message.setUrl(url);

            String topic = topicService.getPublishDeviceTopic(sn);
            if (null != isUpgrade && isUpgrade) {
                mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), JSON.toJSONString(message));
            } else {
                mqttClientManager.publish(topic, JSON.toJSONString(message));
            }
            log.info("[MqttMessageHandler#configUpgrade] config upgrade, sn: {}, configName: {}", sn, configName);

        });
    }

    public void dispatchVpnConfigBySn(String sn, String config, Boolean isUpgrade) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.VPN_CONFIG_DISPATCH.cmd);
        message.setContent(config);

        String topic = topicService.getPublishDeviceTopic(sn);
        if (null != isUpgrade && isUpgrade) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), JSON.toJSONString(message));
        } else {
            mqttClientManager.publish(topic, JSON.toJSONString(message));
        }
        log.info("[MqttMessageHandler#dispatchVpnConfigBySn] VPN config dispatch, sn: {}", sn);
    }

    public void dispatchVpnConfigByUsername(String username, String config, List<AcsUserDevice> deviceList) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.VPN_CONFIG_DISPATCH.cmd);
        message.setContent(config);

        String topic = topicService.getPublishUserTopic(username);
        mqttClientManager.publish(topic, JSON.toJSONString(message));
        log.info("[MqttMessageHandler#dispatchVpnConfigByUserId] VPN config dispatch, userName: {}, topic: {}", username, topic);

        if (!CollectionUtils.isEmpty(deviceList)) {
            String userDeviceTopic = topicService.getPublishUserTopic(username);
            for (AcsUserDevice device : deviceList) {
                IotMessage deviceMessage = new IotMessage();
                deviceMessage.setCmd(MessageCmd.DEVICE_INFO_UPLOAD.cmd);
                deviceMessage.setSn(device.getSn());
                deviceMessage.setName(device.getName());
                deviceMessage.setNo(device.getModel());

                deviceMessage.setVerNo(device.getVerNo());
                deviceMessage.setHardVerNO(device.getHardVerNo());

                deviceMessage.setSubIp(device.getSubIp());
                deviceMessage.setDumIp(device.getDumIp());
                deviceMessage.setTrueIp(device.getTrueIp());

                deviceMessage.setSignalNum(device.getSignalNum());
                deviceMessage.setSignal5G(device.getSignal5g());

                deviceMessage.setGPS(device.getGps());
                deviceMessage.setDescrib(device.getDescrib());

                deviceMessage.setBaseDevNum(device.getBaseDevNum());
                deviceMessage.setAlias(device.getAlias());
                deviceMessage.setLocation(device.getPosition());

                deviceMessage.setLoginDate(getLoginDateByConnected(device.getSn(), device.getLoginDate()));
                if (!StringUtils.isEmpty(device.getBaseDev())) {
                    deviceMessage.setBaseDev(JSON.parseArray(device.getBaseDev()));
                }

                mqttClientManager.publish(userDeviceTopic, JSON.toJSONString(deviceMessage));
            }

            log.info("[MqttMessageHandler#dispatchVpnConfigByUserId] device info dispatch, username: {}, topic: {}, total: {}", username, userDeviceTopic, deviceList.size());
        }
    }

    /**
     * 根据链接情况获取登录时间，
     *    API在线、loginDate在线，返回 loginDate值；
     *    API在线、loginDate在线，返回 loginDate值；
     *    API离线、loginDate在线，返回 loginDate = null 离线消息
     */
    private Date getLoginDateByConnected(String sn, Date loginDate) {
        Result clients = MqttUtils.getClientsByClientId(sn);
        List<MqttUtils.Client> clientList = (List<MqttUtils.Client>) clients.getData();
        boolean connected = clientList != null && !clientList.isEmpty() && clientList.get(0).getConnected();
        if (connected) {
            return Optional.ofNullable(loginDate).orElseGet(() -> {
                try {
                    return DateUtils.parseDate(clientList.get(0).getCreatedAt(), "yyyy-MM-dd HH:mm:ss");
                } catch (ParseException e) {
                    throw new RuntimeException("Failed to parse the creation date", e);
                }
            });
        } else {
            return null;
        }
    }

    public void heartReply(String sn, Boolean isUpgrade) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.DEVICE_HEARTBEAT_RESULT.cmd);
        message.setResult(1);

        String topic = topicService.getPublishDeviceTopic(sn);
        if (null != isUpgrade && isUpgrade) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), JSON.toJSONString(message));
        } else {
            mqttClientManager.publish(topic, JSON.toJSONString(message));
        }
        log.info("[MqttMessageHandler#heartReply] Heart reply, sn: {}, message: {}", sn, message);
    }

    public void handlerUnregisteredMessage(final String message, final String topic) {
        IotUnregisteredMessage iotUnregisteredMessage = JSON.parseObject(message, IotUnregisteredMessage.class);
        if (topic.contains("up")) {
            handlerUnregisteredMessageUp(iotUnregisteredMessage);
        } else if (topic.contains("down")) {
            handlerUnregisteredMessageDown(iotUnregisteredMessage);
        }
    }

    private void handlerUnregisteredMessageUp(final IotUnregisteredMessage message) {
        DeviceFilter deviceFilter = new DeviceFilter();
        deviceFilter.setSn(message.getSn());
        List<AcsDevice> acsDevices = acsDeviceService.getByFilter(deviceFilter);
        if (acsDevices == null || acsDevices.size() <= 0) {
            return;
        }

        if (message.getCmd() == 1) {
            AcsUser acsUser = acsUserService.selectByPrimaryKey(acsDevices.get(0).getAllocateUserId());
            if (acsUser == null) {
                return;
            }
            String downTopic = "SN/" + message.getSn() + "/unregistered/down";
            message.setActivated(1);
            message.setActivatedName(acsUser.getName());
            message.setActivatedPasswd(acsUser.getPasswd());
            mqttClientManager.publish(downTopic, JSON.toJSONString(message));
            log.info("Auto Registered Publish. SN: {}, Username: {}, Date: {}", message.getSn(), acsUser.getName(), new Date());
        }

        if (message.getCmd() == 2) {
            log.info("sn: {}, reactivate: {}, Date: {}", message.getSn(), message.getActivated(), new Date());
        }
    }

    private void handlerUnregisteredMessageDown(final IotUnregisteredMessage message) {

    }

    public void handlerReactivateMessage(String sn) {
        IotUnregisteredMessage message = new IotUnregisteredMessage();
        message.setCmd(2);
        message.setSn(sn);
        try {
            String downTopic = "SN/" + sn + "/unregistered/down";
            mqttClientManager.publish(downTopic, JSON.toJSONString(message));
        } catch(Exception e) {
            log.error("[MqttMessageHandler#handlerReactivateMessage] Heart reply, sn: {}, error: {}", sn, e.toString());
        }
    }

    public void pushVpnConfigBySn(String sn, String config, Boolean upgradeDevice) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.PUSH_OPENVPN_CONFIG.cmd);
        message.setContent(config);

        String topic = topicService.getPublishDeviceTopic(sn);
        if (null != upgradeDevice && upgradeDevice) {
            mqttClientManager.publish(topicService.getPublishUpgradeDeviceTopic(sn), JSON.toJSONString(message));
        } else {
            mqttClientManager.publish(topic, JSON.toJSONString(message));
        }
        log.info("[MqttMessageHandler#pushVpnConfigBySn] VPN config dispatch, sn: {}", sn);
    }

    public void pushVpnConfigByUsername(String username, String config) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.PUSH_OPENVPN_CONFIG.cmd);
        message.setContent(config);
        String topic = topicService.getPublishUserTopic(username);
        mqttClientManager.publish(topic, JSON.toJSONString(message));
        log.info("[MqttMessageHandler#pushVpnConfigByUsername] VPN config dispatch, username: {}", username);
    }

    /**
     * 下发Headscale PreAuth Key到设备
     *
     * @param topic MQTT主题
     * @param preAuthKey Headscale预授权密钥
     * @param sn 设备序列号
     */
    public void sendHeadscalePreAuthKey(String topic, String preAuthKey, String sn) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.PUSH_HEADSCALE_PREAUTHKEY_RESULT.cmd);
        message.setContent(preAuthKey);
        message.setSn(sn);

        mqttClientManager.publish(topic, JSON.toJSONString(message));

        log.info("[MqttMessageHandler#sendHeadscalePreAuthKey] Headscale PreAuth Key sent to device, sn: {}, topic: {}",
                sn, topic);
    }

    /**
     * 下发Headscale路由设置结果到设备
     *
     * @param topic MQTT主题
     * @param router 路由信息（IP范围/CIDR）
     * @param result 结果（0=成功，1=失败）
     * @param sn 设备序列号
     */
    public void sendHeadscaleRouterResponse(String topic, String router, int result, String sn) {
        IotMessage message = new IotMessage();
        message.setCmd(MessageCmd.ENABLE_HEADSCALE_ROUTER_RESULT.cmd);
        message.setRouter(router);
        message.setResult(result);
        message.setSn(sn);

        mqttClientManager.publish(topic, JSON.toJSONString(message));

        log.info("[MqttMessageHandler#sendHeadscaleRouterResponse] Headscale router response sent to device, sn: {}, router: {}, result: {}, topic: {}",
                sn, router, result, topic);
    }
}
