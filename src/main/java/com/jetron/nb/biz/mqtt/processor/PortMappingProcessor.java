package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.Constants;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsPortMapping;
import com.jetron.nb.dal.po.AcsPortMappingDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 网络监控配置订阅
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.PORT_MAPPING_UP, describ = "网络监控配置订阅")
public class PortMappingProcessor implements MessageProcessor {

    @Autowired
    AcsUserDeviceService userDeviceService;

    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();
        Integer code = message.getCode();
        String type = message.getType();
        List<String> displayName = message.getDisplayName();
        if (0 != code) {
            // 如果失败.则什么也不做，退出
            return;
        }
        if ("add".equals(type)) {
            AcsPortMapping param = new AcsPortMapping();
            param.setNameList(displayName);
            List<AcsPortMapping> portMappingList = userDeviceService.findPortMappingList(param);
            Date now = new Date();
            List<AcsPortMappingDevice> addList = new ArrayList<>();
            for (int i = 0; i < portMappingList.size(); i++) {
                AcsPortMapping acsPortMapping = portMappingList.get(i);
                AcsPortMappingDevice portMappingDevice = JSONObject.parseObject(JSONObject.toJSONString(acsPortMapping), AcsPortMappingDevice.class);
                portMappingDevice.setSn(sn)
                        .setId(IdWorker.getIdStr())
                        .setGmtCreate(now);
                addList.add(portMappingDevice);
            }
            userDeviceService.insertPortMappingDeviceList(addList);
            // 如果是增加
        } else if ("del".equals(type)) {
            // 如果是删除
            userDeviceService.delPortMappingDeviceList(sn,displayName);
        } else if (Constants.NAT_TYPE_GET.equals(type)) {
            // 更新数据操作
            JSONArray portMap = message.getPortMap();
            // 封装映射数据
            ArrayList<AcsPortMappingDevice> portMappingDevices = new ArrayList<>();
            for (int i = 0; i < portMap.size(); i++) {
                // 获取网关返回的端口映射
                JSONObject jsonObject = portMap.getJSONObject(i);
                // 封装数据
                AcsPortMappingDevice acsPortMappingDevice = convertPortMappingData(jsonObject, sn);
                portMappingDevices.add(acsPortMappingDevice);
            }
            // 更新库的数据
            userDeviceService.updatePortMappingDeviceList(sn, portMappingDevices);
        }
    }

    // 封装端口映射数据
    private AcsPortMappingDevice convertPortMappingData(JSONObject jsonObject, String sn) {
        // 获取网关返回的端口映射
        Integer enable = (Integer) jsonObject.get("enable");
        Integer gatewayPort = (Integer) jsonObject.get("srcPort");
        String devIp = (String) jsonObject.get("destIP");
        Integer devPort = (Integer) jsonObject.get("destPort");
        String name = (String) jsonObject.get("displayName");
        JSONArray protocolArray = (JSONArray) jsonObject.get("protocol");
        // 封装数据
        AcsPortMappingDevice acsPortMapping = new AcsPortMappingDevice();
        String protocol = protocolArray.stream()
                .map(Object::toString)
                .reduce((s1, s2) -> s1 + "," + s2)
                .orElse("");
        acsPortMapping.setProtocol(protocol);
        acsPortMapping.setSn(sn);
        acsPortMapping.setStatus(enable);
        acsPortMapping.setDevPort(devPort);
        acsPortMapping.setDevIp(devIp);
        acsPortMapping.setGatewayPort(gatewayPort);
        acsPortMapping.setName(name);
        // 返回处理数据
        return acsPortMapping;
    }

}
