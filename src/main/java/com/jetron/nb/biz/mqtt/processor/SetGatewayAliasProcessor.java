package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

/**
 * Bremote设备网关别名
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.SET_GATEWAY_ALIAS, describ = "Bremote设备网关别名")
public class SetGatewayAliasProcessor implements MessageProcessor {

    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsTopicService acsTopicService;
    @Autowired
    private MqttClientManager mqttClientManager;

    @Override
    public void process(IotMessage message) {
        log.info("Bremote设备网关别名, username:{}", message.getSn());
        String sn = message.getSn();
        String username = message.getUsername();
        AcsUserDevice  userDevice = acsUserDeviceService.getBySn(sn);
        if (userDevice != null && !StringUtils.isEmpty(username)) {
            // 修改别名
            userDevice.setAlias(message.getAlias());
            userDevice.setPosition(message.getLocation());
            int update = acsUserDeviceService.updateByPrimaryKey(userDevice);
            // 向网关推送
            JSONObject paramJson = new JSONObject();
            paramJson.put("cmd", MessageCmd.DEVICE_INFO_ALIAS.cmd);
            paramJson.put("alias", message.getAlias());
            paramJson.put("location", message.getLocation());
            acsUserDeviceService.sendMqtt(userDevice.getSn(),paramJson);
            // 封装上行数据
            JSONObject content = new JSONObject();
            content.put("cmd", MessageCmd.SET_GATEWAY_ALIAS_RESULT.cmd);
            content.put("code", update > 0 ? 0 : 1);
            String topic = acsTopicService.getPublishUserTopic(username);
            mqttClientManager.publish(topic, content.toString());
            log.info("Bremote设备网关别名完成, username:{}, content:{}", message.getSn(), content);
        }
    }

}
