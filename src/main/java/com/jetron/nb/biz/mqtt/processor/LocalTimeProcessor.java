package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.common.constant.MessageCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 获取服务器时间配置
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.GET_TIME, describ = "获取服务器时间")
public class LocalTimeProcessor implements MessageProcessor {
    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    @Override
    public void process(IotMessage message) {
        JSONObject mqttMsg = new JSONObject();
        mqttMsg.put("cmd", MessageCmd.PUSH_TIME.cmd);
        mqttMsg.put("ts", Long.toString(System.currentTimeMillis()));
        mqttMessageHandler.sendMqtt(message.getSn(), mqttMsg, message.getUpgradeTopic());
    }

}
