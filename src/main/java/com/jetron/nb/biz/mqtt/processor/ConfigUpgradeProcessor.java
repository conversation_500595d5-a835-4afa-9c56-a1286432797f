package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsConfigUpgradeService;
import com.jetron.nb.biz.service.AcsLogService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.ConfigUpgradeStatus;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsConfigUpgrade;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.DEVICE_CONFIG_UPGRADE_RESULT, describ = "设备配置结果")
public class ConfigUpgradeProcessor implements MessageProcessor {
    @Autowired
    private AcsConfigUpgradeService acsConfigUpgradeService;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsLogService acsLogService;

    @Override
    @Transactional
    public void process(IotMessage message) {
        try {
            if (message.getResult() == null) {
                log.warn("[ConfigUpgradeProcessor#process] invalid message, result is null, message: {}", JSONObject.toJSONString(message));
                return;
            }

            AcsConfigUpgrade latestUpgrade = acsConfigUpgradeService.getLatestUpgrade(message.getSn());
            if (latestUpgrade == null) {
                log.error("[ConfigUpgradeProcessor#process] no upgrade is found, message: {}", JSONObject.toJSONString(message));
                return;
            }

            AcsConfigUpgrade updateUpgrade = new AcsConfigUpgrade();
            updateUpgrade.setId(latestUpgrade.getId());
            updateUpgrade.setStatus(message.getResult());
            acsConfigUpgradeService.updateByPrimaryKeySelective(updateUpgrade);

            // 更新设备更新状态
            AcsUserDevice userDevice = acsUserDeviceService.selectByPrimaryKey(latestUpgrade.getDeviceId());
            AcsUserDevice updateDevice = new AcsUserDevice();
            updateDevice.setId(userDevice.getId());
            updateDevice.setUpgradeStatus(0);
            acsUserDeviceService.updateByPrimaryKeySelective(updateDevice);

            // 更新日志记录
            acsLogService.appendLog(userDevice, LogType.CONFIG_UPGRADE, ConfigUpgradeStatus.getByStatus(message.getResult()).describ);
            log.info("[ConfigUpgradeProcessor#process] success, message: {}",message.getId());
        } catch (Exception e) {
            log.error("[ConfigUpgradeProcessor#process] error, {}, {}", JSONObject.toJSONString(message), e);
        }
    }
}
