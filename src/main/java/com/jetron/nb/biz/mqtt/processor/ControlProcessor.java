package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *  对网关的控制指令发出器
 */
@Slf4j
@Component
public class ControlProcessor {
    @Autowired
    private MqttClientManager mqttClientManager;
    @Autowired
    private AcsTopicService acsTopicService;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;

    public void publish(String sn, String content) {
        AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
        boolean upgrade =
                null != acsUserDevice && null != acsUserDevice.getUpgradeDevice() && acsUserDevice.getUpgradeDevice();
        String topic = acsTopicService.getPublishDeviceTopic(sn);
        if (upgrade) {
            mqttClientManager.publish(acsTopicService.getPublishUpgradeDeviceTopic(sn), content);
        } else {
            mqttClientManager.publish(topic, content);
        }

    }
}
