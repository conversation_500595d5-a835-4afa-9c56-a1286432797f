package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsVpnConfigService;
import com.jetron.nb.common.constant.MessageCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.VPN_CONFIG_REQUEST, describ = "VPN配置请求&设备信息")
public class VpnConfigProcessor implements MessageProcessor {

    @Autowired
    private AcsVpnConfigService acsVpnConfigService;

    @Override
    public void process(IotMessage message) {
        try {
            if (Objects.nonNull(message.getSn())) {
                acsVpnConfigService.dispatchConfigBySn(message.getSn(), message.getUpgradeTopic());

                log.info("[VpnConfigProcessor#process] process vpn config request success, message ID: {}, message: {}", message.getId(), JSONObject.toJSONString(message));
            } else if (Objects.nonNull(message.getUsername())) {
                acsVpnConfigService.dispatchConfigAndDeviceInfoByUserName(message.getUsername());

                log.info("[VpnConfigProcessor#process] process vpn config request success, userId: {}, message ID: {}, message: {}", message.getUsername(), message.getId(), JSONObject.toJSONString(message));
            }
        } catch (Exception e) {
            log.error("[VpnConfigProcessor#process] process error, message ID: {}, error info: {}, message: {}", message.getId(), e.toString(), JSONObject.toJSONString(message));
        }
    }
}
