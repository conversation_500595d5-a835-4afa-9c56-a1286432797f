package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.AcsFirmwareUpgradeService;
import com.jetron.nb.biz.service.AcsLogService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.ConfigUpgradeStatus;
import com.jetron.nb.common.constant.FirmwareUpgradeStatus;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsFirmwareUpgrade;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.FIRMWARE_UPGRADE_RESULT, describ = "升级结果处理")
public class FirmwareUpgradeProcessor implements MessageProcessor {

    @Autowired
    private AcsFirmwareUpgradeService acsFirmwareUpgradeService;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsLogService acsLogService;

    @Override
    @Transactional
    public void process(IotMessage message) {
        String sn = message.getSn();
        Integer result = message.getResult();
        if (StringUtils.isBlank(sn) || result == null) {
            log.warn("[UpgradeResultProcessor#process] invalid message, sn is null or result is null, message: {}", JSONObject.toJSONString(message));
            return;
        }
        try {
            AcsFirmwareUpgrade latestUpgrade = acsFirmwareUpgradeService.getLatestUpgrade(sn);
            if (latestUpgrade == null) {
                log.error("[UpgradeResultProcessor#process] no firmwareUpgrade is found, message: {}", JSONObject.toJSONString(message));
                return;
            }


            AcsFirmwareUpgrade updateUpgrade = new AcsFirmwareUpgrade();
            updateUpgrade.setId(latestUpgrade.getId());
            updateUpgrade.setStatus(result);
            acsFirmwareUpgradeService.updateByPrimaryKeySelective(updateUpgrade);

            AcsUserDevice userDevice = acsUserDeviceService.selectByPrimaryKey(latestUpgrade.getDeviceId());
            AcsUserDevice updateDevice = new AcsUserDevice();
            updateDevice.setId(userDevice.getId());
            updateDevice.setUpgradeStatus(0);
            acsUserDeviceService.updateByPrimaryKeySelective(updateDevice);

            // 更新日志记录
            acsLogService.appendLog(userDevice, LogType.FIRMWARE_UPGRADE, FirmwareUpgradeStatus.getByStatus(result).describ);

            log.info("[UpgradeResultProcessor#process] update firmwareUpgrade success, id: {}, message: {}", latestUpgrade.getId(), sn, message.getId());
        } catch (Exception e) {
            log.error("[UpgradeResultProcessor#process] update firmwareUpgrade error, message ID: {}, error info: {}", JSONObject.toJSONString(message), e.toString());
        }

    }
}
