package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.service.*;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.dao.AcsFirmwareUpgradeMapper;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.DEVICE_INFO_UPLOAD, describ = "设备信息")
public class DeviceInfoProcessor implements MessageProcessor {

    @Autowired
    private AcsUserDeviceService acsUserDeviceService;

    @Autowired
    private AcsFirmwareUpgradeMapper firmwareUpgradeMapper;
    @Autowired
    private AcsFirmwareUpgradeService firmwareUpgradeService;

    @Autowired
    private AcsFirmwareService firmwareService;

    @Autowired
    private AcsParameterConfigService acsParameterConfigService;

    @Autowired
    private AcsAlarmService acsAlarmService;


    @Override
    @Transactional
    public void process(IotMessage message) {
        try {
            AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(message.getSn());
            if (acsUserDevice == null) {
                log.error("[DeviceInfoProcessor#process] device not found, message: {}", message.getId());
                return;
            }
            if (!StringUtils.equals(acsUserDevice.getTrueIp(),message.getTrueIp())) {
                List<AcsParameterConfig> acsParameterConfigs = acsParameterConfigService.findBySn(message.getSn());
                if (acsParameterConfigs != null) {
                    List<AcsParameterConfig> list = acsParameterConfigs.stream().filter(x -> "ip".equalsIgnoreCase(x.getParameterCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(list)) {
                        AcsParameterConfig config = list.get(0);
                        // 如果不一致，则报警
                        AcsCaptureAlarm acsCaptureAlarm = new AcsCaptureAlarm();
                        acsCaptureAlarm.setSn(message.getSn());
                        acsCaptureAlarm.setInsertTime(new Date());
                        acsCaptureAlarm.setParameterCode(config.getParameterCode());
                        acsCaptureAlarm.setCompany(config.getCompany());
                        acsCaptureAlarm.setParameterKey(config.getParameterKey());
                        acsCaptureAlarm.setParameterContent(config.getParameterContent());
                        acsCaptureAlarm.setCurrentValue(message.getTrueIp());
                        acsCaptureAlarm.setParameterValue(acsUserDevice.getTrueIp());
                        acsCaptureAlarm.setIp(message.getTrueIp());
                        acsAlarmService.insertList(Arrays.asList(acsCaptureAlarm));
                    }
                }
            }
            if (StringUtils.isNotBlank(message.getUptime()) && message.getUptime().contains(".")) {
                double uptimeD = Double.parseDouble(message.getUptime());
                int uptimeI = (int) uptimeD;
                message.setUptime(String.valueOf(uptimeI));
            }
            AcsUserDevice updateDevice = JSONObject.parseObject(JSONObject.toJSONString(message),AcsUserDevice.class);
            updateDevice.setId(acsUserDevice.getId());
            updateDevice.setPosition(message.getLocation());
            updateDevice.setVillageNum(message.getPci());
            updateDevice.setUpgradeDevice(message.getUpgradeTopic());
            if (message.getBaseDev() != null) {
                updateDevice.setBaseDev(JSON.toJSONString(message.getBaseDev()));
            }
            if (acsUserDevice.getUpgradeStatus() != null && 1 == acsUserDevice.getUpgradeStatus()) {
                // 如果设备在更新中，getUpgradeStatus = 1
                // 并且message 的版本 不等于AcsUserDevice 版本。则视为更新成功，
                // 将getUpgradeStatus 设为0
                AcsFirmwareUpgrade upgrade = firmwareUpgradeMapper.getLatestUpgrade(acsUserDevice.getId());
                if (upgrade != null && upgrade.getStatus() == 0) {
                    AcsFirmware acsFirmware = firmwareService.selectByPrimaryKey(upgrade.getFirmwareId());
                    if (acsFirmware != null && acsFirmware.getVersion().trim().equals(message.getVerNo().trim()) ) {
                        log.warn("[DeviceInfoProcessor#AcsFirmwareUpgrade] success, message: {}", JSONObject.toJSONString(message));
                        upgrade.setStatus(3);
                        updateDevice.setUpgradeStatus(0);
                        firmwareUpgradeService.updateByPrimaryKey(upgrade);
                        // 2022年5月，出现固件升级，设备升级状态不改0的问题。加一行代码看看能不能解决
                        //acsUserDeviceService.setUpgradeStatus(Arrays.asList(updateDevice.getId()),0);
                    }
                }
            }
            acsUserDeviceService.updateByPrimaryKeySelective(updateDevice);

            log.info("[DeviceInfoProcessor#process] success, message: {}", message.getId());
        } catch (Exception e) {
            log.error("[DeviceInfoProcessor#process] error, message ID: {} , error info: {}", message.getId(), e.toString());
        }
    }
}
