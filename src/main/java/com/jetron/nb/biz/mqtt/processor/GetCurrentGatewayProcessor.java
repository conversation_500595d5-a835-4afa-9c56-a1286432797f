package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttClientManager;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.dal.po.AcsUserDevice;
import com.jetron.nb.dal.vo.UserDeviceToBremoteVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Bremote获取当前网关信息
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.GET_CURRENT_GATEWAY, describ = "Bremote获取当前网关信息")
public class GetCurrentGatewayProcessor implements MessageProcessor {

    private ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsTopicService acsTopicService;
    @Autowired
    private MqttClientManager mqttClientManager;

    @Override
    public void process(IotMessage message) {
        log.info("Bremote获取账户下当前网关信息, username:{}", message.getUsername());
        String username = message.getUsername();
        String sn = message.getSn();
        // 查询账户下所有网关信息
        AcsUserDevice userDevice = acsUserDeviceService.getBySn(sn);
        if (null != userDevice) {
            // 转换需要的字段
            UserDeviceToBremoteVo userDeviceToBremoteVo =  getDeviceInfo(userDevice);
            try {
                // 填充数据并下发
                String dataString = objectMapper.writeValueAsString(userDeviceToBremoteVo);
                JSONObject jsonObject = JSONObject.parseObject(dataString);
                jsonObject.put("cmd", MessageCmd.GET_CURRENT_GATEWAY_RESULT.cmd);
                String topic = acsTopicService.getPublishUserTopic(username);
                mqttClientManager.publish(topic, objectMapper.writeValueAsString(jsonObject));

            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            log.info("Bremote获取账户下当前网关信息, result: {}", userDeviceToBremoteVo);
        }
    }

    private UserDeviceToBremoteVo getDeviceInfo(AcsUserDevice userDevice) {
        return new UserDeviceToBremoteVo(userDevice);
    }
}
