package com.jetron.nb.biz.mqtt.processor;

import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.AcsParameterConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * NTP服务器配置
 */
@Slf4j
@MessageProcessorType(cmd = MessageCmd.NTP_SERVER_S, describ = "NTP服务器配置")
public class NTPProcessor implements MessageProcessor {

    @Autowired
    RedisServiceImpl redisService;

    @Override
    public void process(IotMessage message) {
        String sn = message.getSn();
        String ntpServer = message.getNtpServer();
        String interval = message.getInterval();
        JSONObject json = new JSONObject();
        json.put("ntpServer",ntpServer);
        json.put("interval",interval);
        redisService.set(getRedisKey(sn),json.toJSONString());
    }

    public static String getRedisKey(String sn) {
        return sn + "_" + "ntpServer";
    }

}
