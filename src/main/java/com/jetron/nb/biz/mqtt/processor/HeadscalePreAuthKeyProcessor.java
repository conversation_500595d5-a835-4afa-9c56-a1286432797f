package com.jetron.nb.biz.mqtt.processor;

import com.jetron.nb.biz.mqtt.IotMessage;
import com.jetron.nb.biz.mqtt.MessageProcessor;
import com.jetron.nb.biz.mqtt.MessageProcessorType;
import com.jetron.nb.biz.mqtt.MqttMessageHandler;
import com.jetron.nb.biz.service.AcsTopicService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

import java.util.Arrays;

@Slf4j
@MessageProcessorType(cmd = MessageCmd.GET_HEADSCALE_PREAUTHKEY, describ = "获取Headscale预授权密钥")
public class HeadscalePreAuthKeyProcessor implements MessageProcessor {

    private static final Long SUCCESS = 1L;
    private static final String PREAUTH_KEY_LOCK_PREFIX = "preauth_key_lock:";
    private static final int LOCK_EXPIRE_TIME = 30; // 锁过期时间30秒
    private static final String LOCK_VALUE = "1"; // 锁的值

    @Autowired
    private AcsUserDeviceService acsUserDeviceService;

    @Autowired
    private AcsUserService acsUserService;

    @Autowired
    private RedisServiceImpl redisService;

    @Autowired
    private AcsTopicService acsTopicService;

    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void process(IotMessage message) {
        try {
            // 获取设备SN号
            String sn = message.getSn();
            if (StringUtils.isBlank(sn)) {
                log.warn("[HeadscalePreAuthKeyProcessor] SN is blank, ignoring message");
                return;
            }

            log.info("[HeadscalePreAuthKeyProcessor] Processing Headscale PreAuth Key request for SN: {}", sn);

            // 通过SN号查找设备信息
            AcsUserDevice acsUserDevice = acsUserDeviceService.getBySn(sn);
            if (acsUserDevice == null) {
                log.warn("[HeadscalePreAuthKeyProcessor] Device not found for SN: {}", sn);
                return;
            }

            // 通过设备信息查找用户信息
            AcsUser acsUser = acsUserService.selectByPrimaryKey(acsUserDevice.getBelongTo());
            if (acsUser == null) {
                log.warn("[HeadscalePreAuthKeyProcessor] User not found for device SN: {}", sn);
                return;
            }

            // 获取用户名
            String username = acsUser.getName();
            if (StringUtils.isBlank(username)) {
                log.warn("[HeadscalePreAuthKeyProcessor] Username is blank for device SN: {}", sn);
                return;
            }

            log.info("[HeadscalePreAuthKeyProcessor] Found user: {} for device SN: {}", username, sn);

            // 构建Redis key
            String redisKey = username + "-headscale";

            // 从Redis中查找PreAuth Key
            String preAuthKey = redisService.get(redisKey);

            if (StringUtils.isNotBlank(preAuthKey)) {
                // Redis中存在PreAuth Key，直接下发
                log.info("[HeadscalePreAuthKeyProcessor] Found existing PreAuth Key in Redis for user: {}", username);
                sendPreAuthKeyToDevice(sn, preAuthKey);
            } else {
                // Redis中不存在PreAuth Key，需要创建新的
                log.info("[HeadscalePreAuthKeyProcessor] PreAuth Key not found in Redis, creating new one for user: {}", username);

                // 使用分布式锁防止并发创建
                String lockKey = PREAUTH_KEY_LOCK_PREFIX + username;
                boolean lockAcquired = false;

                try {
                    // 尝试获取分布式锁
                    lockAcquired = getLock(lockKey, LOCK_VALUE, LOCK_EXPIRE_TIME);

                    if (lockAcquired) {
                        log.info("[HeadscalePreAuthKeyProcessor] Acquired lock for user: {}", username);

                        // 双重检查：获取锁后再次检查Redis中是否已有PreAuth Key
                        preAuthKey = redisService.get(redisKey);

                        if (StringUtils.isNotBlank(preAuthKey)) {
                            // 其他实例已经创建了PreAuth Key，直接使用
                            log.info("[HeadscalePreAuthKeyProcessor] PreAuth Key was created by another instance for user: {}", username);
                            sendPreAuthKeyToDevice(sn, preAuthKey);
                        } else {
                            // 确实需要创建新的PreAuth Key
                            log.info("[HeadscalePreAuthKeyProcessor] Creating new PreAuth Key for user: {}", username);

                            // 调用AcsUserService创建PreAuth Key
                            acsUserService.createAndStorePreAuthKey(username);

                            // 重新从Redis获取创建的PreAuth Key
                            preAuthKey = redisService.get(redisKey);

                            if (StringUtils.isNotBlank(preAuthKey)) {
                                log.info("[HeadscalePreAuthKeyProcessor] Successfully created and retrieved PreAuth Key for user: {}", username);
                                sendPreAuthKeyToDevice(sn, preAuthKey);
                            } else {
                                log.error("[HeadscalePreAuthKeyProcessor] Failed to create or retrieve PreAuth Key for user: {}", username);
                            }
                        }
                    } else {
                        // 获取锁失败，可能其他实例正在创建，等待一段时间后重试
                        log.warn("[HeadscalePreAuthKeyProcessor] Failed to acquire lock for user: {}, waiting and retrying...", username);

                        // 等待一段时间后重试获取PreAuth Key
                        Thread.sleep(1000); // 等待1秒
                        preAuthKey = redisService.get(redisKey);

                        if (StringUtils.isNotBlank(preAuthKey)) {
                            log.info("[HeadscalePreAuthKeyProcessor] PreAuth Key found after waiting for user: {}", username);
                            sendPreAuthKeyToDevice(sn, preAuthKey);
                        } else {
                            log.error("[HeadscalePreAuthKeyProcessor] PreAuth Key still not available after waiting for user: {}", username);
                        }
                    }
                } catch (InterruptedException e) {
                    log.error("[HeadscalePreAuthKeyProcessor] Thread interrupted while waiting for user: {}", username, e);
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("[HeadscalePreAuthKeyProcessor] Error during PreAuth Key creation for user: {}", username, e);
                } finally {
                    // 确保释放锁
                    if (lockAcquired) {
                        releaseLock(lockKey, LOCK_VALUE);
                        log.info("[HeadscalePreAuthKeyProcessor] Released lock for user: {}", username);
                    }
                }
            }

        } catch (Exception e) {
            log.error("[HeadscalePreAuthKeyProcessor] Error processing Headscale PreAuth Key request", e);
        }
    }

    /**
     * 向设备下发PreAuth Key
     */
    private void sendPreAuthKeyToDevice(String sn, String preAuthKey) {
        try {
            // 获取MQTT下发主题
            String upgradeDeviceTopic = acsTopicService.getPublishUpgradeDeviceTopic(sn);

            // 调用MqttMessageHandler下发PreAuth Key
            mqttMessageHandler.sendHeadscalePreAuthKey(upgradeDeviceTopic, preAuthKey, sn);

            log.info("[HeadscalePreAuthKeyProcessor] Successfully sent PreAuth Key to device SN: {}", sn);

        } catch (Exception e) {
            log.error("[HeadscalePreAuthKeyProcessor] Failed to send PreAuth Key to device SN: {}", sn, e);
        }
    }

    /**
     * 获取分布式锁
     * 参考 ScheduleTask.getLock 方法实现
     *
     * @param key 锁的key
     * @param value 锁的值
     * @param expireTime 锁的过期时间（秒）
     * @return 是否获取成功
     */
    private boolean getLock(String key, String value, int expireTime) {
        try {
            String evalScript = new StringBuilder()
                    .append(" if redis.call('setnx', KEYS[1], ARGV[1])==1 then")
                    .append("   if redis.call('get', KEYS[1])==ARGV[1] then")
                    .append("       return redis.call('expire', KEYS[1], ARGV[2])")
                    .append("   else return 0")
                    .append("   end")
                    .append(" else return 0")
                    .append(" end").toString();
            RedisScript<Long> redisScript = new DefaultRedisScript<>(evalScript, Long.class);
            Object result = redisTemplate.execute(redisScript, Arrays.asList(key), String.valueOf(value), String.valueOf(expireTime));
            if (SUCCESS.equals(result)) {
                return true;
            } else {
                log.debug("[HeadscalePreAuthKeyProcessor] Failed to acquire lock, key: {}", key);
                return false;
            }
        } catch (Exception e) {
            log.error("[HeadscalePreAuthKeyProcessor] Error acquiring lock, key: {}", key, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     * 参考 ScheduleTask.releaseLock 方法实现
     *
     * @param key 锁的key
     * @param value 锁的值
     * @return 是否释放成功
     */
    private boolean releaseLock(String key, String value) {
        try {
            String evalScript = new StringBuilder()
                    .append("  if redis.call('get', KEYS[1]) == ARGV[1] then ")
                    .append("    return redis.call('del', KEYS[1])")
                    .append("  else return 0 ")
                    .append("  end").toString();
            RedisScript<Long> redisScript = new DefaultRedisScript<>(evalScript, Long.class);
            Object result = redisTemplate.execute(redisScript, Arrays.asList(key), value);
            if (SUCCESS.equals(result)) {
                return true;
            } else {
                log.warn("[HeadscalePreAuthKeyProcessor] Failed to release lock or lock not owned, key: {}", key);
                return false;
            }
        } catch (Exception e) {
            log.error("[HeadscalePreAuthKeyProcessor] Error releasing lock, key: {}", key, e);
            return false;
        }
    }
}
