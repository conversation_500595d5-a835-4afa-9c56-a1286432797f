package com.jetron.nb.web.common.converter;

import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MessageConvertorConfig {

    @Bean
    public HttpMessageConverters customConverters() {
        FormHttpMessageConverter formHttpMessageConverter = new FormHttpMessageConverter();
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        return new HttpMessageConverters(fastJsonHttpMessageConverter, formHttpMessageConverter);
    }
}
