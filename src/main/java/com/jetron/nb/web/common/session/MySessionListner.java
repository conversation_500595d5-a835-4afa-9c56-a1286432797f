package com.jetron.nb.web.common.session;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.dal.dao.AcsUserMapper;
import com.jetron.nb.dal.po.AcsUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;
import java.util.Date;

@Slf4j
@Component
public class MySessionListner implements HttpSessionListener {

    @Autowired
    private AcsUserMapper acsUserMapper;

    @Override
    public void sessionCreated(HttpSessionEvent se) {
        log.info("new session created: {}", se.getSession().getId());
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        HttpSession session = se.getSession();
        String userInfo = (String) session.getAttribute("user_info");
        if (StringUtils.isNotBlank(userInfo)) {
            AcsUser user = JSON.parseObject(userInfo, AcsUser.class);

            AcsUser updateUser = new AcsUser();
            updateUser.setLastLogoutTime(new Date());
            updateUser.setId(user.getId());
            acsUserMapper.updateByPrimaryKeySelective(updateUser);

            log.info("{} session过期, sessionId: {}", user.getName(), session.getId());
        }
    }
}
