package com.jetron.nb.web.common.filter;

import com.jetron.nb.web.common.util.ThreadContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *
 * Date: 2020-06-17
 * Version: 1.0.0
 */
@Slf4j
@Configuration
@WebFilter(urlPatterns = {"/*"}, filterName = "IotHttpFilter")
public class IotHttpFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain) throws ServletException, IOException {
        log.debug("do filter, threadID={}, path={}, method={}, sessionId={}",
                Thread.currentThread().getId(), request.getServletPath(), request.getMethod(), request.getSession().getId());

        ThreadContextUtils.setRemoteIp(request);
        filterChain.doFilter(request, response);
        ThreadContextUtils.remove();
    }
}
