package com.jetron.nb.web.common.util;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * Date: 2020-06-17
 * Version: 1.0.0
 */
public class ThreadContextUtils {
    private static ThreadLocal<ThreadContext> threadLocal;

    static {
        threadLocal = new ThreadLocal() {
            @Override
            protected Object initialValue() {
                return new ThreadContext();
            }
        };
    }

    public static void setRemoteIp(HttpServletRequest request) {

        String remoteIp = request.getRemoteHost();
        String ips = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(ips)) {
            remoteIp = ips.split(",")[0];
        }

        threadLocal.get().setRemoteIp(remoteIp);
    }

    public static String getRemoteIp() {
        return threadLocal.get().getRemoteIp();
    }

    public static void remove() {
        threadLocal.remove();
    }
}
