package com.jetron.nb.web.common.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

public class FormHttpMessageConverter implements HttpMessageConverter<Object> {

    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private final static Logger logger = LoggerFactory.getLogger(FormHttpMessageConverter.class);
    private final List<Charset> availableCharsets = new ArrayList<Charset>(Charset.availableCharsets().values());
    private List<MediaType> supportedMediaTypes = Arrays.asList(MediaType.APPLICATION_FORM_URLENCODED);
    private boolean writeAcceptCharset = true;

    @Override
    public boolean canRead(Class clazz, MediaType mediaType) {
        if (mediaType == null) {
            return true;
        }
        for (MediaType supportedMediaType : getSupportedMediaTypes()) {
            if (supportedMediaType.includes(mediaType)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean canWrite(Class clazz, MediaType mediaType) {
        if (mediaType == null || MediaType.ALL.equals(mediaType)) {
            return true;
        }
        for (MediaType supportedMediaType : getSupportedMediaTypes()) {
            if (supportedMediaType.includes(mediaType)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<MediaType> getSupportedMediaTypes() {
        return supportedMediaTypes;
    }

    @Override
    public Object read(Class clazz, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        if (clazz != null) {
            Charset charset = getContentTypeCharset(inputMessage.getHeaders().getContentType());
            String body = IOUtils.toString(inputMessage.getBody(), charset);
            try {
                if (StringUtils.isNotBlank(body)) {
                    String[] pairs = org.springframework.util.StringUtils.tokenizeToStringArray(body, "&");
                    Map<String, Object> map = new HashMap<>(pairs.length);
                    String[] var8 = pairs;
                    int var9 = pairs.length;

                    for (int var10 = 0; var10 < var9; ++var10) {
                        String pair = var8[var10];
                        int idx = pair.indexOf(61);
                        if (idx == -1) {
                            String name = URLDecoder.decode(pair, charset.name());
                            map.put(StringUtils.trim(name), null);
                        } else {
                            String name = URLDecoder.decode(pair.substring(0, idx), charset.name());
                            String value = URLDecoder.decode(pair.substring(idx + 1), charset.name());
                            map.put(StringUtils.trim(name), StringUtils.trim(value));
                        }
                    }

                    return JSON.parseObject(JSON.toJSONString(map, SerializerFeature.WriteMapNullValue), clazz);
                }

                return clazz.newInstance();
            } catch (Exception e) {
                logger.error("<EMAIL>", e);
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public void write(Object o, MediaType contentType, HttpOutputMessage outputMessage)
                    throws IOException, HttpMessageNotWritableException {
        if (o instanceof MultiValueMap) {
            writeForm((MultiValueMap<String, String>) o, contentType, outputMessage);
        }
    }

    private Charset getContentTypeCharset(MediaType contentType) {
        if ((contentType != null) && (contentType.getCharset() != null)) {
            return contentType.getCharset();
        }
        return DEFAULT_CHARSET;
    }

    private void writeForm(MultiValueMap<String, String> form, MediaType contentType, HttpOutputMessage outputMessage)
                    throws IOException {
        Charset charset;
        if (contentType != null) {
            outputMessage.getHeaders().setContentType(contentType);
            charset = contentType.getCharset() != null ? contentType.getCharset() : DEFAULT_CHARSET;
        } else {
            outputMessage.getHeaders().setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            charset = DEFAULT_CHARSET;
        }

        StringBuilder builder = new StringBuilder();
        Iterator bytes = form.keySet().iterator();

        while (bytes.hasNext()) {
            String name = (String) bytes.next();
            Iterator valueIterator = ((List) form.get(name)).iterator();

            while (valueIterator.hasNext()) {
                String value = (String) valueIterator.next();
                builder.append(URLEncoder.encode(name, charset.name()));
                if (value != null) {
                    builder.append('=');
                    builder.append(URLEncoder.encode(value, charset.name()));
                    if (valueIterator.hasNext()) {
                        builder.append('&');
                    }
                }
            }

            if (bytes.hasNext()) {
                builder.append('&');
            }
        }

        byte[] bytes1 = builder.toString().getBytes(charset.name());
        outputMessage.getHeaders().setContentLength((long) bytes1.length);
        StreamUtils.copy(bytes1, outputMessage.getBody());
    }
}