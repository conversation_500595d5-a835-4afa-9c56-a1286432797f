package com.jetron.nb.web.common.filter;

import com.jetron.nb.biz.proxy.IotProxy;
import com.jetron.nb.common.util.MdcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

@Slf4j
@WebFilter(filterName = "sessionFilter",
        urlPatterns = {"/account/*", "/config/*", "/device/*", "/firmware/*", "/log/*", "/sysconfig/*", "/cmd/*",
                "/parm/*", "/dictionary/*", "/alarm/*", "/userStatistics/*", "/remote/*","/deviceLog/*","/companyDict/*",
                "/about", "/table/*","/driver/*","/headscale/*"})
public class SessionFilter extends OncePerRequestFilter {
    private static final String USER_INFO = "user_info";
    private String[] whiteList = new String[]{"/", "/ok.htm", "/login.html", "/account/login",
            "/account/invalidSession", "/firmware/download", "/config/download", "/device/activate", "/device/logUpload",
            "/device/flow/flowStatistics", "/device/flow/compatureDataStatistics", "/device/certDownload",
            "/device/activate/v1","/device/testrecord"
            ,"/kanban.html","/device/deviceModel","/device/flow/flowStatisticsOfDashboard","/device/onlineClass"
            ,"/alarm/kanban","/device/flow/flowEarlyWarn","/device/flow/save/kanbanTitle","/device/flow/query/kanbanTitle"
            };

    @Autowired
    private IotProxy iotProxy;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 转发设备url
        String host = request.getHeader("Host");
        if (host.matches("^[A-Za-z0-9]{32}\\.")) {
            iotProxy.visit(request, response);
        } else {
            String url = request.getRequestURI();
            if (isNotInWhiteList(request.getRequestURI())) {
                HttpSession session = request.getSession();
                String userInfo = (String) session.getAttribute(USER_INFO);
                if (userInfo == null) {
                    log.info("[SessionFilter#doFilterInternal] user info is null, url: {}", url);
                    if (url.endsWith("index.html")) {
                        response.sendRedirect(request.getContextPath() + "/login.html");
                    } else {
                        response.sendRedirect("/account/invalidSession");
                    }
                } else {
                    MdcUtils.putCurrentAcsUser(userInfo);
                    filterChain.doFilter(request, response);
                    MdcUtils.clear();
                }
            } else {
                filterChain.doFilter(request, response);
            }
        }
    }

    private boolean isNotInWhiteList(String uri) {
        return !uri.matches("[\\s\\S]*(\\.css|\\.js|\\.img|\\.png|\\.jpg|\\.woff|\\.woff2|\\.tff|\\.icon)$") &&
                !uri.endsWith(".css") && !uri.endsWith(".js")
                && !ArrayUtils.contains(whiteList, uri);
    }
}
