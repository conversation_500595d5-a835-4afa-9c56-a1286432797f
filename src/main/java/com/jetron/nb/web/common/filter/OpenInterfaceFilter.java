package com.jetron.nb.web.common.filter;


import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.service.OpenInterfaceService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.RequestUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.OpenInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@WebFilter(filterName = "openInterfaceFilter" ,
        urlPatterns = {"/device/flow/flowStatistics","/device/flow/compatureDataStatistics","/external/*"})
@Slf4j
public class OpenInterfaceFilter extends OncePerRequestFilter {

    private static final String SIGN_KEY = "sign";

    private static final String TIMESTAMP_KEY = "timestamp";

    private static final String ACCESS_KEY = "accessKey";

    private static final String ACCESS_SECRET = "accessSecret";

    private static final int SIGN_EXPIRED_TIME = 5 * 60 * 1000;

    public static Map<String,String> hashMap = null;

    public static Map<String,String> userMap = null;

    @Autowired
    OpenInterfaceService openInterfaceService;



    /**
     * 验证方法介绍。
     * 1. 客户端保存 accessKey 与 accessSecret 。
     * 2. 客户端将入参按照参数名称正序排序，a=1&b=1
     */

    @PostConstruct
    public void init() {
        hashMap =  new ConcurrentHashMap<>();
        userMap = new ConcurrentHashMap<>();
        List<OpenInterface> list = openInterfaceService.findList(null);
        for (int i = 0; i < list.size(); i++) {
            OpenInterface openInterface = list.get(i);
            hashMap.put(openInterface.getAccessKey(),openInterface.getAccessSecret());
            userMap.put(openInterface.getAccessKey(),openInterface.getCompany());
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String header = request.getHeader("content-type");
        JSONObject jsonParam = null;
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        if ("application/json".equals(header)) {
            jsonParam = RequestUtils.getJSONParam(requestWrapper);
        } else {
            jsonParam = RequestUtils.getFormParam(requestWrapper);
        }

        List<OpenInterface> list = openInterfaceService.findList(null);
        for (int i = 0; i < list.size(); i++) {
            OpenInterface openInterface = list.get(i);
            hashMap.putIfAbsent(openInterface.getAccessKey(),openInterface.getAccessSecret());
            userMap.putIfAbsent(openInterface.getAccessKey(),openInterface.getCompany());
        }

        String timestamp = jsonParam.getString(TIMESTAMP_KEY);
        String accessKey = jsonParam.getString(ACCESS_KEY);
        String accessSecret = hashMap.get(accessKey);
        String company = userMap.get(accessKey);

        if (!org.apache.commons.lang3.StringUtils.isNumeric(timestamp) ) {
            invalidParam(request,response);
            return;
        }
        // 检查KEY是否合理
        if (StringUtils.isEmpty(accessKey) || StringUtils.isEmpty(accessSecret)) {
            invalidParam(request,response);
            return;
        }
        Long ts = Long.valueOf(timestamp);
        // 禁止超时签名
        if (System.currentTimeMillis() - ts > SIGN_EXPIRED_TIME) {
            invalidParam(request,response);
            return;
        }
        //签名错误
        if (!verificationSign(jsonParam, accessSecret)) {
             invalidParam(request,response);
            return;
        } else {
            AcsUser user = new AcsUser();
            user.setCompany(company);
            user.setRole(RoleEnum.SUPER_ADMIN.getRole());
            MdcUtils.putCurrentAcsUser(JSONObject.toJSONString(user));
            filterChain.doFilter(requestWrapper, response);
        }

    }

    public void invalidParam(HttpServletRequest request, HttpServletResponse response){
        Result result = Result.toResult(ApiCode.BAD_REQUEST);
        try {
            PrintWriter writer = response.getWriter();
            writer.print(JSONObject.toJSONString(result));
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean verificationSign(JSONObject jsonParam, String accessSecret) throws UnsupportedEncodingException {
        String originSign = jsonParam.getString(SIGN_KEY);
        jsonParam.remove(SIGN_KEY);
        String sign = createSign(jsonParam, accessSecret);
        return sign.equals(originSign);
    }

    private String createSign(JSONObject params, String accessSecret) throws UnsupportedEncodingException {
        Set<String> keysSet = params.keySet();
        Object[] keys = keysSet.toArray();
        Arrays.sort(keys);
        StringBuilder temp = new StringBuilder();
        boolean first = true;
        for (Object key : keys) {
            if (first) {
                first = false;
            } else {
                temp.append("&");
            }
            temp.append(key).append("=");
            Object value = params.get(key);
            String valueString = "";
            if (null != value) {
                valueString = String.valueOf(value);
            }
            temp.append(valueString);
        }
        temp.append("&").append(ACCESS_SECRET).append("=").append(accessSecret);
        return DigestUtils.md5DigestAsHex(temp.toString().getBytes("utf-8")).toUpperCase();
    }
}
