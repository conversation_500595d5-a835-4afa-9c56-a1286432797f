package com.jetron.nb.web.controller.sys;

import com.jetron.nb.biz.service.AcsRegisterService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.vo.Result;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 *  NMS系统注册管理
 */
@RestController
@RequestMapping("/register")
@Slf4j
public class SysRegisterController {

    @Autowired
    private AcsRegisterService acsRegisterService;

    @OperateLog(remark = "'导入注册文件'")
    @RequestMapping(value = "/importLicense", method = RequestMethod.POST)
    public Object importDevices(MultipartFile file) {
        if (file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        return acsRegisterService.importLicense(file) ? Result.success("注册文件导入成功!") : Result.toResult(ApiCode.SERVER_ERROR);
    }
}
