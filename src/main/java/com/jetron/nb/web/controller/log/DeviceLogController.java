package com.jetron.nb.web.controller.log;


import com.jetron.nb.biz.service.DeviceLogService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.vo.Result;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/deviceLog")
@Slf4j
public class DeviceLogController {

    @Data
    @Accessors
    @NoArgsConstructor
    public static class DeviceLogFilter {
        private String sn;
        private String level;
        private String content;
        private String start;
        private String end;
        private String model;
    }

    @Data
    @Accessors
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelError {
        private String model;
        private String error;
        private Integer num;
    }

    @Autowired
    private DeviceLogService deviceLogService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object list(
            DeviceLogFilter param,
            @RequestParam(value = "sn") String sn,
            @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false,defaultValue = "1000") Integer size) {

        Result result = null;
        try {
            param.setSn(sn);
            result = deviceLogService.list(param,page,size);
        } catch (Exception e) {
            log.error("[DeviceLogController#list] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/statistics", method = RequestMethod.POST)
    public Object statistics(
            DeviceLogFilter param,
            @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false,defaultValue = "20") Integer size) {

        Result result = null;
        try {
            result = deviceLogService.statistics(param,page,size);
        } catch (Exception e) {
            log.error("[DeviceLogController#statistics] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }
}
