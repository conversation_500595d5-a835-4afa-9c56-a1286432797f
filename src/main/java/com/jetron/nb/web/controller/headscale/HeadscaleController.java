package com.jetron.nb.web.controller.headscale;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.biz.service.HeadscaleService;
import com.jetron.nb.biz.service.HeadscaleHybridService;
import com.jetron.nb.biz.service.HeadscaleGrpcService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.HeadscalePreAuthKeyUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsUser;


import com.jetron.nb.dal.vo.headscale.HeadscaleNode;
import com.jetron.nb.dal.vo.headscale.HeadscalePreAuthKey;
import com.jetron.nb.dal.vo.headscale.HeadscaleUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/headscale")
@Slf4j
public class HeadscaleController {

    @Autowired
    private HeadscaleService headscaleService;

    @Autowired
    private HeadscaleHybridService hybridService;
    @Autowired
    private HeadscalePreAuthKeyUtils headscalePreAuthKeyUtils;

    @Autowired
    private HeadscaleGrpcService grpcService;

    /**
     * Check admin access for all Headscale operations
     */
    private void checkAdminAccess() {
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();

        if (currentUser == null) {
            throw new IotException(ApiCode.INVALID_SESSION);
        } else if (!currentUser.getName().equals("admin")) {
            throw new IotException(ApiCode.NO_AUTHORITY);
        }
    }

    /**
     * Test Headscale connection (both REST and gRPC)
     */
    @OperateLog(remark = "'测试Headscale连接'")
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public Result testConnection() {
        checkAdminAccess();

        try {
            HeadscaleHybridService.ConnectionStatus status = hybridService.getConnectionStatus();

            if (status.isAnyAvailable()) {
                // Return the status message directly for better display
                return Result.success(status.getStatus());
            } else {
                return Result.toResult(ApiCode.FAIL, "所有Headscale连接都失败");
            }
        } catch (Exception e) {
            log.error("[HeadscaleController#testConnection] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "连接测试失败: " + e.getMessage());
        }
    }

    /**
     * Get detailed connection status
     */
    @OperateLog(remark = "'获取Headscale连接状态详情'")
    @RequestMapping(value = "/status", method = RequestMethod.GET)
    public Result getConnectionStatus() {
        checkAdminAccess();

        try {
            HeadscaleHybridService.ConnectionStatus status = hybridService.getConnectionStatus();

            return Result.success(new Object() {
                public final boolean restAvailable = status.isRestAvailable();
                public final boolean grpcAvailable = status.isGrpcAvailable();
                public final String message = status.getStatus();
                public final String restStatus = status.isRestAvailable() ? "可用" : "不可用";
                public final String grpcStatus = status.isGrpcAvailable() ? "可用" : "不可用";
            });
        } catch (Exception e) {
            log.error("[HeadscaleController#getConnectionStatus] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取连接状态失败: " + e.getMessage());
        }
    }

    /**
     * Create namespace using gRPC API
     */
    @OperateLog(remark = "'创建Headscale命名空间'")
    @RequestMapping(value = "/namespaces", method = RequestMethod.POST)
    public Result createNamespace(@RequestParam("namespaceName") String namespaceName) {
        checkAdminAccess();

        if (StringUtils.isBlank(namespaceName)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "命名空间名称不能为空");
        }

        // 校验命名空间名称格式
        if (!namespaceName.matches("^[a-zA-Z0-9_-]+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "命名空间名称只能包含字母、数字、下划线和连字符");
        }

        try {
            hybridService.createNamespace(namespaceName);
            return Result.success("命名空间创建成功");
        } catch (Exception e) {
            log.error("[HeadscaleController#createNamespace] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "创建命名空间失败: " + e.getMessage());
        }
    }

    /**
     * Get all users
     */
    @OperateLog(remark = "'获取Headscale用户列表'")
    @RequestMapping(value = "/users", method = RequestMethod.GET)
    public Result getUsers() {
        checkAdminAccess();

        try {
            List<HeadscaleUser> users = headscaleService.getUsers();
            return Result.success(users);
        } catch (Exception e) {
            log.error("[HeadscaleController#getUsers] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * Create a new user with optional displayName (namespace)
     */
    @OperateLog(remark = "'创建Headscale用户'")
    @RequestMapping(value = "/users", method = RequestMethod.POST)
    public Result createUser(@RequestParam("username") String username,
                           @RequestParam(value = "displayName", required = false) String displayName) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        // 校验用户名不能是纯数字
        if (username.matches("^\\d+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能是纯数字，请使用包含字母的用户名");
        }

        // 校验用户名格式
        if (!username.matches("^[a-zA-Z0-9_-]+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名只能包含字母、数字、下划线和连字符");
        }

        // 校验displayName格式（如果提供）
        if (StringUtils.isNotBlank(displayName)) {
            if (!displayName.matches("^[a-zA-Z0-9_-]+$")) {
                return Result.toResult(ApiCode.BAD_REQUEST, "显示名称只能包含字母、数字、下划线和连字符");
            }
        }

        try {
            HeadscaleUser user;
            if (StringUtils.isNotBlank(displayName)) {
                log.info("Creating user with displayName: username={}, displayName={}", username, displayName);
                user = hybridService.createUser(username, displayName);
            } else {
                log.info("Creating user without displayName: username={}", username);
                user = hybridService.createUser(username);
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("[HeadscaleController#createUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "创建用户失败: " + e.getMessage());
        }
    }

    /**
     * Delete a user
     */
    @OperateLog(remark = "'删除Headscale用户'")
    @RequestMapping(value = "/users/{username}", method = RequestMethod.DELETE)
    public Result deleteUser(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            headscaleService.deleteUserSafely(username);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("[HeadscaleController#deleteUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * Check if user can be deleted (has no nodes)
     */
    @OperateLog(remark = "'检查用户是否可删除'")
    @RequestMapping(value = "/users/{username}/can-delete", method = RequestMethod.GET)
    public Result canDeleteUser(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            boolean hasNodes = headscaleService.userHasNodes(username);
            return Result.success(new Object() {
                public final boolean canDelete = !hasNodes;
                public final String message = hasNodes ? "该用户下还有设备节点，无法删除" : "可以删除";
            });
        } catch (Exception e) {
            log.error("[HeadscaleController#canDeleteUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "检查失败: " + e.getMessage());
        }
    }

    /**
     * Get pre-auth keys for a user
     */
    @OperateLog(remark = "'获取用户预授权密钥'")
    @RequestMapping(value = "/users/{username}/preauth-keys", method = RequestMethod.GET)
    public Result getPreAuthKeys(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            log.info("Getting pre-auth keys for user: {}", username);
            List<HeadscalePreAuthKey> keys = headscaleService.getPreAuthKeys(username);
            log.info("Successfully retrieved {} pre-auth keys for user: {}", keys != null ? keys.size() : 0, username);
            return Result.success(keys);
        } catch (Exception e) {
            log.error("[HeadscaleController#getPreAuthKeys] exception for user: {}", username, e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取预授权密钥失败: " + e.getMessage());
        }
    }

    /**
     * Create a pre-auth key for a user
     */
    @OperateLog(remark = "'创建用户预授权密钥'")
    @RequestMapping(value = "/users/{username}/preauth-keys", method = RequestMethod.POST)
    public Result createPreAuthKey(@PathVariable("username") String username,
                                   @RequestParam(value = "reusable", defaultValue = "false") Boolean reusable,
                                   @RequestParam(value = "ephemeral", defaultValue = "false") Boolean ephemeral,
                                   @RequestParam(value = "expirationHours", required = false) Integer expirationHours) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            HeadscalePreAuthKey key = headscaleService.createPreAuthKey(username, reusable, ephemeral, expirationHours);
            return Result.success(key);
        } catch (Exception e) {
            log.error("[HeadscaleController#createPreAuthKey] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "创建预授权密钥失败: " + e.getMessage());
        }
    }

    /**
     * Get cached PreAuth Key from Redis for a user
     */
    @OperateLog(remark = "'获取用户缓存的预授权密钥'")
    @RequestMapping(value = "/users/{username}/cached-preauth-key", method = RequestMethod.GET)
    public Result getCachedPreAuthKey(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            String preAuthKey = headscalePreAuthKeyUtils.getPreAuthKey(username);

            if (StringUtils.isNotBlank(preAuthKey)) {
                long ttl = headscalePreAuthKeyUtils.getPreAuthKeyTTL(username);
                String user = username;

                return Result.success(new Object() {
                    public final String key = preAuthKey;
                    public final String username = user;
                    public final long ttlSeconds = ttl;
                    public final String ttlHours = String.format("%.1f", ttl / 3600.0);
                    public final boolean expired = ttl <= 0;
                });
            } else {
                return Result.toResult(ApiCode.FAIL, "该用户没有缓存的预授权密钥");
            }
        } catch (Exception e) {
            log.error("[HeadscaleController#getCachedPreAuthKey] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取缓存预授权密钥失败: " + e.getMessage());
        }
    }

    /**
     * Delete cached PreAuth Key from Redis for a user
     */
    @OperateLog(remark = "'删除用户缓存的预授权密钥'")
    @RequestMapping(value = "/users/{username}/cached-preauth-key", method = RequestMethod.DELETE)
    public Result deleteCachedPreAuthKey(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            boolean deleted = headscalePreAuthKeyUtils.deletePreAuthKey(username);

            if (deleted) {
                return Result.success("缓存的预授权密钥删除成功");
            } else {
                return Result.toResult(ApiCode.FAIL, "该用户没有缓存的预授权密钥");
            }
        } catch (Exception e) {
            log.error("[HeadscaleController#deleteCachedPreAuthKey] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "删除缓存预授权密钥失败: " + e.getMessage());
        }
    }

    /**
     * Get all nodes/devices
     */
    @OperateLog(remark = "'获取所有设备节点'")
    @RequestMapping(value = "/nodes", method = RequestMethod.GET)
    public Result getNodes() {
        checkAdminAccess();

        try {
            List<HeadscaleNode> nodes = headscaleService.getNodes();
            return Result.success(nodes);
        } catch (Exception e) {
            log.error("[HeadscaleController#getNodes] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取设备节点失败: " + e.getMessage());
        }
    }

    /**
     * Get nodes for a specific user
     */
    @OperateLog(remark = "'获取用户设备节点'")
    @RequestMapping(value = "/users/{username}/nodes", method = RequestMethod.GET)
    public Result getNodesByUser(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            List<HeadscaleNode> nodes = headscaleService.getNodesByUser(username);
            return Result.success(nodes);
        } catch (Exception e) {
            log.error("[HeadscaleController#getNodesByUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取用户设备节点失败: " + e.getMessage());
        }
    }

    /**
     * Delete a node
     */
    @OperateLog(remark = "'删除设备节点'")
    @RequestMapping(value = "/nodes/{nodeId}", method = RequestMethod.DELETE)
    public Result deleteNode(@PathVariable("nodeId") String nodeId) {
        checkAdminAccess();

        if (StringUtils.isBlank(nodeId)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "节点ID不能为空");
        }

        try {
            headscaleService.deleteNode(nodeId);
            return Result.success("设备节点删除成功");
        } catch (Exception e) {
            log.error("[HeadscaleController#deleteNode] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "删除设备节点失败: " + e.getMessage());
        }
    }

    /**
     * Get online devices count and status
     */
    @OperateLog(remark = "'获取在线设备状态'")
    @RequestMapping(value = "/nodes/status", method = RequestMethod.GET)
    public Result getNodesStatus() {
        checkAdminAccess();

        try {
            List<HeadscaleNode> nodes = headscaleService.getNodes();

            long onlineCount = nodes.stream().filter(node -> Boolean.TRUE.equals(node.getOnline())).count();
            long totalCount = nodes.size();
            long offlineCount = totalCount - onlineCount;

            return Result.success(new Object() {
                public final long total = totalCount;
                public final long online = onlineCount;
                public final long offline = offlineCount;
            });
        } catch (Exception e) {
            log.error("[HeadscaleController#getNodesStatus] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取设备状态失败: " + e.getMessage());
        }
    }

    /**
     * Get all routes from all nodes
     */
    @OperateLog(remark = "'获取所有设备路由'")
    @RequestMapping(value = "/routes", method = RequestMethod.GET)
    public Result getAllRoutes() {
        checkAdminAccess();

        try {
            List<HeadscaleNode> nodes = headscaleService.getNodes();
            return Result.success(nodes);
        } catch (Exception e) {
            log.error("[HeadscaleController#getAllRoutes] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取路由失败: " + e.getMessage());
        }
    }

    /**
     * Set approved routes for a node
     */
    @OperateLog(remark = "'设置节点批准路由'")
    @RequestMapping(value = "/nodes/{nodeId}/approve-routes", method = RequestMethod.POST)
    public Result setApprovedRoutes(@PathVariable("nodeId") String nodeId,
                                   @RequestBody List<String> routes) {
        checkAdminAccess();

        if (StringUtils.isBlank(nodeId)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "节点ID不能为空");
        }

        if (routes == null) {
            return Result.toResult(ApiCode.BAD_REQUEST, "路由列表不能为空");
        }

        try {
            hybridService.setApprovedRoutes(nodeId, routes);
            return Result.success("路由设置成功");
        } catch (Exception e) {
            log.error("[HeadscaleController#setApprovedRoutes] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "设置路由失败: " + e.getMessage());
        }
    }

    /**
     * Get current ACL policy
     */
    @OperateLog(remark = "'获取ACL策略'")
    @RequestMapping(value = "/acl", method = RequestMethod.GET)
    public Result getACLPolicy() {
        checkAdminAccess();

        try {
            String aclPolicy = headscaleService.getACLPolicy();
            return Result.success(aclPolicy);
        } catch (Exception e) {
            log.error("[HeadscaleController#getACLPolicy] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取ACL策略失败: " + e.getMessage());
        }
    }

    /**
     * Update ACL policy
     */
    @OperateLog(remark = "'更新ACL策略'")
    @RequestMapping(value = "/acl", method = RequestMethod.PUT)
    public Result updateACLPolicy(@RequestBody String aclPolicyJson) {
        checkAdminAccess();

        if (StringUtils.isBlank(aclPolicyJson)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "ACL策略不能为空");
        }

        try {
            // Use the corrected method for Headscale v0.26.1
            String updatedPolicy = headscaleService.updateACLPolicy(aclPolicyJson);
            log.info("[HeadscaleController#updateACLPolicy] Successfully updated ACL policy");
            return Result.success(updatedPolicy);
        } catch (Exception e) {
            log.warn("[HeadscaleController#updateACLPolicy] Wrapped format failed, trying raw JSON format", e);

            // If wrapped format fails, try sending raw JSON directly
            try {
                String updatedPolicy = headscaleService.updateACLPolicyAlternative(aclPolicyJson);
                log.info("[HeadscaleController#updateACLPolicy] Successfully updated ACL policy with alternative method");
                return Result.success(updatedPolicy);
            } catch (Exception e2) {
                log.error("[HeadscaleController#updateACLPolicy] Both methods failed", e2);
                return Result.toResult(ApiCode.SERVER_ERROR, "更新ACL策略失败: " + e2.getMessage() + " (原始错误: " + e.getMessage() + ")");
            }
        }
    }
    /**
     * Validate ACL policy
     */
    @OperateLog(remark = "'验证ACL策略'")
    @RequestMapping(value = "/acl/validate", method = RequestMethod.POST)
    public Result validateACLPolicy(@RequestBody String aclPolicyJson) {
        checkAdminAccess();

        if (StringUtils.isBlank(aclPolicyJson)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "ACL策略不能为空");
        }

        try {
            // Basic JSON validation
            com.alibaba.fastjson.JSONObject jsonObj = JSON.parseObject(aclPolicyJson);

            // Validate ACL structure
            java.util.List<String> errors = new java.util.ArrayList<>();

            // Validate groups
            if (jsonObj.containsKey("groups")) {
                com.alibaba.fastjson.JSONObject groups = jsonObj.getJSONObject("groups");
                if (groups != null) {
                    for (String groupName : groups.keySet()) {
                        Object groupValue = groups.get(groupName);
                        if (!(groupValue instanceof com.alibaba.fastjson.JSONArray)) {
                            errors.add("组 \"" + groupName + "\" 的成员必须是数组");
                        }
                    }
                }
            }

            // Validate tagOwners
            if (jsonObj.containsKey("tagOwners")) {
                com.alibaba.fastjson.JSONObject tagOwners = jsonObj.getJSONObject("tagOwners");
                if (tagOwners != null) {
                    for (String tagName : tagOwners.keySet()) {
                        Object tagValue = tagOwners.get(tagName);
                        if (!(tagValue instanceof com.alibaba.fastjson.JSONArray)) {
                            errors.add("标签 \"" + tagName + "\" 的所有者必须是数组");
                        }
                    }
                }
            }

            // Validate ACLs
            if (jsonObj.containsKey("acls")) {
                com.alibaba.fastjson.JSONArray acls = jsonObj.getJSONArray("acls");
                if (acls != null) {
                    for (int i = 0; i < acls.size(); i++) {
                        com.alibaba.fastjson.JSONObject acl = acls.getJSONObject(i);
                        if (acl != null) {
                            if (!acl.containsKey("action")) {
                                errors.add("策略 #" + (i + 1) + " 缺少 action 字段");
                            }
                            if (!acl.containsKey("src") || !(acl.get("src") instanceof com.alibaba.fastjson.JSONArray)) {
                                errors.add("策略 #" + (i + 1) + " 的 src 必须是数组");
                            }
                            if (!acl.containsKey("dst") || !(acl.get("dst") instanceof com.alibaba.fastjson.JSONArray)) {
                                errors.add("策略 #" + (i + 1) + " 的 dst 必须是数组");
                            }
                        }
                    }
                }
            }

            if (!errors.isEmpty()) {
                return Result.toResult(ApiCode.BAD_REQUEST, "ACL配置验证失败: " + String.join("; ", errors));
            }

            return Result.success("ACL配置验证通过");
        } catch (Exception e) {
            log.error("[HeadscaleController#validateACLPolicy] exception", e);
            return Result.toResult(ApiCode.BAD_REQUEST, "ACL配置格式错误: " + e.getMessage());
        }
    }

    /**
     * Get ACL template
     */
    @OperateLog(remark = "'获取ACL模板'")
    @RequestMapping(value = "/acl/template", method = RequestMethod.GET)
    public Result getACLTemplate(@RequestParam(value = "type", defaultValue = "basic") String templateType) {
        checkAdminAccess();

        try {
            String template = getACLTemplateByType(templateType);
            return Result.success(template);
        } catch (Exception e) {
            log.error("[HeadscaleController#getACLTemplate] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取ACL模板失败: " + e.getMessage());
        }
    }

    private String getACLTemplateByType(String templateType) {
        switch (templateType.toLowerCase()) {
            case "basic":
                return "{\n" +
                       "  \"groups\": {\n" +
                       "    \"group:admin\": [\"admin@\"],\n" +
                       "    \"group:users\": [\"user1@\", \"user2@\"]\n" +
                       "  },\n" +
                       "  \"tagOwners\": {\n" +
                       "    \"tag:server\": [\"group:admin\"]\n" +
                       "  },\n" +
                       "  \"hosts\": {},\n" +
                       "  \"acls\": [\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:admin\"],\n" +
                       "      \"dst\": [\"*:*\"]\n" +
                       "    },\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:users\"],\n" +
                       "      \"dst\": [\"group:users:*\"]\n" +
                       "    }\n" +
                       "  ]\n" +
                       "}";
            case "enterprise":
                return "{\n" +
                       "  \"groups\": {\n" +
                       "    \"group:admin\": [\"admin@\"],\n" +
                       "    \"group:dev\": [\"dev1@\", \"dev2@\"],\n" +
                       "    \"group:intern\": [\"intern1@\"]\n" +
                       "  },\n" +
                       "  \"tagOwners\": {\n" +
                       "    \"tag:prod-servers\": [\"group:admin\"],\n" +
                       "    \"tag:dev-servers\": [\"group:admin\", \"group:dev\"],\n" +
                       "    \"tag:internal\": [\"group:admin\"]\n" +
                       "  },\n" +
                       "  \"hosts\": {\n" +
                       "    \"database.internal\": \"*********/32\",\n" +
                       "    \"web.internal\": \"*********/32\"\n" +
                       "  },\n" +
                       "  \"acls\": [\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:admin\"],\n" +
                       "      \"dst\": [\"*:*\"]\n" +
                       "    },\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:dev\"],\n" +
                       "      \"dst\": [\"tag:dev-servers:*\", \"tag:prod-servers:80,443\"]\n" +
                       "    },\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:intern\"],\n" +
                       "      \"dst\": [\"tag:dev-servers:80,443\"]\n" +
                       "    }\n" +
                       "  ]\n" +
                       "}";
            case "homelab":
                return "{\n" +
                       "  \"groups\": {\n" +
                       "    \"group:family\": [\"dad@\", \"mom@\"],\n" +
                       "    \"group:kids\": [\"kid1@\", \"kid2@\"]\n" +
                       "  },\n" +
                       "  \"tagOwners\": {\n" +
                       "    \"tag:servers\": [\"group:family\"],\n" +
                       "    \"tag:iot\": [\"group:family\"]\n" +
                       "  },\n" +
                       "  \"hosts\": {\n" +
                       "    \"nas.home\": \"*************/32\",\n" +
                       "    \"printer.home\": \"*************/32\"\n" +
                       "  },\n" +
                       "  \"acls\": [\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:family\"],\n" +
                       "      \"dst\": [\"*:*\"]\n" +
                       "    },\n" +
                       "    {\n" +
                       "      \"action\": \"accept\",\n" +
                       "      \"src\": [\"group:kids\"],\n" +
                       "      \"dst\": [\"group:kids:*\", \"nas.home:80,443\"]\n" +
                       "    }\n" +
                       "  ]\n" +
                       "}";
            case "empty":
            default:
                return "{\n" +
                       "  \"groups\": {},\n" +
                       "  \"tagOwners\": {},\n" +
                       "  \"hosts\": {},\n" +
                       "  \"acls\": []\n" +
                       "}";
        }
    }

    /**
     * Check Headscale configuration for ACL troubleshooting
     */
    @OperateLog(remark = "'检查Headscale配置'")
    @RequestMapping(value = "/config/check", method = RequestMethod.GET)
    public Result checkHeadscaleConfig() {
        checkAdminAccess();

        try {
            java.util.Map<String, Object> configInfo = headscaleService.checkHeadscaleConfig();
            return Result.success(configInfo);
        } catch (Exception e) {
            log.error("[HeadscaleController#checkHeadscaleConfig] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "检查配置失败: " + e.getMessage());
        }
    }





    /**
     * Get gRPC diagnostics information
     */
    @OperateLog(remark = "'获取gRPC诊断信息'")
    @RequestMapping(value = "/grpc/diagnostics", method = RequestMethod.GET)
    public Result getGrpcDiagnostics() {
        checkAdminAccess();

        try {
            String diagnostics = grpcService.getGrpcDiagnostics();
            return Result.success(diagnostics);
        } catch (Exception e) {
            log.error("[HeadscaleController#getGrpcDiagnostics] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "获取gRPC诊断信息失败: " + e.getMessage());
        }
    }

    /**
     * Test different gRPC connection modes
     */
    @OperateLog(remark = "'测试gRPC连接模式'")
    @RequestMapping(value = "/grpc/test-modes", method = RequestMethod.GET)
    public Result testGrpcConnectionModes() {
        checkAdminAccess();

        try {
            String results = grpcService.testConnectionModes();
            return Result.success(results);
        } catch (Exception e) {
            log.error("[HeadscaleController#testGrpcConnectionModes] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "测试gRPC连接模式失败: " + e.getMessage());
        }
    }

    /**
     * Test basic gRPC connectivity
     */
    @OperateLog(remark = "'测试基本gRPC连接'")
    @RequestMapping(value = "/grpc/test-basic", method = RequestMethod.GET)
    public Result testBasicGrpcConnectivity() {
        checkAdminAccess();

        try {
            String results = grpcService.testBasicConnectivity();
            return Result.success(results);
        } catch (Exception e) {
            log.error("[HeadscaleController#testBasicGrpcConnectivity] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "测试基本gRPC连接失败: " + e.getMessage());
        }
    }

    /**
     * Test specific gRPC port
     */
    @OperateLog(remark = "'测试特定gRPC端口'")
    @RequestMapping(value = "/grpc/test-port", method = RequestMethod.GET)
    public Result testGrpcPort(@RequestParam("port") int port,
                              @RequestParam(value = "tls", defaultValue = "false") boolean useTls) {
        checkAdminAccess();

        try {
            String results = grpcService.testSpecificPort(port, useTls);
            return Result.success(results);
        } catch (Exception e) {
            log.error("[HeadscaleController#testGrpcPort] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "测试gRPC端口失败: " + e.getMessage());
        }
    }

    /**
     * Test gRPC user creation with displayName (for debugging)
     */
    @OperateLog(remark = "'测试gRPC用户创建'")
    @RequestMapping(value = "/users/test-grpc-create", method = RequestMethod.POST)
    public Result testGrpcCreateUser(@RequestParam("username") String username,
                                   @RequestParam(value = "displayName", required = false) String displayName) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        // 校验用户名不能是纯数字
        if (username.matches("^\\d+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能是纯数字，请使用包含字母的用户名");
        }

        // 校验用户名格式
        if (!username.matches("^[a-zA-Z0-9_-]+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名只能包含字母、数字、下划线和连字符");
        }

        // 校验displayName格式（如果提供）
        if (StringUtils.isNotBlank(displayName)) {
            if (!displayName.matches("^[a-zA-Z0-9_-]+$")) {
                return Result.toResult(ApiCode.BAD_REQUEST, "显示名称只能包含字母、数字、下划线和连字符");
            }
        }

        try {
            HeadscaleUser user;
            if (StringUtils.isNotBlank(displayName)) {
                log.info("Testing gRPC user creation with displayName: username={}, displayName={}", username, displayName);
                user = hybridService.createUser(username, displayName);
            } else {
                log.info("Testing gRPC user creation without displayName: username={}", username);
                user = hybridService.createUser(username);
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("[HeadscaleController#testGrpcCreateUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "测试gRPC用户创建失败: " + e.getMessage());
        }
    }

    /**
     * Test simple user creation (for debugging)
     */
    @OperateLog(remark = "'测试简单用户创建'")
    @RequestMapping(value = "/users/test-create", method = RequestMethod.POST)
    public Result testCreateUser(@RequestParam("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        // 校验用户名不能是纯数字
        if (username.matches("^\\d+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能是纯数字，请使用包含字母的用户名");
        }

        // 校验用户名格式
        if (!username.matches("^[a-zA-Z0-9_-]+$")) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名只能包含字母、数字、下划线和连字符");
        }

        try {
            log.info("Testing simple user creation for username: {}", username);
            HeadscaleUser user = headscaleService.createUser(username);
            log.info("Test user creation successful: {}", user.getName());
            return Result.success(user);
        } catch (Exception e) {
            log.error("[HeadscaleController#testCreateUser] exception", e);
            return Result.toResult(ApiCode.SERVER_ERROR, "测试创建用户失败: " + e.getMessage());
        }
    }

    /**
     * Debug endpoint to test pre-auth keys API call
     */
    @OperateLog(remark = "'调试预授权密钥API'")
    @RequestMapping(value = "/debug/users/{username}/preauth-keys", method = RequestMethod.GET)
    public Result debugGetPreAuthKeys(@PathVariable("username") String username) {
        checkAdminAccess();

        if (StringUtils.isBlank(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST, "用户名不能为空");
        }

        try {
            log.info("Debug: Getting pre-auth keys for user: {}", username);

            // First check if user exists
            try {
                HeadscaleUser user = headscaleService.getUserByName(username);
                log.info("Debug: User found - ID: {}, Name: {}, DisplayName: {}",
                        user.getId(), user.getName(), user.getDisplayName());
            } catch (Exception e) {
                log.warn("Debug: User not found or error getting user: {}", e.getMessage());
                return Result.toResult(ApiCode.BAD_REQUEST, "用户不存在: " + username);
            }

            // Try to get pre-auth keys
            List<HeadscalePreAuthKey> keys = headscaleService.getPreAuthKeys(username);
            log.info("Debug: Successfully retrieved {} pre-auth keys for user: {}",
                    keys != null ? keys.size() : 0, username);

            final String finalUsername = username;
            final List<HeadscalePreAuthKey> finalKeys = keys;
            return Result.success(new Object() {
                public final String username = finalUsername;
                public final int keyCount = finalKeys != null ? finalKeys.size() : 0;
                public final List<HeadscalePreAuthKey> keys = finalKeys;
            });
        } catch (Exception e) {
            log.error("[HeadscaleController#debugGetPreAuthKeys] exception for user: {}", username, e);
            return Result.toResult(ApiCode.SERVER_ERROR, "调试获取预授权密钥失败: " + e.getMessage());
        }
    }
}
