package com.jetron.nb.web.controller.external;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jetron.nb.biz.service.AcsDeviceFlowService;
import com.jetron.nb.biz.service.AcsDeviceService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.ParamUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.common.vo.UserInfo;
import com.jetron.nb.dal.po.AcsDeviceFlow;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.AcsUserDevice;
import com.jetron.nb.dal.po.UserDeviceFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/external")
@Slf4j
public class ExternalController {

    @Autowired
    AcsUserDeviceService userDeviceService;
    @Autowired
    AcsDeviceFlowService acsDeviceFlowService;
    @Autowired
    AcsUserService acsUserService;
    @Autowired
    AcsDeviceService acsDeviceService;

    /**
     * 查询设备状态
     * @return
     */
    @PostMapping("deviceStatus")
    public Result findUserDeviceStatus(ContentCachingRequestWrapper requestWrapper) {
        String s = new String(requestWrapper.getContentAsByteArray());
        JSONObject param = JSONObject.parseObject(s);
        List<String> snList = JSONArray.parseArray(param.getString("snList"), String.class);
        return  userDeviceService.findDeviceStatus(snList);
    }

    /**
     * 获取所有设备列表
     */
    @PostMapping("/deviceList/query")
    public Result findDeviceList(ContentCachingRequestWrapper requestWrapper) {
        String s = new String(requestWrapper.getContentAsByteArray());
        JSONObject param = JSONObject.parseObject(s);
        // 直接从请求体中解析出UserDeviceFilter对象
        Integer page = param.getInteger("page");
        Integer size = param.getInteger("size");
        String username = param.getString("username");
        Result result = null;
        try {
            // 封装过滤参数
            UserDeviceFilter filter = param.containsKey("filter")
                    ? JSONObject.parseObject(param.getString("filter"), UserDeviceFilter.class)
                    : new UserDeviceFilter();
            // 查询数据
            Map<String, Object> data = userDeviceService.search(filter, username, page, size);
            result = Result.success(data);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userDeviceList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 获取设备详情
     */
    @PostMapping("/deviceDetail/get")
    public Result getDeviceDetail(ContentCachingRequestWrapper requestWrapper) {
        String s = new String(requestWrapper.getContentAsByteArray());
        JSONObject param = JSONObject.parseObject(s);
        String sn = param.getString("sn");
        if (StringUtils.isBlank(sn)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }
        String start = param.getString("flowStartTime");
        String end = param.getString("flowEndTime");
        Result result = null;
        Map<String, Object> dataMap = Maps.newHashMap();
        try {
            AcsUserDevice userDevice = userDeviceService.getBySn(sn);
            dataMap.put("childDevices", JSONArray.parseArray(userDevice.getBaseDev()));
            dataMap.put("deviceInfo", userDevice);
            // 获取流量
            AcsDeviceFlow paramToFlow = new AcsDeviceFlow();
            paramToFlow.setSn(sn);
            Date startTime = StringUtils.isNotBlank(start) ?
                    DateUtils.parseDate(start, "yyyy-MM-dd HH:mm:ss") : getDateBeforeThirtyDay();
            Date endTime = StringUtils.isNotBlank(end) ?
                    DateUtils.parseDate(end, "yyyy-MM-dd HH:mm:ss") : new Date();
            paramToFlow.setStart(startTime);
            paramToFlow.setEnd(endTime);
            List<AcsDeviceFlow> flowInfos = acsDeviceFlowService.findTrueList(paramToFlow);
            dataMap.put("flowInfos", flowInfos);
            // 获取图形数据
            Result chartResult = acsDeviceFlowService.deviceDataLineChart(sn, startTime, endTime);
            Object chartData = chartResult.getData();
            dataMap.put("chartData", chartData);
            // 获取
            result = Result.success(dataMap);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @PostMapping("/user/add")
    public Result addUser(ContentCachingRequestWrapper requestWrapper) {
        Result result = null;

        String s = new String(requestWrapper.getContentAsByteArray());
        JSONObject param = JSONObject.parseObject(s);

        UserInfo userInfo = new UserInfo();
        userInfo.setName(param.getString("username"));
        userInfo.setPasswd(param.getString("passwd"));
        userInfo.setVpnHost(param.getString("vpnHost"));
        userInfo.setMultilayer(param.getString("multilayer"));
        if (!StringUtils.isBlank(param.getString("vpnPort"))) {
            userInfo.setVpnPort(Integer.parseInt(param.getString("vpnPort")));
        }
        if (!StringUtils.isBlank(param.getString("company"))) {
            userInfo.setCompany(param.getString("company"));
        }

        try {
            ParamUtils.checkCreateUserInfo(userInfo);
            acsUserService.createUser(userInfo);
            result = Result.success();
        } catch (Exception e) {
            log.error("[ExternalController#createUser] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @PostMapping("/devices/add")
    public Result addDevices(ContentCachingRequestWrapper requestWrapper) {
        Result result = null;
        String s = new String(requestWrapper.getContentAsByteArray());
        JSONObject param = JSONObject.parseObject(s);
        List<String> snList = JSONArray.parseArray(param.getString("snList"), String.class);
        if (snList == null || snList.size() == 0) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }

        try {
            result = acsDeviceService.addList(snList);;
        } catch (Exception e) {
            log.error("[ExternalController#createDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @PostMapping("/devices/bind")
    public Result bindDevice(ContentCachingRequestWrapper requestWrapper) {
        Result result = null;
        try {
            // 获取当前用户的company信息
            AcsUser currentUser = MdcUtils.getCurrentAcsUser();
            if (currentUser == null) {
                return Result.toResult(ApiCode.BAD_REQUEST);
            }

            String company = currentUser.getCompany();
            if (StringUtils.isBlank(company)) {
                return Result.toResult(ApiCode.BAD_REQUEST, "公司信息不能为空");
            }

            result = acsDeviceService.getAllocatedDevices(company);
        } catch (Exception e) {
            log.error("[ExternalController#bindDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    private Date getDateBeforeThirtyDay() {
        Date date = new Date();
        return DateUtils.addDays(date, -30);
    }
}
