package com.jetron.nb.web.controller.firmware;

import com.jetron.nb.biz.service.AcsFirmwareService;
import com.jetron.nb.biz.service.AcsFirmwareUpgradeService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.FirmwareUpgradeInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsConfigUpgrade;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.dal.po.UpgradeFilter;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 固件控制器
 */
@RestController
@RequestMapping("/firmware")
@Slf4j
public class FirmwareController {

    @Autowired
    private AcsFirmwareService acsFirmwareService;
    @Autowired
    private AcsFirmwareUpgradeService acsFirmwareUpgradeService;

    @OperateLog(remark = "'上传固件, name：' + #p1 + ', model: ' + #p2 + ', version: ' + #p3 + ', describ: ' + #p4 + ',configId: ' + #p6")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Object upload(@RequestParam("name") String name,
                         @RequestParam("model") String model,
                         @RequestParam("version") String version,
                         @RequestParam("describ") String describ,
                         @RequestParam("file") MultipartFile file,
                         @RequestParam(value = "configId", required = false) Integer configId) {

        if (StringUtils.isAnyBlank(name, model, version) || file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsFirmwareService.upload(name, model, version, describ, file, configId);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#addDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 固件列表按条件查询
     *
     * @param name  固件名
     * @param model 设备型号
     * @param page  页码
     * @param size  每页数量
     * @return 按条件查询的固件结果
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object firmwareList(@RequestParam(value = "name", required = false) String name,
                               @RequestParam(value = "model", required = false) String model,
                               @RequestParam(value = "page", required = false) Integer page,
                               @RequestParam(value = "size", required = false) Integer size) {


        Result result = null;
        try {
            Map<String, Object> data = acsFirmwareService.search(name, model, page, size);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#firmwareList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 固件升级结果
     *
     * @param id 固件ID
     * @return 固件ID对应的升级结果
     */
    @RequestMapping(value = "/upgradeList", method = RequestMethod.POST)
    public Object upgradeList(Integer id) {
        if (id == null || id < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            List<FirmwareUpgradeInfo> data = acsFirmwareUpgradeService.getUpgradeRecords(id);
            result = Result.success(data);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#upgradeList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 固件升级结果
     *
     * @param id 固件ID
     * @return 固件ID对应的升级结果
     */
    @RequestMapping(value = "/upgradePage", method = RequestMethod.POST)
    public Object upgradePage(@RequestParam(value = "id") Integer id,
                              @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                              @RequestParam(value = "size", required = false,defaultValue = "20") Integer size) {
        if (id == null || id < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            UpgradeFilter filter = new UpgradeFilter();
            filter.setId(id);
            return Result.success(acsFirmwareUpgradeService.page(filter,page,size));
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#upgradeList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @OperateLog(remark = "'固件升级, firmwareId：' + #p1 + ', deviceIds: ' + #p2")
    @RequestMapping(value = "/upgrade", method = RequestMethod.POST)
    public Object upgrade(@RequestParam("firmwareId") Integer firmwareId,
                          @RequestParam("deviceIds") String deviceIds) {
        if (firmwareId == null || StringUtils.isAnyBlank(deviceIds)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            MdcUtils.putLogType(LogType.FIRMWARE_UPGRADE);
            acsFirmwareService.upgrade(firmwareId, deviceIds);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#upgrade] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'删除固件, firmwareId：' + #p1")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Object delete(@RequestParam("firmwareId") Integer firmwareId,
                         @RequestParam(value = "passwd") String passwd) {
        if (firmwareId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsFirmwareService.delete(firmwareId);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'删除固件升级记录, firmwareId：' + #p1")
    @RequestMapping(value = "/deleteUpgrade", method = RequestMethod.POST)
    public Object deleteUpgrade(@RequestParam("firmwareId") Integer firmwareId,
                                @RequestParam(value = "passwd") String passwd) {
        if (firmwareId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsFirmwareUpgradeService.deleteFirmwareUpgradeByFirmwareId(firmwareId);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#deleteUpgrade] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'强制完成升级'")
    @RequestMapping(value = "/completeUpgrade", method = RequestMethod.POST)
    public Result completeUpgrade(
            @RequestParam(value = "passwd") String passwd,
            @RequestParam(value = "id") Integer id) {
        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsFirmwareUpgradeService.completeUpgrade(id);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#deleteUpgrade] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return Result.success();
    }
}
