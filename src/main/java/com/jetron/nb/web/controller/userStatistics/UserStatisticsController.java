package com.jetron.nb.web.controller.userStatistics;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.service.AcsAlarmService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.util.DateUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.*;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/userStatistics")
@Slf4j
public class UserStatisticsController {

    @Value("${heartbeat.interval}")
    private Integer heartBeat;
    @Autowired
    private AcsUserService acsUserService;

    @Autowired
    private AcsUserDeviceService deviceService;
    @Autowired
    private AcsAlarmService acsAlarmService;

    /**
     * 根据公司
     *
     * @return 查询列表
     */
    @RequestMapping(value = "/userStatisticsTable", method = RequestMethod.POST)
    public Object userInfo(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            UserFilter userFilter) {
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = 20;
        }
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (MdcUtils.isAdmin()) {
        } else if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            userFilter.setCompany(currentUser.getCompany());
        } else {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        }
        return Result.success(acsUserService.userStatistics(userFilter, page, size));
    }

    /**
     * 根据公司
     *
     * @return 查询列表
     */
    @RequestMapping(value = "/statistic", method = RequestMethod.POST)
    public Object statistic() {
        UserFilter userFilter = new UserFilter();
        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        AcsAlarmFilter alarm = new AcsAlarmFilter();
        alarm.setAlarmStatus(null);
        int companyNum = 1;
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (MdcUtils.isAdmin()) {
            List<String> company = acsUserService.company();
            companyNum = CollectionUtils.isEmpty(company) ? 0 : company.size();
        } else if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            userFilter.setCompany(currentUser.getCompany());
            deviceFilter.setBelongTo(currentUser.getId());
            alarm.setCompany(currentUser.getCompany());
        } else {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        }
        int userNum = acsUserService.countByFilter(userFilter);
        int deviceNum = deviceService.countByFilter(deviceFilter);
        int alarmNum = acsAlarmService.findCount(alarm);
        Map<String, Object> map = new HashMap<>();
        map.put("companyNum", companyNum);
        map.put("userNum", userNum);
        map.put("deviceNum", deviceNum);
        map.put("alarmNum", alarmNum);
        return Result.success(map);
    }

    /**
     * 活跃用户
     *
     * @return 查询列表
     */
    @RequestMapping(value = "/activeUser", method = RequestMethod.POST)
    public Object activeUser(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size
    ) {
        UserFilter filter = new UserFilter();
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (MdcUtils.isAdmin()) {
        } else if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            filter.setCompany(currentUser.getCompany());
        } else {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        }
        filter.setStartLastLoginTime(DateUtils.getDateBefore(new Date(), 7));
        Map<String, Object> data = acsUserService.findPage(filter, page, size);
        return Result.success(data);
    }


    /**
     * 活跃用户
     *
     * @return 查询列表
     */
    @RequestMapping(value = "/deviceTree", method = RequestMethod.POST)
    public Object deviceTree() {
        String company = "root";
        UserDeviceFilter deviceFilter = new UserDeviceFilter();
        // 设置belongTo
        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (MdcUtils.isAdmin()) {
        } else if (currentUser.getRole().intValue() == RoleEnum.USER_ADMIN.role) {
            deviceFilter.setBelongTo(currentUser.getId());
            company = currentUser.getCompany();
        } else {
            return Result.toResult(ApiCode.NO_AUTHORITY);
        }
        List<AcsUserDevice> list = deviceService.findListWithUser(deviceFilter);
        JSONObject tree = creatTree(company, list);
        return Result.success(tree);
    }

    public JSONObject creatTree(String root, List<AcsUserDevice> deviceList) {
        long now = DateTime.now().getMillis();
        JSONArray oneArray = new JSONArray();
        JSONObject data = new JSONObject();
        data.put("name", root);
        data.put("children", oneArray);

        if (CollectionUtils.isEmpty(deviceList)) {
            return data;
        }
        if (MdcUtils.isAdmin()) {
            Collections.sort(deviceList, new Comparator<AcsUserDevice>() {
                @Override
                public int compare(AcsUserDevice o1, AcsUserDevice o2) {
                    //升序
                    return o1.getUser().getCompany().compareTo(o2.getUser().getCompany());
                }
            });
        }

        List<String> companyList = new ArrayList<>();
        for (int i = 0; i < deviceList.size(); i++) {
            AcsUserDevice device = deviceList.get(i);
            String userCompany = device.getUser().getCompany();
            JSONArray secondArray = null;
            if (MdcUtils.isAdmin()) {
                if (!companyList.contains(userCompany)) {
                    secondArray = new JSONArray();
                    JSONObject companyNode = new JSONObject();
                    companyNode.put("name", userCompany);
                    companyNode.put("children", secondArray);
                    oneArray.add(companyNode);
                    companyList.add(userCompany);
                } else {
                    for (int j = 0; j < oneArray.size(); j++) {
                        if (userCompany.equals(oneArray.getJSONObject(j).getString("name"))) {
                            JSONObject companyNode = oneArray.getJSONObject(j);
                            secondArray = companyNode.getJSONArray("children");
                            break;
                        }
                    }
                }
            }

            JSONObject node = new JSONObject();
            node.put("name", device.getSn());

            long lastHeartBeatTime = device.getLastHbTime().getTime();
            // 与HeartProcessor.java方法保持一致
            if ((int)((now - lastHeartBeatTime) / 1000) > heartBeat + 5) {
                node.put("status", "离线");
            } else {
                node.put("status", "在线");
            }
            JSONArray childList = new JSONArray();
            Integer baseDevNum = device.getBaseDevNum();
            if (baseDevNum != null && baseDevNum.intValue() > 0) {
                childList = JSONArray.parseArray(device.getBaseDev());
                for (int j = 0; j < childList.size(); j++) {
                    JSONObject childNode = childList.getJSONObject(j);
                    childNode.put("name", childNode.get("baseName"));
                }
            }
            node.put("children", childList);
            if (MdcUtils.isAdmin()) {
                secondArray.add(node);
            } else {
                oneArray.add(node);
            }
        }
        return data;
    }
}
