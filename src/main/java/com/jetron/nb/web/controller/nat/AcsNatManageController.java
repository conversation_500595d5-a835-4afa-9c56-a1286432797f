package com.jetron.nb.web.controller.nat;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jetron.nb.biz.service.AcsNatManageService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.vo.AcsNatParameter;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsNatRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/nat")
@Slf4j
public class AcsNatManageController {

    @Autowired
    private AcsNatManageService natManageService;

    /**
     * 分页条件查询 NAT 规则
     * @param pageNum 分页
     * @param pageSize 分页
     * @param acsNatRule 过滤条件
     * @return 返回集合
     */
    @GetMapping("/list")
    public Result listNatRules(@RequestParam(defaultValue = "1") Integer pageNum,
                                @RequestParam(defaultValue = "10") Integer pageSize,
                               AcsNatRule acsNatRule) {
        // 过滤数据
        IPage<AcsNatRule> resultPage = natManageService.queryPageWithCondition(pageNum, pageSize, acsNatRule);
        Map<String, Object> pageResult = PageUtils.toPageResult(Math.toIntExact(resultPage.getTotal()), pageNum, pageSize, resultPage.getRecords());
        return Result.success(pageResult);
    }

    /**
     * 查询NAT规则根据sn号
     * @param sn sn号
     * @return 结果集
     */
    @GetMapping("/get/{sn}")
    public Result getNatBySn(@PathVariable("sn") String sn) {
        List<AcsNatRule> nat = natManageService.getNatBySn(sn);
        return Result.success(nat);
    }

    /**
     * 新增NAT规则
     * @param natRule NAT规则
     * @return 结果
     */
    @RequestMapping("/add/logs")
    public Result addNat(AcsNatRule natRule) {
        natManageService.addNatLog(natRule);
        return Result.success();
    }

    /**
     *  删除 NAT 规则 ，暂时只是修改状态，真正删除在网关返回成功消息后
     * @param id 规则id
     * @return 返回集
     */
    @DeleteMapping("/{id}")
    public Result deleteNatRule(@PathVariable Integer id) {
        natManageService.updateNatStatus(id, "1");
        return Result.success("提交删除命令成功，等待删除");
    }

    /**
     * 推送指令
     */
    @PostMapping("/push")
    public Result pushCmd(@RequestBody AcsNatParameter natParameter) {
        // 入参校验
        if (null == natParameter.getAcsNat() || StringUtils.isEmpty(natParameter.getPushType())) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        // 发送推送指令
        natManageService.sendNatDelivery(natParameter.getAcsNat(),  natParameter.getPushType());
        return Result.success("推送成功");
    }

}
