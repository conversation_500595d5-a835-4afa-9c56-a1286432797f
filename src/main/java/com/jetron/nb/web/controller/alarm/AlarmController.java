package com.jetron.nb.web.controller.alarm;

import com.jetron.nb.biz.service.AcsAlarmService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.PageUtils;
import com.jetron.nb.common.util.ResultUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsAlarmFilter;
import com.jetron.nb.dal.po.AcsCaptureAlarm;
import com.jetron.nb.dal.po.AcsUser;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 命令窗口
 */
@RestController
@RequestMapping("/alarm")
@Slf4j
public class AlarmController {

    @Autowired
    private AcsAlarmService acsAlarmService;

    /**
     * 查询异常信息
     *
     * @param start 开始时间
     * @param end   截至时间
     * @return 异常信息
     */
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    public Object queryInfo(@RequestParam(value = "start", required = false) String start,
                            @RequestParam(value = "end", required = false) String end,
                            @RequestParam(value = "page", required = false) Integer page,
                            @RequestParam(value = "size", required = false) Integer size,
                            AcsAlarmFilter alarm ) {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }

        alarm = alarm == null ? new AcsAlarmFilter() : alarm;
        try {
            if (StringUtils.isNotBlank(start)) {
                alarm.setStart(DateUtils.parseDate(start, "yyyy-MM-dd"));
            }
            if (StringUtils.isNotBlank(end)) {
                alarm.setEnd(DateUtils.parseDate(end, "yyyy-MM-dd"));
            }
        } catch (ParseException e) {
            log.error(e+"");
        }
        alarm.setLimit(size);
        alarm.setOffset((page - 1) * size);

        AcsUser currentUser = MdcUtils.getCurrentAcsUser();
        if (currentUser.getRole() != RoleEnum.SUPER_ADMIN.role) {
            alarm.setCompany(currentUser.getCompany());
        }
        return Result.success(acsAlarmService.findWithDeviceList(alarm, page, size));
    }

    /**
     * 查询异常信息
     *
     * @param start 开始时间
     * @param end   截至时间
     * @return 异常信息
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object infoWithOutSession(@RequestParam(value = "start", required = false) String start,
                            @RequestParam(value = "end", required = false) String end,
                                     AcsAlarmFilter alarm ) {

        alarm = alarm == null ? new AcsAlarmFilter() : alarm;
        try {
            if (StringUtils.isNotBlank(start)) {
                alarm.setStart(DateUtils.parseDate(start, "yyyy-MM-dd"));
            }
            if (StringUtils.isNotBlank(end)) {
                alarm.setEnd(DateUtils.parseDate(end, "yyyy-MM-dd"));
            }
        } catch (ParseException e) {
            log.error(e+"");
        }
        return Result.success(acsAlarmService.findWithDeviceList(alarm));
    }

    /**
     * 大屏看板，报警栏
     *
     * @param start 开始时间
     * @param end   截至时间
     * @return 异常信息
     */
    @RequestMapping(value = "/kanban", method = RequestMethod.POST)
    public Object latest(@RequestParam(value = "start", required = false) String start,
                                     @RequestParam(value = "end", required = false) String end,
                                        AcsAlarmFilter alarm ) {

        alarm = alarm == null ? new AcsAlarmFilter() : alarm;
        try {
            if (StringUtils.isNotBlank(start)) {
                alarm.setStart(DateUtils.parseDate(start, "yyyy-MM-dd"));
            }
            if (StringUtils.isNotBlank(end)) {
                alarm.setEnd(DateUtils.parseDate(end, "yyyy-MM-dd"));
            } else {
                alarm.setEnd(com.jetron.nb.common.util.DateUtils.getDateBefore(new Date(), -1));
            }
        } catch (ParseException e) {
            log.error(e+"");
        }
        return Result.success( acsAlarmService.kanban(alarm));
    }

    /**
     * 修改异常信息状态
     *
     * @param alarm 修改列表，包含 ID，STATUS
     * @return 修改成功
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object updateStatus(AcsCaptureAlarm alarm) {
        acsAlarmService.updateAlarmStatus(alarm);
        return Result.success();
    }

    @RequestMapping(value = "/delList", method = RequestMethod.POST)
    public Object delList(@RequestBody List<Integer> ids) {
        acsAlarmService.delList(ids);
        return Result.success();
    }

    @RequestMapping(value = "/delByDate", method = RequestMethod.POST)
    public Object delByDate( Date startDate, Date endDate) {
        acsAlarmService.delByDate(startDate,endDate);
        return Result.success();
    }

    /**
     * 查询异常信息
     *
     * @param start 开始时间
     * @param end   截至时间
     * @return 异常信息
     */
    @RequestMapping(value = "/excel", method = RequestMethod.POST)
    public Object excel(@RequestParam(value = "start", required = false) String start,
                        @RequestParam(value = "end", required = false) String end,
                        AcsAlarmFilter alarm
            , HttpServletRequest request, HttpServletResponse response) {

        alarm = alarm == null ? new AcsAlarmFilter() : alarm;
        try {
            if (StringUtils.isNotBlank(start)) {
                alarm.setStart(DateUtils.parseDate(start, "yyyy-MM-dd"));
            }
            if (StringUtils.isNotBlank(end)) {
                alarm.setEnd(DateUtils.parseDate(end, "yyyy-MM-dd"));
            }
            acsAlarmService.excel(request,response,alarm);
        } catch (ParseException e) {
            log.error(e+"");
        } catch (IOException e) {
            log.error(e+"");
            return ResultUtils.newResult.fail("导出失败！");
        }
        return Result.success();
    }
}
