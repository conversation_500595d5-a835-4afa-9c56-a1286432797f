package com.jetron.nb.web.controller.sys;

import com.jetron.nb.biz.service.AcsCompanyDictionaryService;
import com.jetron.nb.biz.service.AcsCompanyDictionaryService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsCompanyDictionary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  系统字典
 */
@RestController
@RequestMapping("/companyDict")
@Slf4j
public class SysCompanyDictionaryController {
    @Autowired
    private AcsCompanyDictionaryService service;


    @RequestMapping(value = "/dictPage", method = RequestMethod.POST)
    public Object getDictionaryList(@RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                                    @RequestParam(value = "size", required = false,defaultValue = "20") Integer size,
                                    @RequestParam(value= "dict",required = false)AcsCompanyDictionary dict) {
        return Result.success(service.findConvertPage(page, size,dict));
    }

    @RequestMapping(value = "/dictList", method = RequestMethod.POST)
    public Object getDictionaryList(AcsCompanyDictionary dict) {
        return Result.success(service.findList(dict));
    }

    /**
     *  删除特定字典信息
     * @return 删除结果
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Result save(@Validated  AcsCompanyDictionary dict) {
        Result result = null;
        try {
            return service.save(dict);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     *  新增字典
     * @return 添加结果
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result addDictionary(@RequestBody List<AcsCompanyDictionary> list) {
        Result result = null;
        try {
            return service.insertList(list);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#add] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     *  删除特定字典信息
     * @return 删除结果
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result update(@RequestBody  AcsCompanyDictionary dict) {
        Result result = null;
        try {
            return service.update(dict);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     *  删除特定字典信息
     * @return 删除结果
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result delDictionary(@RequestBody List<Integer> list) {
        Result result = null;
        try {
            return service.delete(list);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }
}
