package com.jetron.nb.web.controller.device;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jetron.nb.biz.service.AcsTableDisplayService;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsTableDisplay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/table")
@Slf4j
public class TableDisplayController {

    @Autowired
    private AcsTableDisplayService tableDisplayService;

    public Result findList(AcsTableDisplay param){
        return tableDisplayService.findList(param);
    }

    @PostMapping(value = "/userDevice")
    public Result findUserDeviceInfoColumn(){
        AcsTableDisplay param = new AcsTableDisplay();
        param.setUserId(MdcUtils.getCurrentAcsUser().getId());
        return tableDisplayService.findList(param);
    }

    @PostMapping(value = "/userDevice/update")
    public Result updateColumn(@RequestBody JSONObject param){
        String table = param.getString("tableName");
        List<String> columnsList = JSONArray.parseArray(param.getJSONArray("columnList").toJSONString(), String.class);
        return tableDisplayService.updateColumn(columnsList,table);
    }
}
