package com.jetron.nb.web.controller.sys;

import com.jetron.nb.biz.service.AcsDictionaryService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsDictionary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  系统字典
 */
@RestController
@RequestMapping("/dictionary")
@Slf4j
public class SysDictionaryController {
    @Autowired
    private AcsDictionaryService acsDictionaryService;

    /**
     *  根据key查找内容
     * @param key 字典key
     * @return 字典内容
     */
    @RequestMapping(value = "/dic", method = RequestMethod.GET)
    public Object getDictionary(@RequestParam("key") String key) {
        return acsDictionaryService.findByKey(key);
    }

    /**
     *  根据字典key查找
     * @param page 页码
     * @param size 每页数量
     * @param dicKey 字典key
     * @return 特定key对应的字典内容
     */
    @RequestMapping(value = "/dicList", method = RequestMethod.POST)
    public Object getDictionaryList(@RequestParam(value = "page", required = false) Integer page,
                                    @RequestParam(value = "size", required = false) Integer size,
                                    @RequestParam(value= "dicKey",required = false)String dicKey) {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }
        AcsDictionary dictionary = new AcsDictionary();
        dictionary.setDicKey(dicKey);
        return Result.success(acsDictionaryService.findList(page, size,dictionary));
    }

    /**
     *  新增字典
     * @param acsDictionary 字典内容
     * @return 添加结果
     */
    @RequestMapping(value = "/dic/add", method = RequestMethod.POST)
    public Result addDictionary(AcsDictionary acsDictionary) {
        Result result = null;
        try {
            acsDictionaryService.insert(acsDictionary);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#add] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     *  删除特定字典信息
     * @param id 字典ID
     * @return 删除结果
     */
    @RequestMapping(value = "/dic/delete", method = RequestMethod.POST)
    public Result delDictionary(@RequestParam("id") Integer id) {
        Result result = null;
        try {
            acsDictionaryService.deleteByPrimaryKey(id);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }
}
