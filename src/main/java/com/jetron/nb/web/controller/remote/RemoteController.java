package com.jetron.nb.web.controller.remote;

import com.jetron.nb.biz.service.RemoteService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 远程网关控制
 */
@RestController
@RequestMapping("/remote")
@Slf4j
public class RemoteController {

    @Autowired
    private RemoteService remoteService;

    /**
     * 根据公司
     *
     * @return 查询列表
     */
    @RequestMapping(value = "/runContainer", method = RequestMethod.POST)
    public Object runContainer(
            @RequestParam(value = "userDeviceId") Integer userDeviceId
            ,@RequestParam("passwd") String passwd) {
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(passwd, currUser.getPasswd())) {
            return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
        }
        Result init = remoteService.runContainer(userDeviceId,1);
        return init;
    }


    /**
     * 局域网远程连接
     * @return 查询列表
     */
    @RequestMapping(value = "/lanConnection", method = RequestMethod.POST)
    public Object lanConnection(
            @RequestParam(value = "userDeviceId") Integer userDeviceId
            , @RequestParam("passwd") String passwd) {
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(passwd, currUser.getPasswd())) {
            return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
        }
        Result init = remoteService.runContainer(userDeviceId,2);
        return init;
    }

}
