package com.jetron.nb.web.controller.config;

import com.jetron.nb.biz.service.*;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.LogType;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.ConfigUpgradeInfo;
import com.jetron.nb.common.vo.FirmwareUpgradeInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.*;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户配置
 */
@RestController
@RequestMapping("/config")
@Slf4j
public class ConfigController {

    @Autowired
    private AcsConfigService acsConfigService;
    @Autowired
    private AcsConfigUpgradeService acsConfigUpgradeService;
    @Autowired
    private AcsVpnConfigService acsVpnConfigService;


    @OperateLog(remark = "'上传配置, name: ' + #p1 + ', model: ' + #p2 + ', describ: ' + #p3")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Object upload(@RequestParam("name") String name,
                         @RequestParam("model") String model,
                         @RequestParam("describ") String describ,
                         @RequestParam("file") MultipartFile file) {

        if (StringUtils.isAnyBlank(name, model) || file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        // 检查文件格式
        String filename = file.getOriginalFilename();
        if (StringUtils.isEmpty(filename) || !(filename.endsWith(".txt") || filename.endsWith(".tar.gz"))) {
            return Result.toResult(ApiCode.FILE_FORMAT_NOT_CORRECT);
        }

        Result result = null;
        try {
            acsConfigService.upload(name, model, describ, file);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#addDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 配置列表
     *
     * @param name  配置名称
     * @param model 型号
     * @param page  页码
     * @param size  每页数量
     * @return 配置列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object configList(@RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "model", required = false) String model,
                             @RequestParam(value = "page", required = false) Integer page,
                             @RequestParam(value = "size", required = false) Integer size) {


        Result result = null;
        try {
            Map<String, Object> data = acsConfigService.search(name, model, page, size);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#configList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 配置升级
     *
     * @param id 配置ID
     * @return 升级结果
     */
    @RequestMapping(value = "/upgradeList", method = RequestMethod.POST)
    public Object upgradeList(Integer id) {
        if (id == null || id < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            List<ConfigUpgradeInfo> data = acsConfigUpgradeService.getUpgradeRecords(id);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#upgradeList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 配置升级
     *
     * @param id 配置ID
     * @return 升级结果
     */
    @RequestMapping(value = "/upgradePage", method = RequestMethod.POST)
    public Object upgradePage(@RequestParam(value = "id") Integer id,
                              @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                              @RequestParam(value = "size", required = false,defaultValue = "20") Integer size) {
        if (id == null || id < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            UpgradeFilter filter = new UpgradeFilter();
            filter.setId(id);
            return Result.success(acsConfigUpgradeService.page(filter,page,size));
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#upgradeList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'配置升级, configId：' + #p1 + ', 设备列表: ' + #p2")
    @RequestMapping(value = "/upgrade", method = RequestMethod.POST)
    public Object upgrade(@RequestParam("configId") Integer configId,
                          @RequestParam("deviceIds") String deviceIds) {
        if (configId == null || StringUtils.isAnyBlank(deviceIds)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            MdcUtils.putLogType(LogType.CONFIG_UPGRADE);
            acsConfigService.upgrade(configId, deviceIds);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#upgrade] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }


    @OperateLog(remark = "'删除配置, configId：' + #p1")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Object delete(
            @RequestParam(value = "passwd") String passwd,
            @RequestParam("configId") Integer configId) {
        if (configId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsConfigService.delete(configId);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'管理员分配配置'")
    @RequestMapping(value = "/share", method = RequestMethod.POST)
    public Object share(AcsConfigShare share) {
        List<AcsConfigShare> shareList = acsConfigService.searchConfigShare(share);
        if (!CollectionUtils.isEmpty(shareList)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        acsConfigService.insertConfigShare(share);
        return Result.success();
    }

    @OperateLog(remark = "'设置VPN静态IP'")
    @RequestMapping(value = "/vpn/staticip", method = RequestMethod.POST)
    public Object staticIp(@RequestParam("userId") Integer userId,
                           @RequestParam("tIP") String tIP,
                           @RequestParam("rIP") String rIP) {

        return acsVpnConfigService.setVpnStaticIp(userId, tIP, rIP) ? Result.success() : Result.toResult(ApiCode.SERVER_ERROR);
    }

    @OperateLog(remark = "'删除VPN静态IP'")
    @RequestMapping(value = "/vpn/staticip", method = RequestMethod.DELETE)
    public Object staticIpDelete(@RequestParam("id") Integer id) {

        return acsVpnConfigService.deleteVpnStaticById(id);
    }

    @OperateLog(remark = "'更新VPN静态IP'")
    @RequestMapping(value = "/vpn/staticip", method = RequestMethod.PUT)
    public Object staticIpUpdate(AcsVpn acsVpn) {

        return acsVpnConfigService.updateVpnStatic(acsVpn);
    }

    @OperateLog(remark = "'VPN静态IP查找所有'")
    @RequestMapping(value = "/vpn/staticip/findall", method = RequestMethod.GET)
    public Object staticIpFindAll(@RequestParam(value = "page", required = false) Integer page,
                                  @RequestParam(value = "size", required = false) Integer size) {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }
        return acsVpnConfigService.findAllVpnStatic(page, size);
    }

    @OperateLog(remark = "'VPN静态IP查找-根据用户名'")
    @RequestMapping(value = "/vpn/staticip/findbyname", method = RequestMethod.GET)
    public Object staticIpFindByUsername(@RequestParam("userId") Integer userId,
                                         @RequestParam(value = "page", required = false) Integer page,
                                         @RequestParam(value = "size", required = false) Integer size) {
        if (page == null) {
            page = 1;
        }

        if (size == null) {
            size = 20;
        }
        return acsVpnConfigService.findVpnStaticByUserName(userId, page, size);
    }

    @OperateLog(remark = "'强制完成升级'")
    @RequestMapping(value = "/completeUpgrade", method = RequestMethod.POST)
    public Result completeUpgrade(
            @RequestParam(value = "passwd") String passwd,
            @RequestParam(value = "id") Integer id) {
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(passwd, currUser.getPasswd())) {
            return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
        }
        acsConfigUpgradeService.completeUpgrade(id);
        return Result.success();
    }

    @OperateLog(remark = "'删除配置升级记录, configId：' + #p1")
    @RequestMapping(value = "/deleteUpgrade", method = RequestMethod.POST)
    public Object deleteUpgrade(
            @RequestParam(value = "passwd") String passwd,
            @RequestParam("configId") Integer configId) {
        if (configId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsConfigUpgradeService.deleteUpgradeByConfigId(configId);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[ConfigController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }
}
