package com.jetron.nb.web.controller.sys;

import com.jetron.nb.biz.service.AcsCommandService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.CommandUtils;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.ShaUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsUser;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 *  命令窗口，用于执行系统命令；重启、重置网关
 * <AUTHOR>
 * @since 2021-7-23
 */
@RestController
@RequestMapping("/cmd")
@Slf4j
public class SysCommandController {
    @Autowired
    private AcsCommandService acsCommandService;

    @OperateLog(remark = "'系统命令执行, command: ' + #p1")
    @RequestMapping(value = "/exec", method = RequestMethod.POST)
    public Object exec(@RequestParam("command") String command) {
        int result = 0;
        System.out.println("Command: " + command);
        String[] cmd = new String[]{"/bin/sh", "-c", command };
        try {
            result = CommandUtils.exec(cmd);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @OperateLog(remark = "'重启5G网关, sn: ' + #p1")
    @RequestMapping(value = "/restart", method = RequestMethod.POST)
    public Object execRestart(@RequestParam("sn") String sn,
                              @RequestParam(value = "passwd") String passwd) {
        if (StringUtils.isEmpty(sn) || StringUtils.isEmpty(passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(ShaUtils.SHAMD5(passwd), currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsCommandService.restartDevice(sn);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return Result.toResult(ApiCode.SUCCESS);
    }

    @OperateLog(remark = "'恢复出厂设置, sn: ' + #p1")
    @RequestMapping(value = "/reset", method = RequestMethod.POST)
    public Object execReset(@RequestParam("sn") String sn,
                              @RequestParam(value = "passwd") String passwd) {
        if (StringUtils.isEmpty(sn) || StringUtils.isEmpty(passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (currUser.getRole() == RoleEnum.USER.role || !Objects.equals(ShaUtils.SHAMD5(passwd), currUser.getPasswd())) {
                return Result.toResult(ApiCode.NO_AUTHORITY);
            }
            acsCommandService.resetDevice(sn);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return Result.toResult(ApiCode.SUCCESS);
    }
}
