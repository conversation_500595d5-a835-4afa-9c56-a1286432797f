package com.jetron.nb.web.controller.device;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.jetron.nb.biz.service.AcsDeviceService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.MessageCmd;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 设备控制器
 */
@RestController
@RequestMapping("/device")
@Slf4j
public class DeviceController {

    @Autowired
    private AcsDeviceService acsDeviceService;
    @Autowired
    private AcsUserDeviceService acsUserDeviceService;
    @Autowired
    private AcsUserService acsUserService;

    @OperateLog(remark = "'添加设备, model：' + #p1 + ', sn: ' + #p2")
    @RequestMapping(value = "/addDevice", method = RequestMethod.POST)
    public Object addDevice(String model, String sn) {
        if (StringUtils.isAnyBlank(model, sn)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsDeviceService.add(model, sn);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#addDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'删除设备, sn: ' + #p1")
    @RequestMapping(value = "/removeDevice", method = RequestMethod.POST)
    public Object removeDevice(String sn) {
        if (StringUtils.isAnyBlank(sn)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsDeviceService.remove(sn);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#removeDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;

    }

    @OperateLog(remark = "'导入设备'")
    @RequestMapping(value = "/importDevice", method = RequestMethod.POST)
    public Object importDevices(MultipartFile file) {
        if (file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            result = acsDeviceService.importDevice(file);
            // result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#addDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }


    @OperateLog(remark = "'分配多个设备'")
    @RequestMapping(value = "/allocateMultiDevice", method = RequestMethod.POST)
    public Object allocateMultiDevices(@RequestParam("deviceIds") String deviceIds,
                                       @RequestParam("username") String username) {
        if (StringUtils.isAnyBlank(deviceIds, username)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        try {
            return acsDeviceService.allocateMultiDevice(deviceIds, username);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.toResult(ApiCode.SERVER_ERROR);
    }

    /**
     * 设备列表
     *
     * @param model 设备型号
     * @param sn    设备SN
     * @param page  页码
     * @param size  每页数量
     * @return 设备列表
     */
    @RequestMapping(value = "/deviceList", method = RequestMethod.POST)
    public Object deviceList(@RequestParam(value = "model", required = false) String model,
                             @RequestParam(value = "sn", required = false) String sn,
                             @RequestParam(value = "page", required = false) Integer page,
                             @RequestParam(value = "size", required = false) Integer size) {
        Result result = null;
        try {
            if (page == null) {
                page = 1;
            }
            if (size == null) {
                size = 20;
            }

            DeviceFilter deviceFilter = new DeviceFilter();
            deviceFilter.setModel(model);
            deviceFilter.setSn(sn);
            deviceFilter.setOffset((page - 1) * size);
            deviceFilter.setLimit(size);
            Map<String, Object> data = acsDeviceService.search(deviceFilter, page, size);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userDeviceList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 用户设备列表
     *
     * @param filter   用户设备过滤类
     * @param username 用户名
     * @param upgrade  升级与否
     * @param page     每页页码
     * @param size     每页数量
     * @return 用户设备列表
     */
    @RequestMapping(value = "/userDeviceList", method = RequestMethod.POST)
    public Object userDeviceList(UserDeviceFilter filter,
                                 @RequestParam(value = "name", required = false) String username,
                                 @RequestParam(value = "upgrade", required = false) Integer upgrade,
                                 @RequestParam(value = "page", required = false) Integer page,
                                 @RequestParam(value = "size", required = false) Integer size) {

        Result result = null;
        try {
            filter.setUpgradeStatus(upgrade);
            Map<String, Object> data = acsUserDeviceService.search(filter, username, page, size);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userDeviceList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @RequestMapping(value = "/exportUserDeviceExcel", method = RequestMethod.GET)
    public Object exportUserDeviceExcel(UserDeviceFilter filter,HttpServletRequest request,HttpServletResponse resp) {
        return acsUserDeviceService.exportUserDeviceExcel(filter,request,resp);
    }


    /**
     * 版本列表
     *
     * @return 版本列表
     */
    @RequestMapping(value = "/versionList", method = RequestMethod.POST)
    public Object userDeviceList() {
        return Result.success(acsUserDeviceService.getVernoList());
    }

    /**
     * 设备下挂子设备信息
     *
     * @param id 设备ID
     * @return 设备下挂子设备信息
     */
    @RequestMapping(value = "/userBaseDevice", method = RequestMethod.POST)
    public Object userBaseDevice(@Param("id") Integer id) {
        Result result = null;
        try {
            JSONArray data = acsUserDeviceService.getBaseDevice(id);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userBaseDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 更新用户设备信息
     *
     * @return 删除结果
     */
    @RequestMapping(value = "/updateUserDevice", method = RequestMethod.POST)
    public Object updateUserDevice(AcsUserDevice acsUserDevice) {
        Result result = null;
        try {
            if (acsUserDevice.getId() == null) {
                return Result.toResult(ApiCode.SERVER_ERROR);
            }
            acsUserDeviceService.updateByPrimaryKeySelective(acsUserDevice);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userBaseDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 更新用户设备别名，位置信息。
     *
     * @return 删除结果
     */
    @RequestMapping(value = "/updateUserDeviceAlias", method = RequestMethod.POST)
    public Object updateUserDeviceAlias(AcsUserDevice acsUserDevice) {
        Result result = null;
        try {
            if (acsUserDevice.getId() == null) {
                return Result.toResult(ApiCode.SERVER_ERROR);
            }
            acsUserDeviceService.updateByPrimaryKeySelective(acsUserDevice);
            JSONObject paramJson = new JSONObject();
            paramJson.put("cmd",MessageCmd.DEVICE_INFO_ALIAS.cmd);
            paramJson.put("alias",acsUserDevice.getAlias());
            paramJson.put("location",acsUserDevice.getPosition());
            acsUserDeviceService.sendMqtt(acsUserDevice.getSn(),paramJson);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userBaseDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }


    /**
     * 删除用户设备信息
     *
     * @param id     设备ID
     * @param passwd 当前用户密码
     * @return 删除结果
     */
    @RequestMapping(value = "/delUserDevice", method = RequestMethod.POST)
    public Object delUserDevice(@RequestParam("id") Integer id,
                                @RequestParam(value = "passwd") String passwd) {
        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsUserDeviceService.deleteByPrimaryKey(id);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userBaseDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 设备认证
     *
     * @param sn     设备SN
     * @param passwd 设备密码
     * @return 认证结果
     */
    @RequestMapping(value = "/auth", method = RequestMethod.POST)
    public Object login(String sn, String passwd) {
        if (StringUtils.isAnyBlank(sn, passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserDeviceService.auth(sn, passwd);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#auth] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'设备激活, sn：' + #p2 + ', username: ' + #p3")
    @RequestMapping(value = "/activate", method = RequestMethod.POST)
    public Object activate(HttpServletRequest request,
                           @RequestParam("sn") String sn,
                           @RequestParam("username") String username,
                           @RequestParam("passwd") String passwd) {

        if (StringUtils.isAnyBlank(sn, username, passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Object result = null;
        try {
            String devicePasswd = acsUserDeviceService.activateDevice(sn, username, passwd);
            result = Result.success(devicePasswd);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#activate] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'设备激活V1, sn：' + #p2 + ', username: ' + #p3")
    @RequestMapping(value = "/activate/v1", method = RequestMethod.POST)
    public Object activateV(HttpServletRequest request,
                            @RequestParam("sn") String sn,
                            @RequestParam("username") String username,
                            @RequestParam("passwd") String passwd) {

        if (StringUtils.isAnyBlank(sn, username, passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Object result = null;
        try {
            result = acsUserDeviceService.activate(sn, username, passwd);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#activate] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'设备重新注册激活, sn：' + #p1")
    @RequestMapping(value = "/reactivate", method = RequestMethod.POST)
    public Object activateV(@RequestParam("sn") String sn,
                            @RequestParam("id") Integer id,
                            @RequestParam(value = "passwd") String passwd) {
        if (StringUtils.isAnyBlank(sn, id.toString())) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Object result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            result = acsDeviceService.reActivate(sn, id);
        }  catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @OperateLog(remark = "'设备日志上传, deviceId: ' + #p1")
    @RequestMapping(value = "/log", method = RequestMethod.POST)
    public Object log(@RequestParam(value = "deviceId") Integer deviceId) {
        if (deviceId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserDeviceService.log(deviceId);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#logUpload] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 设备日志上传
     *
     * @param sn   设备SN
     * @param file 日志文件
     * @return 上传结果
     */
    @RequestMapping(value = "/logUpload", method = RequestMethod.POST)
    public Object logUpload(@RequestParam(value = "sn") String sn, MultipartFile file) {
        if (StringUtils.isAnyBlank(sn) || file.isEmpty()) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserDeviceService.logUpload(sn, file);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#logUpload] log upload failed, sn: {}, ", sn, e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 判断日志是否存在
     *
     * @param deviceId 设备ID
     * @return 判断结果
     */
    @RequestMapping(value = "/logExist", method = RequestMethod.POST)
    public Object logExist(@RequestParam(value = "deviceId") Integer deviceId) {
        if (deviceId == null || deviceId < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            boolean exist = acsUserDeviceService.logExist(deviceId);
            result = Result.success(exist);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#logExist] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'设备日志下载, deviceId: ' + #p2")
    @RequestMapping(value = "/logDownload", method = RequestMethod.GET)
    public Object logDownload(HttpServletResponse response,
                              @RequestParam(value = "deviceId") Integer deviceId) {
        if (deviceId == null || deviceId < 0) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserDeviceService.logDownload(response, deviceId);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#logDownload] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'证书下载, username: ' + #p1+', passwd: ' + #p2")
    @RequestMapping(value = "/certDownload", method = RequestMethod.GET)
    public Object certDownload(
            @RequestParam("username") String username,
            @RequestParam("passwd") String passwd) {
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(username)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        AcsUser user = acsUserService.getByUsername(username);
        if (user == null || !user.getPasswd().equals(passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Object result = null;
        try {
            result = acsUserDeviceService.certDownload();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#logDownload] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 获取设备在线数据，包括在线、离线、总数
     *
     * @return 设备在线数据
     */
    @RequestMapping(value = "/online", method = RequestMethod.GET)
    public Object deviceOnlineStatus() {
        return acsDeviceService.getDeviceOnlineInfo();
    }

    /**
     * 设备树
     *
     * @return 树
     */
    @RequestMapping(value = "/deviceTree", method = RequestMethod.POST)
    public Object deviceTree(@RequestParam Integer userDeviceId, @RequestParam String sn) {
        AcsUserDevice device = acsUserDeviceService.selectByPrimaryKey(userDeviceId);
        if (device == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        JSONObject data = new JSONObject();
        data.put("name", sn);
        if (device.getBaseDevNum() == null || device.getBaseDevNum() < 1) {
            data.put("children", new JSONArray());
        } else {
            JSONArray childList = JSONArray.parseArray(device.getBaseDev());
            for (int i = 0; i < childList.size(); i++) {
                JSONObject node = childList.getJSONObject(i);
                node.put("name", node.get("baseName"));
            }
            data.put("children", childList);
        }

        return Result.success(data);
    }

    /**
     * 分组查询设备型号，和数量
     *
     * @return
     */
    @RequestMapping(value = "/deviceModel", method = RequestMethod.POST)
    public Object deviceModel() {
        return acsUserDeviceService.deviceModel();
    }

    @RequestMapping(value = "/ntpSetUp", method = RequestMethod.POST)
    public Object ntpSetUp(@RequestParam(value = "sn") String sn,
                           @RequestParam(value = "ntpServer") String ntpServer,
                           @RequestParam(value = "interval") String interval,
                           @RequestParam(value = "intervalType") String intervalType) {
        JSONObject param = new JSONObject();
        param.put("ntpServer", ntpServer);
        param.put("interval", interval);
        param.put("intervalType", intervalType);
        param.put("cmd", MessageCmd.NTP_SERVER.cmd);
        return acsUserDeviceService.sendMqtt(sn, param);
    }

    /**
     * 用户设备列表
     * @return 用户设备列表
     */
    @RequestMapping(value = "/onlineClass", method = RequestMethod.POST)
    public Object userDeviceListAdmin() {
        Result result = null;
        try {
            return  acsUserDeviceService.onlineAndOffline();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userDeviceList] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/fiveGSetUp", method = RequestMethod.POST)
    public Object fiveGSetUp(@RequestParam(value = "sn") String sn,
                             @RequestParam(value = "fiveG") String fiveG,
                             @RequestParam(value = "networkType") String networkType) {
        JSONObject param = new JSONObject();
        param.put("fiveG", fiveG);
        param.put("networkType", networkType);
        param.put("cmd", MessageCmd.NET_CONFIG.cmd);
        return acsUserDeviceService.sendMqtt(sn, param);
    }

    @RequestMapping(value = "/apnSetUp", method = RequestMethod.POST)
    public Object apnSetUp(@RequestParam(value = "sn") String sn,
                           @RequestParam(value = "name") String name,
                           @RequestParam(value = "apn") String apn,
                           @RequestParam(value = "proxy", required = false) String proxy,
                           @RequestParam(value = "port", required = false) String port,
                           @RequestParam(value = "userName", required = false) String userName,
                           @RequestParam(value = "pass", required = false) String pass,
                           @RequestParam(value = "authenticationType", required = false) String authenticationType,
                           @RequestParam(value = "apnType", required = false) String apnType,
                           @RequestParam(value = "apnProtocol", required = false) String apnProtocol,
                           @RequestParam(value = "apnRoamingProtocol", required = false) String apnRoamingProtocol,
                           @RequestParam(value = "mvnoType", required = false) String mvnoType,
                           @RequestParam(value = "mvnpValue", required = false) String mvnpValue
    ) {
        JSONObject param = new JSONObject();
        param.put("name", name);
        param.put("apn", apn);
        param.put("proxy", proxy);
        param.put("port", port);
        param.put("userName", userName);
        param.put("password", pass);
        param.put("authenticationType", authenticationType);
        param.put("apnType", apnType);
        param.put("apnProtocol", apnProtocol);
        param.put("apnRoamingProtocol", apnRoamingProtocol);
        param.put("mvnoType", mvnoType);
        param.put("mvnpValue", mvnpValue == null ? "" : mvnpValue);
        param.put("cmd", MessageCmd.APN_CONFIG.cmd);
        return acsUserDeviceService.sendMqtt(sn, param);
    }

    @RequestMapping(value = "/bindIPSetUp", method = RequestMethod.POST)
    public Object bindIPSetUp(@RequestParam(value = "sn") String sn,
                           @RequestParam(value = "bindIP") String bindIP,
                           @RequestParam(value = "enable") String enable) {
        return acsUserDeviceService.bindIP(sn,bindIP,enable);
    }

    @RequestMapping(value = "/networkMonitor", method = RequestMethod.POST)
    public Object networkMonitor(@RequestParam(value = "sn") String sn,
                              @RequestParam(value = "monitor") String monitor,
                              @RequestParam(value = "ip") String ip,
                              @RequestParam(value = "ipBackup",required = false) String ipBackup,
                              @RequestParam(value = "probeInterval") String probeInterval,
                              @RequestParam(value = "messageNum") int messageNum) {
        return acsUserDeviceService.networkMonitor(sn,monitor,ip,ipBackup,probeInterval,messageNum);
    }

    @RequestMapping(value = "/setLANIP", method = RequestMethod.POST)
    public Object setLANIP(@RequestParam(value = "sn") String sn,
                           @RequestParam(value = "ip") String ip,
                           @RequestParam(value = "netmask") String netmask) {
        return acsUserDeviceService.setLANIP(sn, ip,netmask);
    }

    @RequestMapping(value = "/initNetworkSetUp", method = RequestMethod.POST)
    public Object initNetworkSetUp(@RequestParam(value = "sn") String sn) {
        return acsUserDeviceService.initNetworkSetUp(sn);
    }

    @RequestMapping(value = "/networkDiagnosis", method = RequestMethod.POST)
    public Object networkDiagnosis(@RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                                   @RequestParam(value = "size", required = false,defaultValue = "10") Integer size,
                                   @RequestParam(value = "sns", required = false) String sns) {
        List<String> snList = new ArrayList<>();
        if (StringUtils.isNotBlank(sns)) {
            snList = Splitter.on(",").splitToList(sns);
        }
        return acsUserDeviceService.networkDiagnosis(snList,page,size);
    }

    @RequestMapping(value = "/oneClickRepair", method = RequestMethod.POST)
    public Object oneClickRepair(@RequestParam(value = "mqttErrorCode",required = false) String mqttErrorCode,
                                 @RequestParam(value = "mysqlErrorCode",required = false) String mysqlErrorCode,
                                 @RequestParam(value = "vpnErrorCode",required = false) String vpnErrorCode,
                                 @RequestParam(value = "threadErrorCode",required = false) String threadErrorCode) {
        return acsUserDeviceService.oneClickRepair(mqttErrorCode,mysqlErrorCode,vpnErrorCode,threadErrorCode);
    }

    @RequestMapping(value = "/portMappingPage", method = RequestMethod.POST)
    public Object portMappingPage(AcsPortMapping param,
                                 @RequestParam(value = "page", defaultValue = "1") Integer page,
                                 @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return Result.success(acsUserDeviceService.portMappingPage(param,page,size));
    }

    @RequestMapping(value = "/savePortMapping", method = RequestMethod.POST)
    public Object savePortMapping(@Valid AcsPortMapping param) {
        return acsUserDeviceService.savePortMapping(param);
    }
    @RequestMapping(value = "/delPortMapping", method = RequestMethod.POST)
    public Object delPortMapping(@RequestParam String id) {
        acsUserDeviceService.delPortMapping(id);
        return Result.success();
    }

    @RequestMapping(value = "/sendPortMappingAdd", method = RequestMethod.POST)
    public Object sendPortMappingAdd(@RequestBody JSONObject param) {
        return acsUserDeviceService.sendPortMapping(param,"add");
    }

    @RequestMapping(value = "/sendPortMappingDel", method = RequestMethod.POST)
    public Object sendPortMappingDel(@RequestBody JSONObject param) {
        return acsUserDeviceService.sendPortMapping(param,"del");
    }

    @RequestMapping(value = "/portMappingDevicePage", method = RequestMethod.POST)
    public Object portMappingDevicePage(AcsPortMappingDevice param,
                                        @RequestParam(value = "page", defaultValue = "1") Integer page,
                                        @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return Result.success(acsUserDeviceService.portMappingDevicePage(param,page,size));
    }

    @RequestMapping(value = "/testrecord", method = RequestMethod.POST)
    public Object testrecord(@RequestParam String sn, @RequestParam String token,
                             @RequestBody String json) {
        return  acsUserDeviceService.testrecord(sn,token,json);
    }

    /**
     * 下发指令，查询网关的端口映射
     * @param sn 网关sn
     */
    @RequestMapping(value = "/sendPortMappingGet", method = RequestMethod.POST)
    public Object sendPortMappingGet(@RequestParam String sn) {
        return acsUserDeviceService.sendPortMappingGet(sn);
    }
}
