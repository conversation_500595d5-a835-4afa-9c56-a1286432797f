package com.jetron.nb.web.controller.sys;

import com.jetron.nb.biz.service.AcsSysConfigService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.vo.Result;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  系统配置控制器
 */
@RestController
@RequestMapping("/sysconfig")
@Slf4j
public class SysConfigController {
    @Autowired
    private AcsSysConfigService acsSysConfigService;

    @OperateLog(remark = "'更新系统配置项, name: ' + #p1 + ', model: ' + #p2 + ', describ: ' + #p3")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@RequestParam("name") String name,
                         @RequestParam("content") String content,
                         @RequestParam("describ") String describ) {
        if (StringUtils.isAnyBlank(name, content)) {
            return new IotException(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsSysConfigService.update(name, content, describ);
            result = Result.success();

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysConfigController#update] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'查看系统配置项列表'")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object list() {
        Result result = null;
        try {
            result = Result.success(acsSysConfigService.list());

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysConfigController#update] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'查看系统配详情'")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public Object detail(@RequestParam("name") String name) {
        if (StringUtils.isAnyBlank(name)) {
            return new IotException(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            result = Result.success(acsSysConfigService.getByName(name));

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysConfigController#detail] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }


}
