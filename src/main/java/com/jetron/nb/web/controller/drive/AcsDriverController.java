package com.jetron.nb.web.controller.drive;

import com.jetron.nb.biz.service.AcsDriverService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsDriver;
import com.jetron.nb.dal.po.AcsUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Objects;

/**
 * 驱动管理
 */
@RestController
@RequestMapping("/driver")
@Slf4j
public class AcsDriverController {

    @Autowired
    private AcsDriverService service;

    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public Object page(@RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                        @RequestParam(value = "size", required = false,defaultValue = "20") Integer size,
                         AcsDriver param) {
        return Result.success(service.page(page, size,param));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object list(AcsDriver param) {
        return Result.success(service.list(param));
    }



    /**
     *  新增字典
     * @return 添加结果
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result add(
            @RequestParam("name") String name,
            @RequestParam("protocol") String protocol,
            @RequestParam("file") MultipartFile file) {
        if (StringUtils.isAnyBlank(name, protocol) || file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            service.add(name,protocol,file);
            return Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#add] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }


    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Result save(@RequestBody @Validated AcsDriver param) {
        Result result = null;
        try {
            service.save(param);
            return Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     *  删除特定字典信息
     * @return 删除结果
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result delete( @RequestParam(value = "passwd") String passwd,
                          @RequestParam("id") Integer id) {
        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (!Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            service.deleteList(Arrays.asList(id));
            return Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[SysDictionaryController#delete] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }
}
