package com.jetron.nb.web.controller.account;

import com.jetron.nb.biz.service.AcsUserService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.util.ParamUtils;
import com.jetron.nb.common.vo.CurrentUserInfo;
import com.jetron.nb.common.vo.UserInfo;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsUser;
import com.jetron.nb.web.common.util.ThreadContextUtils;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/account")
@Slf4j
public class AccountController {

    @Autowired
    private AcsUserService acsUserService;

    @OperateLog(remark="'登录系统'")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public Object login(HttpServletRequest request,
                        @RequestParam("username") String username,
                        @RequestParam("password") String passwd) {
        if (StringUtils.isAnyBlank(username, passwd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        AcsUser loginUser = new AcsUser();
        loginUser.setName(username);
        loginUser.setPasswd(passwd);
        loginUser.setCurrLoginIp(ThreadContextUtils.getRemoteIp());
        loginUser.setCurrLoginTime(new Date());

        Result result = null;
        try {
            AcsUser user = acsUserService.login(loginUser);

            acsUserService.processSession(user, request.getSession());

            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#login] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark="'登出系统'")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public Object logout(HttpSession session) {
        Result result = null;
        try {

            acsUserService.logout();
            result = Result.success();
            session.invalidate();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#logout] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;

    }

    /**
     *  删除 session
     * @return 删除结果
     */
    @RequestMapping(value = "/invalidSession", method = RequestMethod.GET)
    public Object invalidSession() {
        return Result.toResult(ApiCode.INVALID_SESSION);
    }

    @RequestMapping(value = "/currentUser", method = RequestMethod.POST)
    public Object currentUser(HttpServletRequest request) {
        AcsUser user = MdcUtils.getCurrentAcsUser();
        CurrentUserInfo currentUserInfo = new CurrentUserInfo();
        currentUserInfo.setCompany(user.getCompany());
        currentUserInfo.setName(user.getName());
        currentUserInfo.setLastLoginTime(user.getLastLoginTime());
        currentUserInfo.setLastLoginIp(user.getLastLoginIp());
        currentUserInfo.setLastLogoutTime(user.getLastLogoutTime());
        currentUserInfo.setRole(user.getRole());

        Result result = Result.success(currentUserInfo);
        return result;
    }

    @OperateLog(remark = "'修改密码'")
    @RequestMapping(value = "/changePasswd", method = RequestMethod.POST)
    public Object changePasswd(HttpSession session,
                               @RequestParam("oldPasswd") String oldPasswd,
                               @RequestParam("newPasswd") String newPasswd) {
        if (StringUtils.isAnyBlank(oldPasswd, newPasswd)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserService.changePasswd(oldPasswd, newPasswd);
            result = Result.success();
            session.invalidate();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#changePasswd] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'创建用户' + #p1.name")
    @RequestMapping(value = "/createUser", method = RequestMethod.POST)
    public Object createUser(UserInfo userInfo) {
        Result result = null;
        try {
            ParamUtils.checkCreateUserInfo(userInfo);
            acsUserService.createUser(userInfo);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#createUser] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'修改用户信息，username:' + #p1.name + ', describ: ' + #p1.describ")
    @RequestMapping(value = "/modifyUser", method = RequestMethod.POST)
    public Object modifyUser(UserInfo userInfo) {
        if (userInfo.getUserId() == null
                || StringUtils.isAllBlank(userInfo.getName(), userInfo.getDescrib())) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserService.modifyUser(userInfo);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#modifyUser] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'删除用户信息，userId:' + #p1")
    @RequestMapping(value = "/deleteUser", method = RequestMethod.POST)
    public Object deleteUser(@RequestParam("userId") Integer userId,
                             @RequestParam(value = "passwd", required = false) String passwd) {
        if (userId == null || userId < 1) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (currUser.getRole() == RoleEnum.SUPER_ADMIN.role && !Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }

            acsUserService.deleteUser(userId);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#modifyUser] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }
    @OperateLog(remark = "'撤销删除用户信息，userId:' + #p1")
    @RequestMapping(value = "/undoDel", method = RequestMethod.POST)
    public Object undoDel(@RequestParam("userId") Integer userId,
                             @RequestParam(value = "passwd", required = false) String passwd) {
        if (userId == null || userId < 1) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            AcsUser currUser = MdcUtils.getCurrentAcsUser();
            if (currUser.getRole() == RoleEnum.SUPER_ADMIN.role && !Objects.equals(passwd, currUser.getPasswd())) {
                return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
            }
            acsUserService.undoDel(userId);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#undoDel] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'重置密码, userId:' + #p1.userId")
    @RequestMapping(value = "/resetPasswd", method = RequestMethod.POST)
    public Object resetPasswd(UserInfo userInfo) {
        if (userInfo.getUserId() == null
                || StringUtils.isAllBlank(userInfo.getPasswd())) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            acsUserService.resetPasswd(userInfo);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#resetPasswd] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     *  查询一个公司账户下的用户信息
     * @param company 公司名
     * @param status 状态
     * @param page 页码
     * @param size 每页数量
     * @return 用户信息
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object list(@RequestParam(value = "company", required = false) String company,
                       @RequestParam(value = "status", required = false) Integer status,
                       @RequestParam(value = "page", required = false) Integer page,
                       @RequestParam(value = "size", required = false) Integer size) {

        if ((page != null && page < 1) || (size != null && size < 1)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            Map<String, Object> data = acsUserService.search(company, status, page, size);
            result = Result.success(data);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[AccountController#list] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    @OperateLog(remark = "'查询用户系统管理员MQTT账户密码'")
    @RequestMapping(value = "/mqttUser", method = RequestMethod.GET)
    public Object mqttUser(HttpSession session,
                           @RequestParam("username") String username) {
        return acsUserService.getMqttUsernameAndPassword(username);
    }

    @OperateLog(remark = "'生成license'")
    @RequestMapping(value = "/license", method = RequestMethod.GET)
    public void createLicense(HttpServletResponse response,
                                @RequestParam("expireDate") String expireDate,
                                @RequestParam(value = "limiter", required = false) Integer limiter) throws IOException {

        acsUserService.createLicense(response, expireDate, limiter);
    }

    @OperateLog(remark = "'查询公司'")
    @RequestMapping(value = "/company", method = RequestMethod.GET)
    public Result company() {
        return Result.success(acsUserService.company());
    }

    @OperateLog(remark = "'查询租户管理员'")
    @RequestMapping(value = "/findTenantAdmin", method = RequestMethod.POST)
    public Result findTenantAdmin() {
        return Result.success(acsUserService.findTenantAdmin());
    }
}
