package com.jetron.nb.web.controller.log;

import com.jetron.nb.biz.service.AcsLogService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.LogFilter;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  日志控制器
 */
@RestController
@RequestMapping("/log")
@Slf4j
public class LogController {

    @Autowired
    private AcsLogService acsLogService;

    /**
     *  日志查询
     * @param filter 查询过滤条件
     * @param page 页码
     * @param size 每页数量
     * @return 日志查询结果
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Object list(
                        LogFilter filter,
                        @RequestParam(value = "page", required = false) Integer page,
                        @RequestParam(value = "size", required = false) Integer size) {

        Result result = null;
        try {
            Map<String, Object> data = acsLogService.seach(filter, page, size);
            result = Result.success(data);
        } catch (Exception e) {
            log.error("[LogController#list] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;

    }

    @RequestMapping(value = "/delList", method = RequestMethod.POST)
    public Object delList(@RequestBody List<Integer> ids) {
        acsLogService.delList(ids);
        return Result.success();
    }

    @RequestMapping(value = "/delByDate", method = RequestMethod.POST)
    public Object delByDate( Date startDate, Date endDate) {
        acsLogService.delByDate(startDate,endDate);
        return Result.success();
    }
}
