package com.jetron.nb.web.controller.file;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.biz.oss.FileClient;
import com.jetron.nb.biz.oss.FileClientFactory;
import com.jetron.nb.biz.oss.LocalFileClient;
import com.jetron.nb.biz.oss.ObsFileClient;
import com.jetron.nb.biz.service.FileService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.log.OperateLog;
import com.jetron.nb.common.util.DateUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.conf.impl.RedisServiceImpl;
import com.obs.services.ObsClient;
import com.obs.services.model.fs.ObsFSFile;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Date;

/**
 * 用于NMS系统文件下载
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    @Autowired
    private RedisServiceImpl redisService;

    @Autowired
    private LocalFileClient localFileClient;

    @Value("${app.output}")
    private String appDir;

    @Value("${app.deployType}")
    private String deployType;

    @Autowired
    private FileService fileService;

    @OperateLog(remark = "'文件下载, root: ' + #p2 + ', name: ' + #p3")
    @GetMapping(value = "/download/{root}/{name}")
    public Object fileDownload(HttpServletRequest request, HttpServletResponse response,
                               @PathVariable(value = "root") String root,
                               @PathVariable(value = "name") String name) {
        if (StringUtils.isBlank(root) || StringUtils.isBlank(name)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            if ("firmware".equals(root)) {
                fileService.chunkDownload(request, response, root, name);
            } else {
                if ("local".equals(deployType)) {
                    FileClientFactory.getFileClient("local").download(response, root + "/" + name, name);
                } else {
                    FileClientFactory.getFileClient("net").download(response, root + "/" + name, name);
                }
            }
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[FileController#fileDownload] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        log.info("fileDownload, result: {}", JSON.toJSONString(result));
        return "success";
    }

    @OperateLog(remark = "'证书文件下载, root: ' + #p2 + ', name: ' + #p3")
    @GetMapping(value = "/downloadCert/{root}/{name}")
    public Object fileDownload(HttpServletResponse response,
                               @PathVariable(value = "root") String root,
                               @PathVariable(value = "name") String name,
                               @RequestParam(value = "timestamp") Long timestamp,
                               @RequestParam(value = "id") String uuid) {
        if (StringUtils.isBlank(root) || StringUtils.isBlank(name)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        String s = redisService.get(String.valueOf(timestamp));
        Date date2 = new Date(timestamp);
        if (DateUtils.diffMinute(new Date(), date2) >= 5 || StringUtils.isEmpty(s) || !s.equals(uuid)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            localFileClient.download(response, root + "/" + name, name);
            result = Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[FileController#fileDownload] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        log.info("fileDownload, result: {}", JSON.toJSONString(result));
        return "success";
    }
}
