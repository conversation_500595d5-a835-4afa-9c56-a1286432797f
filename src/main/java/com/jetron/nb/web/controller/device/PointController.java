package com.jetron.nb.web.controller.device;

import com.alibaba.fastjson.JSONArray;
import com.jetron.nb.biz.service.AcsDeviceFlowService;
import com.jetron.nb.biz.service.AcsDriverUpgradeService;
import com.jetron.nb.biz.service.AcsPointService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.AcsBaseDev;
import com.jetron.nb.dal.po.UserDeviceFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/device/point")
@Slf4j
public class PointController {

    @Autowired
    private AcsPointService service;
    @Autowired
    private AcsDriverUpgradeService driverUpgradeService;


    @RequestMapping(value = "/deviceList", method = RequestMethod.POST)
    public Object getMonthFlow(UserDeviceFilter filter,
                               @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                               @RequestParam(value = "size", required = false,defaultValue = "10") Integer size) {
        return Result.success(service.page(page,size,filter));
    }

    /**
     * 设备下挂子设备信息
     *
     * @param id 设备ID
     * @return 设备下挂子设备信息
     */
    @RequestMapping(value = "/userBaseDevice", method = RequestMethod.POST)
    public Object userBaseDevice(@Param("id") Integer id) {
        Result result = null;
        try {
            List<AcsBaseDev> data = service.getBaseDevice(id);
            result = Result.success(data);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceController#userBaseDevice] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Result upload(
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam("file") MultipartFile file) {
        if (deviceId == null || file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            return service.upload(deviceId,file);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }


    @RequestMapping(value = "/uploadDev", method = RequestMethod.POST)
    public Result uploadDev(
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam("devName") String devName,
            @RequestParam("file") MultipartFile file) {
        if (deviceId == null || file == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            return service.uploadDev(deviceId,devName,file);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/downloadJson", method = RequestMethod.GET)
    public Result downloadJson(
            HttpServletResponse resp,
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam(value = "devName",required = false) String devName) {
        if (deviceId == null) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            service.downloadJson(resp,deviceId);
            return Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }


    @RequestMapping(value = "/upgradeDriver", method = RequestMethod.POST)
    public Result upgradeDriver(
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam(value = "devName",required = false) String devName,
            @RequestParam("driverId") String driverStr) {
        if (deviceId == null  ||StringUtils.isBlank(driverStr)) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            return service.upgradeDriver(deviceId,devName,driverStr);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/forceComplete", method = RequestMethod.POST)
    public Result forceComplete(
            @RequestParam("deviceId") Integer deviceId) {
        Result result = null;
        try {
            driverUpgradeService.forceComplete(deviceId);
            return Result.success();
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }


    @RequestMapping(value = "/pointKey", method = RequestMethod.POST)
    public Result pointKey(
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam("devName") String devName) {
        if (deviceId == null || devName == null ) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            return service.pointKey(deviceId,devName);
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    @RequestMapping(value = "/pointTable", method = RequestMethod.POST)
    public Result pointTable(
            @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false,defaultValue = "10") Integer size,
            @RequestParam("deviceId") Integer deviceId,
            @RequestParam("devName") String devName) {
        if (deviceId == null || devName == null ) {
            return Result.toResult(ApiCode.BAD_REQUEST);
        }
        Result result = null;
        try {
            return service.pointTable(page,size,deviceId,devName);
        } catch (Exception e) {
            log.error("exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

}
