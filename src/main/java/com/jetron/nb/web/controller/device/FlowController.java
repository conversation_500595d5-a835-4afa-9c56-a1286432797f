package com.jetron.nb.web.controller.device;


import com.jetron.nb.biz.service.AcsDeviceFlowHourService;
import com.jetron.nb.biz.service.AcsDeviceFlowMonthService;
import com.jetron.nb.biz.service.AcsDeviceFlowService;
import com.jetron.nb.biz.service.AcsUserDeviceService;
import com.jetron.nb.biz.service.mqtt.AcsMqttOnlineService;
import com.jetron.nb.common.constant.ApiCode;
import com.jetron.nb.common.constant.RoleEnum;
import com.jetron.nb.common.exception.IotException;
import com.jetron.nb.common.util.MdcUtils;
import com.jetron.nb.common.vo.Result;
import com.jetron.nb.dal.po.*;
import com.jetron.nb.dal.po.mqtt.AcsMqttOnline;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备流量控制器
 */
@RestController
@RequestMapping("/device/flow")
@Slf4j
public class FlowController {
    @Autowired
    private AcsDeviceFlowService acsDeviceFlowService;

    @Autowired
    private AcsDeviceFlowHourService hourService;
    @Autowired
    private AcsMqttOnlineService mqttOnlineService;
    @Autowired
    private AcsDeviceFlowMonthService monthService;
    @Autowired
    private AcsUserDeviceService userDeviceService;

    /**
     * 按月获取设备流量
     *
     * @param sn 设备SN
     * @return 流量信息
     */
    @RequestMapping(value = "/getDeviceFlow", method = RequestMethod.POST)
    public Object getMonthFlow(@RequestParam("sn") String sn,
                               @RequestParam("start") Date start,
                               @RequestParam("end") Date end) {

        Result result = null;
        try {
            AcsDeviceFlow param = new AcsDeviceFlow();
            param.setSn(sn);
            param.setStart(start);
            param.setEnd(end);
            List<AcsDeviceFlow> flowInfos = acsDeviceFlowService.findTrueList(param);
            result = Result.success(flowInfos);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceFlowController#getMonthFlow] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 按月获取设备流量
     *
     * @param sn 设备SN
     * @return 流量信息
     */
    @RequestMapping(value = "/hour/getDeviceFlow", method = RequestMethod.POST)
    public Object getHourFlow(@RequestParam("sn") String sn,
                              @RequestParam("start") Date start,
                              @RequestParam("end") Date end) {

        Result result = null;
        try {
            List<AcsDeviceFlow> flowInfos = hourService.findTrueList(sn, start, end);
            result = Result.success(flowInfos);
        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceFlowController#getMonthFlow] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }

        return result;
    }

    /**
     * 按月获取用户流量
     *
     * @param userId 用户ID
     * @param month  月份
     * @return 流量信息
     */
    @RequestMapping(value = "/getUserFlow", method = RequestMethod.POST)
    public Object getUserFlow(@RequestParam(value = "userId") Integer userId,
                              @Param("month") String month) {
        if (Objects.isNull(userId) || StringUtils.isAnyBlank(month)) {
            throw new IotException(ApiCode.BAD_REQUEST);
        }

        Result result = null;
        try {
            List<UserFlow> flowInfos = acsDeviceFlowService.getUserFlow(userId, month);
            result = Result.success(flowInfos);

        } catch (IotException ioe) {
            result = Result.toResult(ioe.getApiCode(), ioe.getData());
        } catch (Exception e) {
            log.error("[DeviceFlowController#getUserFlow] exception, message: {}", e);
            result = Result.toResult(ApiCode.SERVER_ERROR);
        }
        return result;
    }

    /**
     * 按公司统计设备每周流量
     *
     * @param company 公司名
     * @return 流量结果
     */
    @RequestMapping(value = "/flowStatistics", method = RequestMethod.POST)
    public Object flowStatistics(@RequestParam("company") String company) {
        return acsDeviceFlowService.weeklyFlow(company);
    }

    /**
     * 按公司统计设备上报的内存、CPU等数据
     *
     * @param company 公司名
     * @return 设备上报的内存、CPU使用率数据信息
     */
    @RequestMapping(value = "/compatureDataStatistics", method = RequestMethod.POST)
    public Object compatureDataStatistics(@RequestParam("company") String company) {
        return acsDeviceFlowService.dataLineChart(company);
    }

    /**
     * 大屏看板，流量统计
     *
     * @return 流量结果
     */
    @RequestMapping(value = "/flowStatisticsOfDashboard", method = RequestMethod.POST)
    public Object flowStatisticsOfDashboard() {
        return acsDeviceFlowService.flowStatisticsOfDashboard();
    }

    /**
     * 设备管理，设备详情页图表
     *
     * @return 流量结果
     */
    @RequestMapping(value = "/deviceDataLineChart", method = RequestMethod.POST)
    public Object deviceDataLineChart(@RequestParam("sn") String sn,
                                      @RequestParam("start") Date start,
                                      @RequestParam("end") Date end) {
        return acsDeviceFlowService.deviceDataLineChart(sn, start, end);
    }


    /**
     * 大屏看板，流量统计     *
     * @return 流量结果
     */
    @RequestMapping(value = "/flowEarlyWarn", method = RequestMethod.POST)
    public Object flowEarlyWarn(){
        return acsDeviceFlowService.flowEarlyWarn();
    }

    /**
     * 大屏看板，流量统计     *
     * @return 流量结果
     */
    @RequestMapping(value = "/flowEarlyWarn2", method = RequestMethod.POST)
    public Object flowEarlyWarn2(){
        return acsDeviceFlowService.flowEarlyWarn2();
    }

    /**
     * 获得大屏看板标题
     * @return 流量结果
     */
    @RequestMapping(value = "/query/kanbanTitle", method = RequestMethod.POST)
    public Object kanbanTitle(@RequestParam(value = "visitorId") String visitorId){
        return acsDeviceFlowService.kanbanTitleQuery(visitorId);
    }

    /**
     *  更改大屏看板标题
     * @return 流量结果
     */
    @RequestMapping(value = "/save/kanbanTitle", method = RequestMethod.POST)
    public Object saveKanbanTitle(@RequestParam(value = "visitorId") String visitorId,
                                  @RequestParam(value = "title") String title){
        return acsDeviceFlowService.kanbanTitleSave(visitorId,title);
    }


    /**
     * 设备上下线表格
     * @return
     */
    @RequestMapping(value = "/onOffLineTable", method = RequestMethod.POST)
    public Object onOffLineTable(@RequestParam(value = "start", required = false) Date start,
                                 @RequestParam(value = "end", required = false) Date end,
                                 @RequestParam(value = "sn", required = false) String sn,
                                 @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                                 @RequestParam(value = "size", required = false,defaultValue = "20") Integer size) {
        //return Result.success(acsDeviceFlowService.onOffLineTable(page,size,sn,start, end));
        AcsMqttOnline param = new AcsMqttOnline();
        param.setSn(sn);
        param.setStart(start);
        param.setEnd(end);
        return Result.success(mqttOnlineService.onOffLineTable(page,size,param));
    }

    @RequestMapping(value = "/deleteOnOffLine", method = RequestMethod.POST)
    public Object deleteOnOffLine(@RequestParam(value = "start") Date start,
                                  @RequestParam(value = "end") Date end,
                                  @RequestParam(value = "sn", required = false) String sn,
                                  @RequestParam(value = "passwd") String passwd  ){
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(passwd, currUser.getPasswd())) {
            return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
        }
        AcsMqttOnline param = new AcsMqttOnline();
        param.setSn(sn);
        param.setStart(start);
        param.setEnd(end);
        mqttOnlineService.delete(param);
        return Result.success();
    }

    @RequestMapping(value = "/month/page", method = RequestMethod.POST)
    public Object monthFlowPage(AcsDeviceFlowMonth month,
                                @RequestParam(value = "page", required = false,defaultValue = "1") Integer page,
                                @RequestParam(value = "size", required = false,defaultValue = "10") Integer size){
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (currUser.getRole() == RoleEnum.USER_ADMIN.role) {
            month.setCompany(currUser.getCompany());
        } else if (currUser.getRole() == RoleEnum.USER.role) {
            UserDeviceFilter deviceFilter = new UserDeviceFilter();
            deviceFilter.setBelongTo(MdcUtils.getCurrentAcsUser().getId());
            List<String> snList = userDeviceService.findListWithUser(deviceFilter)
                    .stream().map(AcsUserDevice::getSn).collect(Collectors.toList());
            if (snList.isEmpty()) {
                return Result.success();
            }
            month.setSnList(snList);
        }
        month.setPage(page);
        month.setSize(size);
        Map<String, Object> page1 = monthService.findPage(month);
        return Result.success(page1);
    }

    @RequestMapping(value = "/month/update", method = RequestMethod.POST)
    public Object monthUpdate(@RequestParam String id, @RequestParam Long flow, @RequestParam String passwd  ){
        AcsUser currUser = MdcUtils.getCurrentAcsUser();
        if (!Objects.equals(passwd, currUser.getPasswd())) {
            return Result.toResult(ApiCode.PASSWD_NOT_CORRECT);
        }
        monthService.update(id,flow);
        return Result.success();
    }

    @RequestMapping(value = "/flowCalculate", method = RequestMethod.POST)
    public Object deleteOnOffLine(){
        monthService.flowCalculate();
        return Result.success();
    }
}
