package com.jetron.nb.conf.impl;

import com.alibaba.fastjson.JSON;
import com.jetron.nb.conf.IRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class RedisServiceImpl implements IRedisService {

    @Autowired
    private RedisTemplate<String, ?> redisTemplate;

    @Override
    public boolean set(final String key, final String value) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            connection.set(serializer.serialize(key), serializer.serialize(value));
            return true;
        });
    }

    @Override
    public boolean set(final String key, final String value, int dbIndex) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            connection.select(dbIndex);
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            connection.set(serializer.serialize(key), serializer.serialize(value));
            return true;
        });
    }

    @Override
    public String get(final String key){
        return redisTemplate.execute((RedisCallback<String>) connection -> {
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            byte[] value =  connection.get(serializer.serialize(key));
            return serializer.deserialize(value);
        });
    }

    @Override
    public String get(final String key, int dbIndex){
        return redisTemplate.execute((RedisCallback<String>) connection -> {
            connection.select(dbIndex);
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            byte[] value =  connection.get(serializer.serialize(key));
            return serializer.deserialize(value);
        });
    }

    @Override
    public boolean expire(final String key, long expire) {
        return Boolean.TRUE.equals(redisTemplate.expire(key, expire, TimeUnit.SECONDS));
    }

    @Override
    public boolean expire(final String key, int dbIndex, long expire) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            connection.select(dbIndex);
            return connection.expire(key.getBytes(), expire);
        });
    }

    @Override
    public <T> boolean setList(String key, List<T> list) {
        String value = JSON.toJSONString(list);
        return set(key,value);
    }

    @Override
    public <T> List<T> getList(String key,Class<T> clz) {
        String json = get(key);
        if(json!=null){
            return JSON.parseArray(json, clz);
        }
        return null;
    }

    @Override
    public List<String> searchKeys(final String key, int dbIndex) {
        return redisTemplate.execute((RedisCallback<List<String>>) connection -> {
            connection.select(dbIndex);
            Set<byte[]> redisKeys = connection.keys(key.getBytes());
            List<String> keysList = new ArrayList<>();
            for (byte[] data : redisKeys) {
                keysList.add(new String(data, 0, data.length));
            }
            return keysList;
        });
    }

    @Override
    public long lPush(final String key, Object obj) {
        final String value = JSON.toJSONString(obj);
        return redisTemplate.execute((RedisCallback<Long>) connection -> {
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            return connection.lPush(serializer.serialize(key), serializer.serialize(value));
        });
    }

    @Override
    public long rPush(final String key, Object obj) {
        final String value = JSON.toJSONString(obj);
        return redisTemplate.execute((RedisCallback<Long>) connection -> {
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            return connection.rPush(serializer.serialize(key), serializer.serialize(value));
        });
    }

    @Override
    public String lPop(final String key) {
        return redisTemplate.execute((RedisCallback<String>) connection -> {
            RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
            byte[] res =  connection.lPop(serializer.serialize(key));
            return serializer.deserialize(res);
        });
    }

    @Override
    public boolean delete(final String key, int dbIndex) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            connection.select(dbIndex);
            if(connection.exists(key.getBytes())) {
                long del = connection.del(key.getBytes());
                return true;
            }
            return false;
        });
    }

    @Override
    public boolean exist(String key, int dbIndex) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            connection.select(dbIndex);
            return connection.exists(key.getBytes());
        });
    }

}
