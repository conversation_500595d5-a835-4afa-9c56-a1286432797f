package com.jetron.nb.conf;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

import javax.annotation.Resource;

@Configuration
@EnableRedisHttpSession
public class RedisConfig extends CachingConfigurerSupport {

    @Resource
    private LettuceConnectionFactory factory;

    @Bean
    public RedisTemplate<?, ?> redisTemplate() {
        RedisTemplate<?, ?> redisTemplate = new RedisTemplate();
        // 关闭共享链接
        factory.setShareNativeConnection(false);
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(redisKeySerializer());
        redisTemplate.setValueSerializer(redisValueSerializer());
        redisTemplate.setHashKeySerializer(redisKeySerializer());
        redisTemplate.setHashValueSerializer(redisValueSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisSerializer<?> redisKeySerializer() {
        return new StringRedisSerializer();
    }

    @Bean
    public RedisSerializer<?> redisValueSerializer() {
        return new MyRedisSerializer();
    }

    @Primary
    @Bean
    public RedisOperationsSessionRepository redisOperationsSessionRepository(RedisTemplate<Object, Object> redisTemplate) {
        RedisOperationsSessionRepository sessionRepository = new RedisOperationsSessionRepository(redisTemplate);
        // Optional: Set custom session timeout (default is 1800 seconds / 30 minutes)
        // sessionRepository.setDefaultMaxInactiveInterval(Duration.ofMinutes(60));
        // Optional: Set a custom Redis namespace (prefix for keys)
        // sessionRepository.setRedisKeyNamespace("myApp:custom:session");
        return sessionRepository;
    }

}
