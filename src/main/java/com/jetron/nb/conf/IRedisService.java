package com.jetron.nb.conf;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRedisService {
    /**
     * 插入数据
     * @param key redis key
     * @param value redis value
     * @return 是否插入成功
     */
    boolean set(String key, String value);

    /**
     * 用于插入数据到指定的数据库
     * @param key key
     * @param value value
     * @param dbIndex database index
     * @return boolean
     */
    boolean set(String key, String value, int dbIndex);

    /**
     *  获取value
     * @param key key 值
     * @return value值
     */
    String get(String key);

    /**
     * 在指定数据库读取数据
     * @param key key值
     * @param dbIndex 数据库序号
     * @return value值
     */
    String get(String key, int dbIndex);

    boolean expire(String key,long expire);

    /**
     *  在指定数据库设置key过期时间
     * @param key key值
     * @param dbIndex 数据库序号
     * @param expire 时间
     * @return boolean
     */
    boolean expire(String key, int dbIndex, long expire);

    <T> boolean setList(String key , List<T> list);

    <T> List<T> getList(String key,Class<T> clz);

    /**
     *  搜索相关的key列表
     * @param key 模糊key
     * @param dbIndex 数据库编号
     * @return 符合条件的key列表
     */
    List<String> searchKeys(String key, int dbIndex);

    long lPush(String key, Object obj);

    long rPush(String key, Object obj);

    String lPop(String key);

    /**
     *  删除特定数据库中的值
     * @param key key值
     * @param dbIndex 数据库序号
     * @return boolean
     */
    boolean delete(String key, int dbIndex);

    /**
     *  key 是否存在
     * @param key key值
     * @param dbIndex 数据库序号
     * @return boolean，存在返回true
     */
    boolean exist(String key, int dbIndex);

}
