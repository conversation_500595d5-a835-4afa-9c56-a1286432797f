<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
						http://www.springframework.org/schema/tx
						http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- ======================================================================== -->
    <!-- DataSource定义 -->
    <!-- ======================================================================== -->
    <bean id="log-filter" class="com.alibaba.druid.filter.logging.Slf4jLogFilter">
        <property name="resultSetLogEnabled" value="false"/>
        <property name="dataSourceLogEnabled" value="false"/>
        <property name="connectionLogEnabled" value="false"/>
        <property name="statementExecutableSqlLogEnable" value="true"/>
    </bean>

    <bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
        <property name="slowSqlMillis" value="1000"/>
        <property name="logSlowSql" value="true"/>
        <property name="mergeSql" value="true"/>
    </bean>

    <bean id="wall-filter" class="com.alibaba.druid.wall.WallFilter">
        <property name="config" ref="myWallConfig"/>
    </bean>

    <bean id="myWallConfig" class="com.alibaba.druid.wall.WallConfig">
        <property name="multiStatementAllow" value="true"/>
    </bean>

    <!-- data source -->
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="url" value="${jdbc.database.url}" />
        <property name="username" value="${jdbc.database.username}" />
        <property name="password" value="${jdbc.database.password}" />
        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver" />

        <property name="maxActive" value="${jdbc.database.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="3000"/>
        <property name="minIdle" value="5"/>

        <property name="connectionInitSqls" value="set names utf8mb4" />

        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="minEvictableIdleTimeMillis" value="1200000"/>

        <property name="validationQuery" value="SELECT 1"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxOpenPreparedStatements" value="100"/>

        <property name="proxyFilters">
            <list>
                <ref bean="log-filter"/>
                <ref bean="wall-filter"/>
                <ref bean="stat-filter"/>
            </list>
        </property>
    </bean>

    <!-- session factory -->
    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="mapperLocations" value="classpath:mapper/*.xml"/>
        <property name="plugins">
            <array>
                <ref bean="mybatisPlusInterceptor"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.jetron.nb.dal.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <bean id="mybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
        <property name="interceptors">
            <list>
                <ref bean="paginationInnerInterceptor"/>
            </list>
        </property>
    </bean>

    <bean id="paginationInnerInterceptor" class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
        <!-- 对于单一数据库类型来说,都建议配置该值,避免每次分页都去抓取数据库类型 -->
        <constructor-arg name="dbType" value="MYSQL"/>
    </bean>
</beans>
