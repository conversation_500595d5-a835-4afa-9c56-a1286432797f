<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="mqttClientManager" class="com.jetron.nb.biz.mqtt.MqttClientManager">
        <constructor-arg name="broker" value="${mqtt.broker}" />
        <constructor-arg name="username" value="${mqtt.username}" />
        <constructor-arg name="password" value="${mqtt.passwd}" />
        <constructor-arg name="mqttDir" value="${mqtt.dir}" />
        <property name="qos" value="2" />
        <property name="retry" value="2" />
    </bean>

</beans>
