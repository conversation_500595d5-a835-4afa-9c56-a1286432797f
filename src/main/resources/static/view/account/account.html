<div id="account">

</div>
<div class="detail-popover fade" id="deviceDetail">
    <div class="panel panel-default">
        <div class="panel-heading">
            <span class="title">流量统计</span>
            <span class="close">X</span>
        </div>
        <div class="panel-body">
            <div class="input-group">
                <input type="text" class="form-control" aria-describedby="basic-addon2" readonly="readonly">
                <span class="input-group-addon fa fa-calendar" id="basic-addon2"></span>
            </div>
            <div id="main" style="width: 100%;height:300px;">

            </div>
        </div>
    </div>
</div>
<div id="addUser" class="hide">
    <form class="form-horizontal" novalidate>
        <div class="form-group">
            <label for="name" class="col-sm-3 control-label required">{username}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required email" id="name" placeholder="{email}">
            </div>
        </div>
        <div class="form-group"><label for="passwd" class="col-sm-3 control-label required">{password}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="passwd" readonly="readonly" nocheck>
            </div>
        </div>
        <div class="form-group">
            <label for="company" class="col-sm-3 control-label required">{company}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="company" maxlength="64">
            </div>
        </div>
        <div class="form-group">
            <label for="describ" class="col-sm-3 control-label">{describ}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="describ" maxlength="64">
            </div>
        </div>
        <div class="form-group">
            <label for="mqttUser" class="col-sm-3 control-label">{mqttUser}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="mqttUser" maxlength="64" placeholder="当前账号不能查看">
            </div>
        </div>
        <div class="form-group">
            <label for="mqttPassword" class="col-sm-3 control-label">{mqttPassword}</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="mqttPassword" maxlength="64" placeholder="当前账号不能查看">
            </div>
        </div>
        <div class="form-group">
            <label for="vpnHost" class="col-sm-3 control-label required">{vpnHost}</label>
            <div class="col-sm-6 has-feedback has-warning">
                <input type="text" class="form-control required" id="vpnHost" maxlength="64">
                <span class="form-control-feedback fa fa-warning"></span>
            </div>
            <i class="warning">{modify}</i>
        </div>
        <div class="form-group">
            <label for="vpnPort" class="col-sm-3 control-label required">{vpnPort}</label>
            <div class="col-sm-6 has-feedback has-warning">
                <input type="number" class="form-control required number" id="vpnPort" min="1" max="65535" placeholder="1~65535">
                <span class="form-control-feedback fa fa-warning"></span>
            </div>
            <i class="warning">{modify}</i>
        </div>
        <div class="form-group">
            <label for="multilayer" class="col-sm-3 control-label required">{multilayer}</label>
            <div class="col-sm-6 has-feedback has-warning">
                        <select name="pets" class="form-control required" id="multilayer">
                          <option value="1">二层</option>
                          <option value="0">三层</option>
                         </select>
                <span class="form-control-feedback fa fa-warning"></span>
            </div>
            <i class="warning">{modify}</i>
        </div>

    </form>
</div>

<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password2" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/account/account.js?v=0b3b08dadf"></script>
<script type="text/javascript" src="view/common/flow.js?v=a2c5845ac9"></script>
<!--<script type="text/javascript" src="view/account/account.js?v=0b3b08dadf"></script>-->