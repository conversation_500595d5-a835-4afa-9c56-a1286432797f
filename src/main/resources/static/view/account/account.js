!function () {
    var o, r, s, i, c = $("#account"), m = Frame.INFO.users;

    function e() {
        var e = {
            url: "account/list",
            pagination: !0,
            columns: [{
                name: "name", title: m.username, format: function (e) {
                    return 0 != Frame.Default.role ? e : "<a>" + e + "</a>"
                }, action: a
            }, {name: "company", title: m.company, role: "0"}, {
                name: "status",
                title: m.status,
                format: l
            }, {name: "del", title: m.del,format:delFormat},
                {name: "lastLoginIp", title: m.lastLoginIp}, {
                name: "lastLoginTime",
                title: m.lastLoginTime,
                format: Frame.Time.formatTime
            }, {name: "gmtCreate", title: m.gmtCreate, format: Frame.Time.formatTime}, {
                name: "describ",
                title: m.describ
            }, {name: "vpnHost", title: m.vpnHost, role: "0"}, {name: "vpnPort", title: m.vpnPort, role: "0"},
                {name: "multilayer", title: m.multilayer, format:is<PERSON><PERSON><PERSON><PERSON>, role: "0"}],
            operations: [{name: "edit", action: u}, {name: "remove", action: p},

                {
                name: "reset",
                icon: {icon: "fa fa-key", title: m.resetPass},
                action: n
                },
                //{name: "mqtt", action:mqtt },
                {name: "restart2",  icon: {icon: "fa fa-repeat", title: m.recovery},action: undoDel},
                ],
            toolbar: {
                search: [{
                    name: "input",
                    placeholder: Frame.INFO.search.company,
                    action: f,
                    role: "0"
                }, {
                    name: "select", content: function t() {
                        var e = {0: m.freeze, 1: m.normal}, a = "<option value='all'>" + m.allStatus + "</option>";
                        for (key in e) a += "<option value='" + key + "'>" + e[key] + "</option>";
                        return a
                    }(), action: f
                }], operations: [{name: "add", action: d}]
            },
            onSelectPage: function (e) {
                f({size: e})
            },
            onChangePageSize: function (e) {
                f({page: e})
            }
        };
        o = $("#account").datagrid(e)
    }

    function a(e) {
        e.find("a").on("click", function () {
            var e = $(this).closest("tr").data("data");
            $("#deviceDetail").removeClass("out").addClass("in").css({visibility: "visible"}), $("#deviceDetail").show(), $("#device").hide(), $("#main").data("id", e.id);
            var a = Frame.Time.getDay(0, "Y-M");
            laydate.render({
                elem: ".input-group input",
                btns: ["now", "confirm"],
                min: "1970-01-01",
                max: a,
                type: "month",
                value: a,
                lang: Frame.Default.lang,
                done: function (e) {
                    t(e)
                }
            }), t(a)
        })
    }

    function t(e) {
        var a = {userId: $("#main").data("id"), month: e}, c = myEchart.getMonth(e);
        Frame.getRequest({
            url: "device/flow/getUserFlow", data: a, onSuccess: function o(e) {
                var a = function i(e) {
                    for (var a = {}, t = [], n = 0; n < e.length; n++) {
                        var o = e[n];
                        a[o.date] = o
                    }
                    for (var r = 0; r < c.length; r++) {
                        var s = a[c[r]] || {};
                        t.push(Math.round(s.flow / 1024 * 100) / 100)
                    }
                    return t
                }(e);
                r = echarts.init(document.getElementById("main"));
                var t = myEchart["default"],
                    n = {series: [{name: "流量", type: "bar", areaStyle: {}, data: a}], xAxis: {data: c}};
                $.extend(t, n), delete t.title, r.setOption(t)
            }
        })
    }

    function n(e) {
        var a = Frame.randomPass(), t = m.confirmPass.replace("%pass", a);
        debugger;
        Frame.Msg.confirm({
            content: t, onConfirm: function n() {
                Frame.setRequest({url: "account/resetPasswd", data: {userId: e.id, passwd: a}, onSuccess: f})
            }
        })
    }

    function l(e) {
        return 0 == e ? '<div class="abnormal"><b class="fa fa-times-circle"></b><span>' + m.freeze + "</span></div>" : '<div class="normal"><b class="fa fa-check-circle"></b><span>' + m.normal + "</span></div>"
    }

    function delFormat(e){
        return 0 == e ? m.normal : m.del;
    }

    function isMultilayer(e){
        return (e == null || e == "") ? "" : "1" == e  ? "二层" : "三层";
    }

    function d() {
        $("i.warning", s).html(m.careAdd);
        var e = Frame.randomPass();
        $("#passwd", s).closest(".form-group").show(),
            $("#company", s).closest(".form-group").show(),
            $("#describ", s).closest(".form-group").show(),
            $("#vpnHost", s).closest(".form-group").show(),
            $("#vpnPort", s).closest(".form-group").show(),
            $("#name", s).closest(".form-group").show(),
            $("#mqttUser", s).closest(".form-group").show(),
            $("#mqttPassword", s).closest(".form-group").show(),
            $("#multilayer", s).closest(".form-group").show(),
            Frame.Msg.prompt({
            title: m.addUser,
            content: s,
            data: {passwd: e},
            onConfirm: function a() {
                var e = Frame.getFormData(s);
                // e.passwd = md5(e.passwd),
                e.company = e.company || Frame.Default.user.company || "", Frame.setRequest({
                    url: "account/createUser",
                    data: e,
                    onSuccess: f
                })
            }
        })
    }

    function u(a) {
        $("i.warning", s).html(m.careMdf),
            $("#passwd", s).closest(".form-group").hide(),
            $("#company", s).closest(".form-group").hide(),
            $("#describ", s).closest(".form-group").show(),
            $("#vpnHost", s).closest(".form-group").show(),
            $("#vpnPort", s).closest(".form-group").show(),
            $("#name", s).closest(".form-group").show(),
            $("#mqttUser", s).closest(".form-group").hide(),
            $("#mqttPassword", s).closest(".form-group").hide(),
            $("#multilayer", s).closest(".form-group").show(),
            Frame.Msg.prompt({
            title: m.editUser,
            content: s,
            data: a,
            onConfirm: function t() {
                var e = Frame.getFormData(s);
                e.passwd = md5(e.passwd), e.userId = a.id, e.company = e.company || Frame.Default.user.company || "", Frame.setRequest({
                    url: "account/modifyUser",
                    data: e,
                    onSuccess: f
                })
            }
        })
    }
//
    function mqtt(e) {
        // $.get( "account/mqttUser",{username : e.name},);}
        $.ajax({
            type: 'get',
            datatype: 'json',
            url: 'account/mqttUser?username='+e.name,
            success: function (data) {
                console.log(data)

                $("i.warning", s).html(m.mqttUser),
                    $("#passwd", s).closest(".form-group").hide(),
                    $("#company", s).closest(".form-group").hide(),
                    $("#describ", s).closest(".form-group").hide(),
                    $("#vpnHost", s).closest(".form-group").hide(),
                    $("#vpnPort", s).closest(".form-group").hide(),
                    $("#name", s).closest(".form-group").hide(),
                    $("#mqttUser", s).closest(".form-group").show(),
                    $("#mqttPassword", s).closest(".form-group").show(),
                    Frame.Msg.prompt({
                        title: m.mqttUser,
                        content: s,
                        data: data,
                        onConfirm: function t() {
                        }
                    })


            }
        })
        }

    function p(a) {
        function t() {
            var e = $("#password", i).val();
            Frame.setRequest({url: "account/deleteUser", data: {userId: a.id, passwd: md5(e)}, onSuccess: f})
        }

        Frame.Msg.confirm({
            content: m.removeUser, onConfirm: function () {
                Frame.getPrivilege("0") ? function e() {
                    Frame.Msg.prompt({
                        title: m.rePass,
                        content: i,
                        data: {username: Frame.Default.user.name},
                        onConfirm: t
                    })
                }() : t()
            }
        })
    }

    function undoDel(a){
        if (1 == a.status &&   0 == a.del ){
            Frame.Msg.error("fail");
            return ;
        }
        function t() {
            var e = $("#password", i).val();
            Frame.setRequest({url: "account/undoDel", data: {userId: a.id, passwd: md5(e)}, onSuccess: f})
        }

        Frame.Msg.confirm({
            content: m.recoveryUser, onConfirm: function () {
                Frame.getPrivilege("0") ? function e() {
                    Frame.Msg.prompt({
                        title: m.rePass,
                        content: i,
                        data: {username: Frame.Default.user.name},
                        onConfirm: t
                    })
                }() : t()
            }
        })
    }

    function f(e) {
        var a = {page: 1, size: $(".toolbar select.pagenum", c).val()}, t = $(".filter input", c).val();
        t && (a.company = t);
        var n = $(".filter select", c).val();
        "all" != n && (a.status = n), e && $.extend(a, e), o.refresh(a)
    }

    function v() {
        e(), function n() {
            var e = $("#addUser").html(), a = {
                "{username}": m.username,
                "{password}": m.password,
                "{company}": m.company,
                "{describ}": m.describ,
                "{vpnHost}": m.vpnHost,
                "{vpnPort}": m.vpnPort,
                "{email}": m.email,
                "{modify}": m.careMdf,
                "{mqttUser}":m.mqttUser,
                "{mqttPassword}":m.mqttPassword,
                "{multilayer}": m.multilayer,
            };
            e = Frame.replaceTitle(e, a), s = $(e), Frame.getPrivilege("0") || ($("#vpnPort", s).closest(".form-group").remove(), $("#vpnHost", s).closest(".form-group").remove(), $("#company", s).closest(".form-group").remove()), $("#addUser").empty().remove();
            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}": m.againPass}), i = $(t), $("#confirm-pass").empty().remove()
        }(),

            $(".title").html(Frame.INFO.echarts.title), $("#deviceDetail .close").on("click", function () {
            $("#deviceDetail").addClass("out").removeClass("in").css({visibility: "hidden"}), $("#deviceDetail").hide(), $("#device").show(), $("#deviceDetail .device-info").empty(), r && r.clear()
        })
    }

    Frame.destory = function () {
        o && o.destory(), m = s = null, function e() {
            r && r.clear(), echarts && echarts.dispose(document.getElementById("main"))
        }(), $("#deviceDetail .close").off("click"), myEchart = null
    }, $(function (e) {
        v()
    })
}();