!function () {
    var table, r = Frame.INFO.device;
    var e = {
        init: function i(div, param) {
            function page(param1,param2){
                let sn = $(".filter input[name='sn']", div).val();
                let alias = $(".filter input[name='alias']", div).val();
                let model = $(".filter select", div).eq(0).val();
                var a = {page: 1, size: $(".toolbar select.pagenum", div).val(),orderByColumn:"alias"};
                sn && (a.snLike = sn);
                alias && (a.alias = alias);
                "all" != model && (a.model = model);
                param && $.extend(a, param);
                param1 && $.extend(a, param1);
                param2 && $.extend(a, param2);
                table.refresh(a)
            };
            !function t(e, n) {
                var i = {
                    url: "device/userDeviceList",
                    data: {page: 1, size: 20,orderByColumn:"alias"},
                    pagination: !0,
                    multiSelect: !0,
                    columns: [
                        {name: "name", title: r.devname},
                        {name: "alias", title: r.alias,orderBy:true},
                        {name: "sn", title: r.sn},
                        {name: "verNo",title: r.version
                    }],
                    onSelectPage: function (size) {
                        let a = {size: size};
                        page(a)
                    },
                    onChangePageSize: function (pageNum) {
                        let a = {page: pageNum};
                        page(a)
                    },
                    toolbar: {
                        search: [
                            {name: "input",name2:"sn", placeholder: Frame.INFO.search.sn, action: page}
                            ,{name: "input",name2:"alias", placeholder: Frame.INFO.search.alias, action: page}
                            ,{name: "select", content: Frame.createModel(!0), action: page}
                        ],
                    },
                };
                $.extend(i.data,n);
                table = e.datagrid(i)
            }(div, param);
        },
        getDeviceID: function t() {
            for (var e = table.getSelectedData(), n = [], i = 0; i < e.length; i++) n.push(e[i].id);
            return n.join(",")
        },
        getDeviceSn: function t() {
            for (var e = table.getSelectedData(), n = [], i = 0; i < e.length; i++) n.push(e[i].sn);
            return n.join(",")
        },
        destory: function n() {
            r = table = null
        }
    };
    window.Table = e
}();