!function () {
    var i, s, l = $("#alarm"),a = Frame.INFO.alarm,queryText = Frame.INFO.queryText;;

    function query(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", l).val()};
        var sn = $(".filter input", l).eq(0).val();
        var alias = $(".filter input", l).eq(1).val();
        var position = $(".filter input", l).eq(2).val();
        var n = $(".filter input:last-child", l).val().split(" ~ ");
        var a = $(".filter select", l).eq(0).val();
        let status = $(".filter select", l).eq(1).val();
        let parameterContent = $(".filter select", l).eq(2).val();

        Frame.Util.isNotBlank(sn) && (t.snEq = sn);
        Frame.Util.isNotBlank(alias) && (t['acsUserDevice.alias'] = alias);
        Frame.Util.isNotBlank(position) && (t['acsUserDevice.position'] = position);
        2 == n.length && (t.start = n[0], t.end = n[1]);
        "all" != a && (t.parameterKey = a);
        t.alarmStatus = status
        "" != parameterContent && (t.parameterContent = parameterContent);
        e && $.extend(t, e), i.refresh(t)
    }

    function delList() {
        let start, end = null;
        let param = {};
        var element = $(".filter input:last-child", l).val().split(" ~ ");
        if (2 == element.length) {
            start = element[0];
            end = element[1]
        }
        if (start == undefined || start == null
            || end == undefined || end == null) {
            Frame.Msg.error("请选择删除时间区间");
            return;
        }
        param.startDate = new Date(Date.parse(start));
        param.endDate = new Date(Date.parse(end));
        Frame.Msg.confirm({
            content: `确认要删除${start} - ${end}(不包含) 数据吗？`,
            onConfirm: function i() {
                Frame.setRequest({
                    url: "alarm/delByDate",
                    data: param,
                    onSuccess: query
                })
            }
        })
    }

    function excel(e) {
        var t = {};
        var sn = $(".filter input", l).eq(0).val();
        var alias = $(".filter input", l).eq(1).val();
        var position = $(".filter input", l).eq(2).val();
        var n = $(".filter input:last-child", l).val().split(" ~ ");
        var a = $(".filter select", l).eq(0).val();
        let status = $(".filter select", l).eq(1).val();
        let parameterContent = $(".filter select", l).eq(2).val();

        Frame.Util.isNotBlank(sn) && (t.snEq = sn);
        Frame.Util.isNotBlank(alias) && (t['acsUserDevice.alias'] = alias);
        Frame.Util.isNotBlank(position) && (t['acsUserDevice.position'] = position);
        2 == n.length && (t.start = n[0], t.end = n[1]);
        "all" != a && (t.parameterKey = a);
        t.alarmStatus = status
        "" != parameterContent && (t.parameterContent = parameterContent);
        e && $.extend(t, e);
        Frame.Util.downloadFile("alarm/excel",t,"post");
    }

    function t() {
        var e = {
            url: "alarm/info",
            data:{alarmStatus:"1"},
            pagination: !0,
            columns: [
                {name: "id", title: "ID",role:"0"},
                {name: "parameterContent", title: a.parameterContent},
                {name: "alarmRank", title: a.alarmRank, format: rankFormatter},
                {name: "parameterValue", title: a.parameterValue},
                {name: "currentValue", title: a.currentValue},
                {name: "alarmStatus", title: a.alarmStatus, format: statusFormatter},
                {name: "parameterKey", title: a.parameterKey,},
                {name: "insertTime", title: a.insertTime, format: Frame.Time.formatTime},
                {name: "company", title: a.company},
                {name: "ip", title: a.ip},
                {name: "sn", title: a.sn},
                {name: "acsUserDevice.alias", title: a.alias},
                {name: "acsUserDevice.position", title: a.position},
            ],
            operations: [{name: "edit", action: update}],
            toolbar: {
                search: [
                    {name: "input", placeholder: Frame.INFO.search.sn,content: queryText.sn,action: query},
                    {name: "input", placeholder: Frame.INFO.search.alias,content: queryText.pos,action: query},
                    {name: "input", placeholder: Frame.INFO.search.position,content: queryText.position,action: query},
                    {name: "select", content: Frame.createModel(!0), action: query},
                    {name: "select", content: alarmStatus(), action: query},
                    {name: "select", content: alarmType(), action: query},
                    {
                        name: "input",
                        placeholder: Frame.Time.getDay(-7, "Y-M-D") + " ~ " + Frame.Time.getDay(0, "Y-M-D"),
                        action: query
                    }
                ],
                operations: [{name: "refresh", action: query}
                    , {name: "remove", action: delList, role: "0,1"}
                    , {name: "download", action: excel, role: "0,1"}
                    ]
            },
            onSelectPage: function (e) {
                query({size: e})
            },
            onChangePageSize: function (e) {
                query({page: e})
            }
        };
        i = l.datagrid(e);
        $("input:last-child").attr("readonly", !0), $("input:last-child").css({"background-color": "#fff"}), laydate.render({
            elem: $("input:last-child")[0],
            range: "~",
            btns: ["clear", "confirm"],
            min: "1970-01-01",
            theme: "molv",
            lang: Frame.Default.lang,
            done: function (e) {
                var t = e.split(" ~ ");
                "" == e ? query() : 2 == t.length && query({start: t[0], end: t[1]})
            }
        })
    }

    function alarmStatus() {
        let str = "<option value=''>未选择</option>";
        for (let key in a.statusObj) {
            if (key == 1) {
                str += `<option value="${key}" selected>${a.statusObj[key]}</option>`;
            } else {
                str += `<option value="${key}">${a.statusObj[key]}</option>`;
            }
        }
        return str;
    }

    function alarmType() {
        let user = Frame.Default.user;
        let param = {page:1,size:1000,company:null};
        if (user.role != 0) {
            param.company = user.company;
        }
        let str = "<option value=''>未选择</option>";
        let typeArray = [];
        Frame.setRequest({url: "parm/findList",data: param,async:false,onSuccess: function (data) {
            let records = data.records;
            if (records instanceof Array) {
                for (let j = 0; j < records.length; j++) {
                    let content = records[j].parameterContent;
                    if (typeArray.indexOf(content) == -1) {
                        str += `<option value="${content}">${content}</option>`;
                        typeArray.push(content);
                    }
                }
            }
        }});
        return str;
    }

    function v() {
        var e = $("#alarmForm").html();
        var o = {
            "{alarmStatus}": a.alarmStatus
        };
        e = Frame.replaceTitle(e, o);
        s = $(e);
        var str = "";
        for (let key in a.statusObj) {
            str += `<option value="${key}">${a.statusObj[key]}</option>`;
        }
        s.find("#alarmStatus").append(str);
        $("#alarmForm").empty().remove(), s.validateForm();
    }

    function update(o) {
        var data = {alarmId: o.id, alarmStatus: o.alarmStatus};
        $("#alarmId", s).closest(".form-group").show();
        $("#alarmStatus", s).closest(".form-group").show();
        Frame.Msg.prompt({
            title: a.edit,
            content: s,
            data: data,
            onConfirm: function t() {
                var e = Frame.getFormData(s);
                var d = {id: e.alarmId, alarmStatus: e.alarmStatus,
                    parameterCode:o.parameterCode,sn:o.sn,
                    currentValue:o.currentValue};

                Frame.setRequest({
                    url: "alarm/update",
                    data: d,
                    onSuccess: query
                })
            }
        })
    }

    function statusFormatter(value) {
        return a.statusObj[value];
    }

    function rankFormatter(value) {
        return a.rankObj[value];
    }

    Frame.destory = function () {
        i && i.destory(), a = s = l = queryText = null
    }, $(function (e) {
        t();
        v();
    })


}();