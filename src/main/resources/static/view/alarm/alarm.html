<div id="alarm">
    <style type="text/css" scoped>
        #alarm .toolbar select[class='form-control'],#alarm .toolbar input[class='form-control']{
            width: 20%;
        }
    </style>
</div>
<div id="alarmForm" class="hide">
    <form class="form-horizontal" novalidate>
        <div class="form-group">
            <label for="alarmId" class="col-sm-3 control-label required">id</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" readonly="readonly" id="alarmId" >
            </div>
        </div>
        <div class="form-group">
            <label for="alarmStatus" class="col-sm-3 control-label required">{alarmStatus}</label>
            <div class="col-sm-6">
                <select type="text" class="form-control required" id="alarmStatus" ></select>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/alarm/alarm.js?v=a1206b5fbb"></script>