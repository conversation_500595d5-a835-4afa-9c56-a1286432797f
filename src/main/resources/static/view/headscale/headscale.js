$(function() {
    var HeadscaleManager = {
        currentUser: null,
        currentACL: {
            groups: {},
            tagOwners: {},
            hosts: {},
            acls: [],
            tests: []
        },
        editingItem: null,
        editingIndex: -1,

        init: function() {
            this.bindEvents();
            this.loadUsers();
            this.loadDeviceStatus();
            this.loadConnectionStatus();
            // Initialize device data for when the tab is first clicked
            this.loadDevices();

            // Check if ACL tab is initially active and initialize it
            var self = this;
            setTimeout(function() {
                if ($('#myTab a[data-href="#acl-tab"]').parent().hasClass('active')) {
                    self.initializeACLTab();
                }
            }, 100);
        },
        
        bindEvents: function() {
            var self = this;
            
            // Test connection
            $('#testConnection').click(function() {
                self.testConnection();
            });
            
            // Add user
            $('#addUser').click(function() {
                $('#addUserModal').modal('show');
            });

            $('#confirmAddUser').click(function() {
                self.addUser();
            });

            // Add namespace
            $('#addNamespace').click(function() {
                $('#addNamespaceModal').modal('show');
            });

            $('#confirmAddNamespace').click(function() {
                self.addNamespace();
            });
            
            // Refresh devices
            $('#refreshDevices').click(function() {
                self.loadDevices();
                self.loadDeviceStatus();
            });
            
            // Save ACL
            $('#saveACL').click(function() {
                self.saveACL();
            });

            // ACL Builder events
            $('#loadACLTemplate').click(function() {
                self.showACLTemplateModal();
            });

            $('#validateACL').click(function() {
                self.validateACL();
            });

            // Debug ACL save issues
            $('#debugACL').click(function() {
                self.debugACLConfig();
            });

            // ACL Builder navigation
            $('#aclBuilderNav a').click(function() {
                $('#aclBuilderNav li').removeClass('active');
                $(this).parent().addClass('active');
                let href = $(this).attr('data-href');
                $('#acl-builder-content .tab-pane').removeClass('active');
                $(href).addClass('active');

                console.log('ACL sub-tab switched to:', href);

                // Load content for the selected section
                switch (href) {
                    case '#acl-groups-section':
                        self.loadACLGroups();
                        break;
                    case '#acl-tagowners-section':
                        self.loadACLTagOwners();
                        break;
                    case '#acl-hosts-section':
                        self.loadACLHosts();
                        break;
                    case '#acl-policies-section':
                        self.loadACLPolicies();
                        break;
                    case '#acl-json-section':
                        self.syncACLToJSON();
                        break;
                }
            });

            // ACL item management
            $('#addGroup').click(function() {
                self.showGroupModal();
            });

            $('#addTagOwner').click(function() {
                self.showTagOwnerModal();
            });

            $('#addHost').click(function() {
                self.showHostModal();
            });

            $('#addPolicy').click(function() {
                self.showPolicyModal();
            });

            // Modal confirmations
            $('#confirmGroupAction').click(function() {
                self.saveGroup();
            });

            $('#confirmTagOwnerAction').click(function() {
                self.saveTagOwner();
            });

            $('#confirmHostAction').click(function() {
                self.saveHost();
            });

            $('#confirmPolicyAction').click(function() {
                self.savePolicy();
            });

            $('#confirmTemplateSelection').click(function() {
                self.loadACLTemplate();
            });

            // Template selection
            $('.template-option').click(function() {
                $('.template-option').removeClass('selected');
                $(this).addClass('selected');
                $('#confirmTemplateSelection').prop('disabled', false);
            });

            // Create PreAuth Key
            $('#createPreAuthKey').click(function() {
                self.createPreAuthKey();
            });

            // Refresh routes
            $('#refreshRoutes').click(function() {
                self.loadRoutes();
            });

            // Save route changes
            $('#saveRouteChanges').click(function() {
                self.saveRouteChanges();
            });
            
            // Tab change events - using the application's custom tab handling
            $('#myTab a').click(function () {
                $("#myTab").find("li[class ='active']").removeClass("active");
                $(this).parent().addClass("active");
                let href = $(this).attr("data-href");
                $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
                $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');

                console.log('Tab switched to:', href);
                if (href === '#devices-tab') {
                    console.log('Loading devices tab content...');
                    self.loadDevices();
                    self.loadDeviceStatus();
                } else if (href === '#namespaces-tab') {
                    console.log('Loading namespaces tab content...');
                    self.loadNamespaces();
                } else if (href === '#routes-tab') {
                    console.log('Loading routes tab content...');
                    self.loadRoutes();
                } else if (href === '#acl-tab') {
                    console.log('Loading ACL tab content...');
                    self.initializeACLTab();
                }
            });
        },

        // Initialize ACL tab with proper default state
        initializeACLTab: function() {
            var self = this;

            // Ensure the default tab is properly set
            $('#aclBuilderNav li').removeClass('active');
            $('#aclBuilderNav a[data-href="#acl-groups-section"]').parent().addClass('active');
            $('#acl-builder-content .tab-pane').removeClass('active');
            $('#acl-groups-section').addClass('active');

            // Load ACL data
            self.loadACL();
        },

        testConnection: function() {
            var self = this;

            // Show loading state
            self.showMessage('info', '正在测试连接...');

            $.ajax({
                url: 'headscale/test',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.showMessage('success', response.data);
                        // Also get detailed status
                        self.loadConnectionStatus();
                    } else {
                        self.showMessage('danger', '连接失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '连接测试失败');
                }
            });
        },

        loadConnectionStatus: function() {
            var self = this;
            $.ajax({
                url: 'headscale/status',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var status = response.data;
                        // Update status indicators if they exist
                        if ($('#restApiStatus').length) {
                            $('#restApiStatus').text(status.restStatus);
                        }
                        if ($('#grpcApiStatus').length) {
                            $('#grpcApiStatus').text(status.grpcStatus);
                        }
                    }
                },
                error: function() {
                    // Silently fail for status update
                    console.warn('Failed to load detailed connection status');
                }
            });
        },
        
        loadUsers: function() {
            var self = this;
            $.ajax({
                url: 'headscale/users',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.renderUsers(response.data);
                    } else {
                        self.showMessage('danger', '加载用户失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '加载用户失败');
                }
            });
        },
        
        renderUsers: function(users) {
            var tbody = $('#usersTable tbody');
            tbody.empty();

            if (!users || users.length === 0) {
                tbody.append('<tr><td colspan="5" class="text-center">暂无用户</td></tr>');
                return;
            }

            var self = this;
            users.forEach(function(user) {
                var row = $('<tr>');
                row.append('<td>' + user.name + '</td>');
                row.append('<td>' + (user.displayName || '-') + '</td>');
                row.append('<td>' + (user.createdAt ? self.formatDateTime(user.createdAt) : '-') + '</td>');
                row.append('<td>' + (user.updatedAt ? self.formatDateTime(user.updatedAt) : '-') + '</td>');

                var actions = $('<td>');
                actions.append('<button class="btn btn-info btn-xs" onclick="HeadscaleManager.showPreAuthKeys(\'' + user.name + '\')"><i class="fa fa-key"></i> 密钥</button> ');
                actions.append('<button class="btn btn-danger btn-xs" onclick="HeadscaleManager.deleteUser(\'' + user.name + '\')"><i class="fa fa-trash"></i> 删除</button>');
                row.append(actions);

                tbody.append(row);
            });
        },
        
        addUser: function() {
            var self = this;
            var username = $('#username').val().trim();
            var displayName = $('#displayName').val().trim();

            if (!username) {
                alert('请输入用户名');
                return;
            }

            // 校验用户名不能是纯数字
            if (/^\d+$/.test(username)) {
                alert('用户名不能是纯数字，请使用包含字母的用户名');
                return;
            }

            // 校验用户名格式
            if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
                alert('用户名只能包含字母、数字、下划线和连字符');
                return;
            }

            // 校验displayName格式（如果提供）
            if (displayName && !/^[a-zA-Z0-9_-]+$/.test(displayName)) {
                alert('显示名称只能包含字母、数字、下划线和连字符');
                return;
            }

            var requestData = { username: username };
            if (displayName) {
                requestData.displayName = displayName;
            }

            $.ajax({
                url: 'headscale/users',
                type: 'POST',
                data: requestData,
                success: function(response) {
                    if (response.code === 200) {
                        $('#addUserModal').modal('hide');
                        $('#addUserForm')[0].reset();
                        var message = '用户创建成功';
                        if (displayName) {
                            message += '（注意：显示名称 "' + displayName + '" 需要gRPC API支持，当前使用REST API创建）';
                        }
                        self.showMessage('success', message);
                        self.loadUsers();
                    } else {
                        alert('创建用户失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('创建用户失败');
                }
            });
        },
        
        deleteUser: function(username) {
            var self = this;

            // First check if user can be deleted
            $.ajax({
                url: 'headscale/users/' + encodeURIComponent(username) + '/can-delete',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var canDelete = response.data.canDelete;
                        var message = response.data.message;

                        if (!canDelete) {
                            alert('无法删除用户: ' + message);
                            return;
                        }

                        // User can be deleted, show confirmation
                        if (!confirm('确定要删除用户 "' + username + '" 吗？')) {
                            return;
                        }

                        // Proceed with deletion
                        self.performDeleteUser(username);
                    } else {
                        alert('检查用户状态失败: ' + response.message);
                    }
                },
                error: function() {
                    // If check fails, still allow deletion but with warning
                    if (!confirm('无法检查用户状态，确定要删除用户 "' + username + '" 吗？')) {
                        return;
                    }
                    self.performDeleteUser(username);
                }
            });
        },

        performDeleteUser: function(username) {
            var self = this;
            $.ajax({
                url: 'headscale/users/' + encodeURIComponent(username),
                type: 'DELETE',
                success: function(response) {
                    if (response.code === 200) {
                        self.showMessage('success', '用户删除成功');
                        self.loadUsers();
                    } else {
                        alert('删除用户失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('删除用户失败');
                }
            });
        },
        
        loadDeviceStatus: function() {
            var self = this;
            $.ajax({
                url: 'headscale/nodes/status',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var status = response.data;
                        $('#totalDevices').text(status.total);
                        $('#onlineDevices').text(status.online);
                        $('#offlineDevices').text(status.offline);
                    }
                },
                error: function() {
                    $('#totalDevices').text('错误');
                    $('#onlineDevices').text('错误');
                    $('#offlineDevices').text('错误');
                }
            });
        },
        
        loadDevices: function() {
            var self = this;
            $.ajax({
                url: 'headscale/nodes',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.renderDevices(response.data);
                    } else {
                        self.showMessage('danger', '加载设备失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '加载设备失败');
                }
            });
        },
        
        renderDevices: function(devices) {
            var tbody = $('#devicesTable tbody');
            tbody.empty();
            
            if (!devices || devices.length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">暂无设备</td></tr>');
                return;
            }
            
            var self = this;
            devices.forEach(function(device) {
                var row = $('<tr>');
                row.append('<td>' + (device.givenName || device.name || '-') + '</td>');
                row.append('<td>' + (device.user ? device.user.name : '-') + '</td>');
                row.append('<td>' + (device.ipAddresses ? device.ipAddresses.join(', ') : '-') + '</td>');
                
                var statusCell = $('<td>');
                if (device.online) {
                    statusCell.append('<span class="label label-success">在线</span>');
                } else {
                    statusCell.append('<span class="label label-default">离线</span>');
                }
                row.append(statusCell);
                
                row.append('<td>' + (device.lastSeen ? self.formatDateTime(device.lastSeen) : '-') + '</td>');
                
                var actions = $('<td>');
                actions.append('<button class="btn btn-danger btn-xs" onclick="HeadscaleManager.deleteDevice(\'' + device.id + '\')"><i class="fa fa-trash"></i> 删除</button>');
                row.append(actions);
                
                tbody.append(row);
            });
        },
        
        deleteDevice: function(deviceId) {
            var self = this;
            if (!confirm('确定要删除此设备吗？')) {
                return;
            }
            
            $.ajax({
                url: 'headscale/nodes/' + encodeURIComponent(deviceId),
                type: 'DELETE',
                success: function(response) {
                    if (response.code === 200) {
                        self.showMessage('success', '设备删除成功');
                        self.loadDevices();
                        self.loadDeviceStatus();
                    } else {
                        alert('删除设备失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('删除设备失败');
                }
            });
        },
        
        loadACL: function() {
            var self = this;
            $.ajax({
                url: 'headscale/acl',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var aclData = response.data;
                        var parsedACL = null;

                        if (typeof aclData === 'string') {
                            // If it's already a string, try to parse and format it
                            try {
                                parsedACL = JSON.parse(aclData);
                                $('#aclEditor').val(JSON.stringify(parsedACL, null, 2));
                            } catch (e) {
                                // If parsing fails, display as-is
                                $('#aclEditor').val(aclData);
                                parsedACL = {};
                            }
                        } else {
                            // If it's an object, stringify it
                            parsedACL = aclData;
                            $('#aclEditor').val(JSON.stringify(aclData, null, 2));
                        }

                        // Update internal ACL structure
                        self.currentACL = {
                            groups: parsedACL.groups || {},
                            tagOwners: parsedACL.tagOwners || {},
                            hosts: parsedACL.hosts || {},
                            acls: parsedACL.acls || [],
                            tests: parsedACL.tests || []
                        };

                        // Always load the current active section
                        var activeSection = $('#aclBuilderNav li.active a').attr('data-href');

                        // Default to groups section if none is active
                        if (!activeSection) {
                            activeSection = '#acl-groups-section';
                        }

                        console.log('Loading ACL section:', activeSection);

                        // Load the appropriate section
                        switch (activeSection) {
                            case '#acl-groups-section':
                                self.loadACLGroups();
                                break;
                            case '#acl-tagowners-section':
                                self.loadACLTagOwners();
                                break;
                            case '#acl-hosts-section':
                                self.loadACLHosts();
                                break;
                            case '#acl-policies-section':
                                self.loadACLPolicies();
                                break;
                            case '#acl-json-section':
                                // JSON editor is already updated above
                                break;
                            default:
                                // Default to groups if unknown section
                                self.loadACLGroups();
                                break;
                        }
                    } else {
                        self.showMessage('danger', '加载ACL配置失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '加载ACL配置失败');
                }
            });
        },

        saveACL: function() {
            var self = this;

            // Check if we're in JSON editor mode or visual builder mode
            var activeSection = $('#aclBuilderNav li.active a').attr('data-href');
            var aclText;

            if (activeSection === '#acl-json-section') {
                // Use JSON editor content
                aclText = $('#aclEditor').val().trim();
            } else {
                // Generate JSON from visual builder
                aclText = JSON.stringify(self.currentACL, null, 2);
                // Update the JSON editor with the generated content
                $('#aclEditor').val(aclText);
            }

            if (!aclText) {
                alert('请输入ACL配置');
                return;
            }

            try {
                // Validate JSON format
                var parsedACL = JSON.parse(aclText);
                // Update internal structure if parsing succeeds
                self.currentACL = {
                    groups: parsedACL.groups || {},
                    tagOwners: parsedACL.tagOwners || {},
                    hosts: parsedACL.hosts || {},
                    acls: parsedACL.acls || [],
                    tests: parsedACL.tests || []
                };
            } catch (e) {
                alert('ACL配置格式错误: ' + e.message);
                return;
            }

            console.log('Saving ACL with data:', aclText);

            $.ajax({
                url: 'headscale/acl',
                type: 'PUT',
                contentType: 'application/json',
                data: aclText, // Send as string directly
                success: function(response) {
                    console.log('ACL save response:', response);
                    if (response.code === 200) {
                        self.showMessage('success', 'ACL配置保存成功');
                        // Reload ACL to show the updated version
                        setTimeout(function() {
                            self.loadACL();
                        }, 1000); // Wait a bit before reloading to ensure database is updated
                    } else {
                        alert('保存ACL配置失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('ACL save error:', xhr.responseText);
                    alert('保存ACL配置失败: ' + error);
                }
            });
        },

        // ACL Builder Functions
        loadACLGroups: function() {
            var self = this;
            var container = $('#groupsList');
            container.empty();

            if (Object.keys(self.currentACL.groups).length === 0) {
                container.html('<div class="acl-empty-state"><i class="fa fa-users"></i>暂无用户组<br><small>点击"添加用户组"开始创建</small></div>');
                return;
            }

            $.each(self.currentACL.groups, function(groupName, users) {
                var groupItem = $('<div class="acl-item">');
                groupItem.html(`
                    <div class="acl-item-header">
                        <h6 class="acl-item-title">${groupName}</h6>
                        <div class="acl-item-actions">
                            <button class="btn btn-xs btn-primary edit-group" data-group="${groupName}">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-xs btn-danger delete-group" data-group="${groupName}">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="acl-item-content">
                        <strong>成员 (${users.length}):</strong><br>
                        ${users.map(user => `<span class="label label-info">${user}</span>`).join(' ')}
                    </div>
                `);
                container.append(groupItem);
            });

            // Bind events
            container.find('.edit-group').click(function() {
                var groupName = $(this).data('group');
                self.editGroup(groupName);
            });

            container.find('.delete-group').click(function() {
                var groupName = $(this).data('group');
                self.deleteGroup(groupName);
            });
        },

        loadACLTagOwners: function() {
            var self = this;
            var container = $('#tagOwnersList');
            container.empty();

            if (Object.keys(self.currentACL.tagOwners).length === 0) {
                container.html('<div class="acl-empty-state"><i class="fa fa-tags"></i>暂无标签所有者<br><small>点击"添加标签所有者"开始创建</small></div>');
                return;
            }

            $.each(self.currentACL.tagOwners, function(tagName, owners) {
                var tagItem = $('<div class="acl-item">');
                tagItem.html(`
                    <div class="acl-item-header">
                        <h6 class="acl-item-title">${tagName}</h6>
                        <div class="acl-item-actions">
                            <button class="btn btn-xs btn-primary edit-tagowner" data-tag="${tagName}">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-xs btn-danger delete-tagowner" data-tag="${tagName}">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="acl-item-content">
                        <strong>授权用户/组 (${owners.length}):</strong><br>
                        ${owners.map(owner => `<span class="label label-success">${owner}</span>`).join(' ')}
                    </div>
                `);
                container.append(tagItem);
            });

            // Bind events
            container.find('.edit-tagowner').click(function() {
                var tagName = $(this).data('tag');
                self.editTagOwner(tagName);
            });

            container.find('.delete-tagowner').click(function() {
                var tagName = $(this).data('tag');
                self.deleteTagOwner(tagName);
            });
        },

        loadACLHosts: function() {
            var self = this;
            var container = $('#hostsList');
            container.empty();

            if (Object.keys(self.currentACL.hosts).length === 0) {
                container.html('<div class="acl-empty-state"><i class="fa fa-server"></i>暂无主机定义<br><small>点击"添加主机定义"开始创建</small></div>');
                return;
            }

            $.each(self.currentACL.hosts, function(hostName, address) {
                var hostItem = $('<div class="acl-item">');
                hostItem.html(`
                    <div class="acl-item-header">
                        <h6 class="acl-item-title">${hostName}</h6>
                        <div class="acl-item-actions">
                            <button class="btn btn-xs btn-primary edit-host" data-host="${hostName}">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-xs btn-danger delete-host" data-host="${hostName}">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="acl-item-content">
                        <strong>地址:</strong> <code>${address}</code>
                    </div>
                `);
                container.append(hostItem);
            });

            // Bind events
            container.find('.edit-host').click(function() {
                var hostName = $(this).data('host');
                self.editHost(hostName);
            });

            container.find('.delete-host').click(function() {
                var hostName = $(this).data('host');
                self.deleteHost(hostName);
            });
        },

        loadACLPolicies: function() {
            var self = this;
            var container = $('#policiesList');
            container.empty();

            if (self.currentACL.acls.length === 0) {
                container.html('<div class="acl-empty-state"><i class="fa fa-shield"></i>暂无访问策略<br><small>点击"添加访问策略"开始创建</small></div>');
                return;
            }

            $.each(self.currentACL.acls, function(index, policy) {
                var policyItem = $('<div class="acl-item">');
                var protocolBadge = policy.proto ? `<span class="policy-rule-protocol">${policy.proto.toUpperCase()}</span>` : '';
                var actionClass = policy.action === 'accept' ? 'success' : 'danger';

                policyItem.html(`
                    <div class="acl-item-header">
                        <h6 class="acl-item-title">
                            <span class="label label-${actionClass}">${policy.action.toUpperCase()}</span>
                            ${protocolBadge}
                            策略 #${index + 1}
                        </h6>
                        <div class="acl-item-actions">
                            <button class="btn btn-xs btn-primary edit-policy" data-index="${index}">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-xs btn-danger delete-policy" data-index="${index}">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="acl-item-content">
                        <div><strong>源:</strong> ${policy.src.map(src => `<span class="label label-default">${src}</span>`).join(' ')}</div>
                        <div style="margin-top: 5px;"><strong>目标:</strong> ${policy.dst.map(dst => `<span class="label label-primary">${dst}</span>`).join(' ')}</div>
                    </div>
                `);
                container.append(policyItem);
            });

            // Bind events
            container.find('.edit-policy').click(function() {
                var index = $(this).data('index');
                self.editPolicy(index);
            });

            container.find('.delete-policy').click(function() {
                var index = $(this).data('index');
                self.deletePolicy(index);
            });
        },

        showPreAuthKeys: function(username) {
            var self = this;
            self.currentUser = username;
            $('#preAuthKeysModal .modal-title').text('预授权密钥管理 - ' + username);

            $.ajax({
                url: 'headscale/users/' + encodeURIComponent(username) + '/preauth-keys',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.renderPreAuthKeys(response.data);
                        $('#preAuthKeysModal').modal('show');
                    } else {
                        alert('加载预授权密钥失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('加载预授权密钥失败');
                }
            });
        },

        renderPreAuthKeys: function(keys) {
            var tbody = $('#preAuthKeysTable tbody');
            tbody.empty();

            if (!keys || keys.length === 0) {
                tbody.append('<tr><td colspan="5" class="text-center">暂无预授权密钥</td></tr>');
                return;
            }

            keys.forEach(function(key) {
                var row = $('<tr>');
                row.append('<td><code>' + (key.key || '-') + '</code></td>');
                row.append('<td>' + (key.reusable ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>') + '</td>');
                row.append('<td>' + (key.ephemeral ? '<span class="label label-warning">是</span>' : '<span class="label label-default">否</span>') + '</td>');
                row.append('<td>' + (key.used ? '<span class="label label-danger">已使用</span>' : '<span class="label label-success">未使用</span>') + '</td>');
                row.append('<td>' + (key.createdAt ? self.formatDateTime(key.createdAt) : '-') + '</td>');

                tbody.append(row);
            });
        },

        createPreAuthKey: function() {
            var self = this;
            if (!self.currentUser) {
                alert('请先选择用户');
                return;
            }

            var reusable = confirm('是否创建可重用密钥？');
            var ephemeral = confirm('是否创建临时密钥？');

            // 询问过期时间
            var expirationHours = prompt('请输入密钥过期时间（小时），留空则默认24小时：', '24');
            if (expirationHours !== null && expirationHours.trim() !== '') {
                expirationHours = parseInt(expirationHours);
                if (isNaN(expirationHours) || expirationHours <= 0) {
                    alert('请输入有效的小时数');
                    return;
                }
            } else {
                expirationHours = null; // 使用默认值
            }

            var requestData = {
                reusable: reusable,
                ephemeral: ephemeral
            };

            if (expirationHours !== null) {
                requestData.expirationHours = expirationHours;
            }

            $.ajax({
                url: 'headscale/users/' + encodeURIComponent(self.currentUser) + '/preauth-keys',
                type: 'POST',
                data: requestData,
                success: function(response) {
                    if (response.code === 200) {
                        var hours = expirationHours || 24;
                        self.showMessage('success', '预授权密钥创建成功（' + hours + '小时后过期）');
                        self.showPreAuthKeys(self.currentUser);
                    } else {
                        alert('创建预授权密钥失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('创建预授权密钥失败');
                }
            });
        },



        formatDateTime: function(dateTimeStr) {
            if (!dateTimeStr) return '-';

            try {
                // Handle ISO 8601 format with nanoseconds
                // Convert from "2025-04-22T08:27:18.802455714Z" to JavaScript Date
                var cleanDateStr = dateTimeStr.replace(/\.\d{6,}Z$/, 'Z'); // Remove nanoseconds
                var date = new Date(cleanDateStr);

                if (isNaN(date.getTime())) {
                    // Fallback: try parsing as-is
                    date = new Date(dateTimeStr);
                }

                if (isNaN(date.getTime())) {
                    return dateTimeStr; // Return original string if parsing fails
                }

                return date.toLocaleString();
            } catch (e) {
                return dateTimeStr; // Return original string if error occurs
            }
        },

        addNamespace: function() {
            var self = this;
            var namespaceName = $('#namespaceName').val().trim();

            if (!namespaceName) {
                alert('请输入命名空间名称');
                return;
            }

            // 校验命名空间名称格式
            if (!/^[a-zA-Z0-9_-]+$/.test(namespaceName)) {
                alert('命名空间名称只能包含字母、数字、下划线和连字符');
                return;
            }

            $.ajax({
                url: 'headscale/namespaces',
                type: 'POST',
                data: { namespaceName: namespaceName },
                success: function(response) {
                    if (response.code === 200) {
                        $('#addNamespaceModal').modal('hide');
                        $('#addNamespaceForm')[0].reset();
                        self.showMessage('success', '命名空间创建成功');
                        self.loadNamespaces();
                    } else {
                        alert('创建命名空间失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('创建命名空间失败');
                }
            });
        },

        loadNamespaces: function() {
            var self = this;
            // For now, we'll extract namespaces from users' displayName
            // In a real implementation, you might have a dedicated namespace endpoint
            $.ajax({
                url: 'headscale/users',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.renderNamespaces(response.data);
                    } else {
                        self.showMessage('danger', '加载命名空间失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '加载命名空间失败');
                }
            });
        },

        renderNamespaces: function(users) {
            var tbody = $('#namespacesTable tbody');
            tbody.empty();

            // Extract unique namespaces from users' displayName
            var namespaces = {};
            if (users && users.length > 0) {
                users.forEach(function(user) {
                    if (user.displayName) {
                        if (!namespaces[user.displayName]) {
                            namespaces[user.displayName] = {
                                name: user.displayName,
                                userCount: 0,
                                createdAt: user.createdAt
                            };
                        }
                        namespaces[user.displayName].userCount++;
                        // Use the earliest creation time
                        if (user.createdAt && (!namespaces[user.displayName].createdAt ||
                            user.createdAt < namespaces[user.displayName].createdAt)) {
                            namespaces[user.displayName].createdAt = user.createdAt;
                        }
                    }
                });
            }

            var namespaceList = Object.values(namespaces);

            if (namespaceList.length === 0) {
                tbody.append('<tr><td colspan="4" class="text-center">暂无命名空间</td></tr>');
                return;
            }

            var self = this;
            namespaceList.forEach(function(namespace) {
                var row = $('<tr>');
                row.append('<td>' + namespace.name + '</td>');
                row.append('<td>' + namespace.userCount + '</td>');
                row.append('<td>' + (namespace.createdAt ? self.formatDateTime(namespace.createdAt) : '-') + '</td>');

                var actions = $('<td>');
                actions.append('<button class="btn btn-info btn-xs" onclick="HeadscaleManager.viewNamespaceUsers(\'' + namespace.name + '\')"><i class="fa fa-users"></i> 查看用户</button> ');
                row.append(actions);

                tbody.append(row);
            });
        },

        viewNamespaceUsers: function(namespaceName) {
            // Filter users by displayName and show them
            var self = this;
            $.ajax({
                url: 'headscale/users',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var namespaceUsers = response.data.filter(function(user) {
                            return user.displayName === namespaceName;
                        });

                        var message = '命名空间 "' + namespaceName + '" 中的用户:\n';
                        if (namespaceUsers.length > 0) {
                            namespaceUsers.forEach(function(user) {
                                message += '- ' + user.name + '\n';
                            });
                        } else {
                            message += '(无用户)';
                        }
                        alert(message);
                    } else {
                        alert('获取命名空间用户失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('获取命名空间用户失败');
                }
            });
        },

        loadRoutes: function() {
            var self = this;
            $.ajax({
                url: 'headscale/routes',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        self.renderRoutes(response.data);
                    } else {
                        self.showMessage('danger', '加载路由失败: ' + response.message);
                    }
                },
                error: function() {
                    self.showMessage('danger', '加载路由失败');
                }
            });
        },

        renderRoutes: function(nodes) {
            var tbody = $('#routesTable tbody');
            tbody.empty();

            if (!nodes || nodes.length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">暂无设备路由</td></tr>');
                return;
            }

            var self = this;
            nodes.forEach(function(node) {
                // Only show nodes that have route information
                if (node.availableRoutes && node.availableRoutes.length > 0) {
                    var row = $('<tr>');
                    row.append('<td>' + (node.givenName || node.name || '-') + '</td>');
                    row.append('<td>' + (node.user ? node.user.name : '-') + '</td>');
                    row.append('<td>' + (node.ipAddresses ? node.ipAddresses.join(', ') : '-') + '</td>');

                    // Available routes
                    var availableRoutesCell = $('<td>');
                    if (node.availableRoutes && node.availableRoutes.length > 0) {
                        availableRoutesCell.append('<span class="label label-info">' + node.availableRoutes.length + ' 条路由</span>');
                    } else {
                        availableRoutesCell.append('-');
                    }
                    row.append(availableRoutesCell);

                    // Approved routes
                    var approvedRoutesCell = $('<td>');
                    if (node.approvedRoutes && node.approvedRoutes.length > 0) {
                        approvedRoutesCell.append('<span class="label label-success">' + node.approvedRoutes.length + ' 条已批准</span>');
                    } else {
                        approvedRoutesCell.append('<span class="label label-default">无</span>');
                    }
                    row.append(approvedRoutesCell);

                    // Actions
                    var actions = $('<td>');
                    actions.append('<button class="btn btn-primary btn-xs" onclick="HeadscaleManager.manageRoutes(\'' + node.id + '\')"><i class="fa fa-cog"></i> 管理路由</button>');
                    row.append(actions);

                    tbody.append(row);
                }
            });

            if (tbody.children().length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">暂无可用路由的设备</td></tr>');
            }
        },

        manageRoutes: function(nodeId) {
            var self = this;

            // Find the node data
            $.ajax({
                url: 'headscale/routes',
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        var node = response.data.find(function(n) { return n.id === nodeId; });
                        if (node) {
                            self.showRouteManagementModal(node);
                        } else {
                            alert('未找到设备信息');
                        }
                    } else {
                        alert('获取设备信息失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('获取设备信息失败');
                }
            });
        },

        showRouteManagementModal: function(node) {
            var self = this;

            // Set device info
            $('#routeDeviceName').text(node.givenName || node.name || '-');
            $('#routeDeviceUser').text(node.user ? node.user.name : '-');
            $('#routeDeviceIP').text(node.ipAddresses ? node.ipAddresses.join(', ') : '-');

            // Store current node for later use
            self.currentRouteNode = node;

            // Render available routes (with checkboxes for management)
            var availableContainer = $('#availableRoutes');
            availableContainer.empty();
            if (node.availableRoutes && node.availableRoutes.length > 0) {
                node.availableRoutes.forEach(function(route) {
                    var isApproved = node.approvedRoutes && node.approvedRoutes.includes(route);
                    var routeItem = $('<div class="checkbox">');
                    routeItem.append(
                        '<label><input type="checkbox" class="route-checkbox" data-route="' + route + '" ' +
                        (isApproved ? 'checked' : '') + '> ' + route + '</label>'
                    );
                    availableContainer.append(routeItem);
                });
            } else {
                availableContainer.append('<p class="text-muted">无可用路由</p>');
            }

            // Render approved routes (display only, for information)
            self.updateApprovedRoutesDisplay(node);

            // Show modal
            $('#routeManagementModal').modal('show');
        },

        updateApprovedRoutesDisplay: function(node) {
            var approvedContainer = $('#approvedRoutes');
            approvedContainer.empty();
            if (node.approvedRoutes && node.approvedRoutes.length > 0) {
                node.approvedRoutes.forEach(function(route) {
                    var routeItem = $('<div class="route-item">');
                    routeItem.append(
                        '<span class="label label-success"><i class="fa fa-check"></i> ' + route + '</span>'
                    );
                    approvedContainer.append(routeItem);
                });
            } else {
                approvedContainer.append('<p class="text-muted">无已批准路由</p>');
            }
        },

        updateAvailableRoutesCheckboxes: function(node) {
            // Update checkboxes based on current approved routes
            $('#availableRoutes .route-checkbox').each(function() {
                var route = $(this).data('route');
                var isApproved = node.approvedRoutes && node.approvedRoutes.includes(route);
                $(this).prop('checked', isApproved);
            });
        },

        refreshRouteModal: function() {
            var self = this;
            if (self.currentRouteNode) {
                // Reload the node data and update the modal
                $.ajax({
                    url: 'headscale/routes',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 200) {
                            var updatedNode = response.data.find(function(n) {
                                return n.id === self.currentRouteNode.id;
                            });
                            if (updatedNode) {
                                self.currentRouteNode = updatedNode;
                                self.updateApprovedRoutesDisplay(updatedNode);
                                self.updateAvailableRoutesCheckboxes(updatedNode);
                            }
                        }
                    }
                });
            }

            // Show modal
            $('#routeManagementModal').modal('show');
        },

        saveRouteChanges: function() {
            var self = this;

            if (!self.currentRouteNode) {
                alert('未选择设备');
                return;
            }

            // Get selected routes
            var selectedRoutes = [];
            $('#availableRoutes .route-checkbox:checked').each(function() {
                selectedRoutes.push($(this).data('route'));
            });

            // Send to server
            $.ajax({
                url: 'headscale/nodes/' + self.currentRouteNode.id + '/approve-routes',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(selectedRoutes),
                success: function(response) {
                    if (response.code === 200) {
                        self.showMessage('success', '路由设置成功');
                        // Refresh the modal display with updated data
                        self.refreshRouteModal();
                        // Also refresh the main routes table
                        self.loadRoutes();
                    } else {
                        alert('路由设置失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('路由设置失败');
                }
            });
        },

        showMessage: function(type, message) {
            var alertClass = 'alert-' + type;
            $('#connectionStatus')
                .removeClass('alert-info alert-success alert-warning alert-danger')
                .addClass(alertClass)
                .find('#connectionMessage').text(message);
            $('#connectionStatus').show();

            setTimeout(function() {
                $('#connectionStatus').fadeOut();
            }, 5000);
        },

        // ACL Builder Modal Functions
        showGroupModal: function(groupName) {
            var self = this;
            self.editingItem = groupName || null;

            if (groupName) {
                $('#aclGroupModal .modal-title').text('编辑用户组');
                $('#groupName').val(groupName);
                $('#groupUsers').val(self.currentACL.groups[groupName].join('\n'));
                $('#groupName').prop('readonly', true);
            } else {
                $('#aclGroupModal .modal-title').text('添加用户组');
                $('#groupName').val('').prop('readonly', false);
                $('#groupUsers').val('');
            }

            $('#aclGroupModal').modal('show');
        },

        showTagOwnerModal: function(tagName) {
            var self = this;
            self.editingItem = tagName || null;

            if (tagName) {
                $('#aclTagOwnerModal .modal-title').text('编辑标签所有者');
                $('#tagName').val(tagName);
                $('#tagOwners').val(self.currentACL.tagOwners[tagName].join('\n'));
                $('#tagName').prop('readonly', true);
            } else {
                $('#aclTagOwnerModal .modal-title').text('添加标签所有者');
                $('#tagName').val('').prop('readonly', false);
                $('#tagOwners').val('');
            }

            $('#aclTagOwnerModal').modal('show');
        },

        showHostModal: function(hostName) {
            var self = this;
            self.editingItem = hostName || null;

            if (hostName) {
                $('#aclHostModal .modal-title').text('编辑主机定义');
                $('#hostName').val(hostName);
                $('#hostAddress').val(self.currentACL.hosts[hostName]);
                $('#hostName').prop('readonly', true);
            } else {
                $('#aclHostModal .modal-title').text('添加主机定义');
                $('#hostName').val('').prop('readonly', false);
                $('#hostAddress').val('');
            }

            $('#aclHostModal').modal('show');
        },

        showPolicyModal: function(index) {
            var self = this;
            self.editingIndex = index !== undefined ? index : -1;

            if (index !== undefined) {
                var policy = self.currentACL.acls[index];
                $('#aclPolicyModal .modal-title').text('编辑访问策略');
                $('#policyAction').val(policy.action);
                $('#policyProtocol').val(policy.proto || '');
                $('#policySources').val(policy.src.join('\n'));
                $('#policyDestinations').val(policy.dst.join('\n'));
            } else {
                $('#aclPolicyModal .modal-title').text('添加访问策略');
                $('#policyAction').val('accept');
                $('#policyProtocol').val('');
                $('#policySources').val('');
                $('#policyDestinations').val('');
            }

            $('#aclPolicyModal').modal('show');
        },

        showACLTemplateModal: function() {
            $('.template-option').removeClass('selected');
            $('#confirmTemplateSelection').prop('disabled', true);
            $('#aclTemplateModal').modal('show');
        },

        // ACL Builder Save Functions
        saveGroup: function() {
            var self = this;
            var groupName = $('#groupName').val().trim();
            var usersText = $('#groupUsers').val().trim();

            if (!groupName) {
                alert('请输入组名称');
                return;
            }

            var users = usersText ? usersText.split('\n').map(u => u.trim()).filter(u => u) : [];

            if (self.editingItem && self.editingItem !== groupName) {
                // Renaming group - delete old and add new
                delete self.currentACL.groups[self.editingItem];
            }

            self.currentACL.groups[groupName] = users;
            self.loadACLGroups();
            $('#aclGroupModal').modal('hide');
            self.showMessage('success', '用户组保存成功');
        },

        saveTagOwner: function() {
            var self = this;
            var tagName = $('#tagName').val().trim();
            var ownersText = $('#tagOwners').val().trim();

            if (!tagName) {
                alert('请输入标签名称');
                return;
            }

            var owners = ownersText ? ownersText.split('\n').map(o => o.trim()).filter(o => o) : [];

            if (self.editingItem && self.editingItem !== tagName) {
                // Renaming tag - delete old and add new
                delete self.currentACL.tagOwners[self.editingItem];
            }

            self.currentACL.tagOwners[tagName] = owners;
            self.loadACLTagOwners();
            $('#aclTagOwnerModal').modal('hide');
            self.showMessage('success', '标签所有者保存成功');
        },

        saveHost: function() {
            var self = this;
            var hostName = $('#hostName').val().trim();
            var hostAddress = $('#hostAddress').val().trim();

            if (!hostName || !hostAddress) {
                alert('请输入主机名称和地址');
                return;
            }

            // Basic CIDR validation
            if (!/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/.test(hostAddress)) {
                alert('请输入有效的CIDR格式地址 (例如: ********/32)');
                return;
            }

            if (self.editingItem && self.editingItem !== hostName) {
                // Renaming host - delete old and add new
                delete self.currentACL.hosts[self.editingItem];
            }

            self.currentACL.hosts[hostName] = hostAddress;
            self.loadACLHosts();
            $('#aclHostModal').modal('hide');
            self.showMessage('success', '主机定义保存成功');
        },

        savePolicy: function() {
            var self = this;
            var action = $('#policyAction').val();
            var protocol = $('#policyProtocol').val();
            var sourcesText = $('#policySources').val().trim();
            var destinationsText = $('#policyDestinations').val().trim();

            if (!sourcesText || !destinationsText) {
                alert('请输入源和目标');
                return;
            }

            var sources = sourcesText.split('\n').map(s => s.trim()).filter(s => s);
            var destinations = destinationsText.split('\n').map(d => d.trim()).filter(d => d);

            var policy = {
                action: action,
                src: sources,
                dst: destinations
            };

            if (protocol) {
                policy.proto = protocol;
            }

            if (self.editingIndex >= 0) {
                self.currentACL.acls[self.editingIndex] = policy;
            } else {
                self.currentACL.acls.push(policy);
            }

            self.loadACLPolicies();
            $('#aclPolicyModal').modal('hide');
            self.showMessage('success', '访问策略保存成功');
        },

        // ACL Builder Edit Functions
        editGroup: function(groupName) {
            this.showGroupModal(groupName);
        },

        editTagOwner: function(tagName) {
            this.showTagOwnerModal(tagName);
        },

        editHost: function(hostName) {
            this.showHostModal(hostName);
        },

        editPolicy: function(index) {
            this.showPolicyModal(index);
        },

        // ACL Builder Delete Functions
        deleteGroup: function(groupName) {
            var self = this;
            if (confirm('确定要删除用户组 "' + groupName + '" 吗？')) {
                delete self.currentACL.groups[groupName];
                self.loadACLGroups();
                self.showMessage('success', '用户组删除成功');
            }
        },

        deleteTagOwner: function(tagName) {
            var self = this;
            if (confirm('确定要删除标签所有者 "' + tagName + '" 吗？')) {
                delete self.currentACL.tagOwners[tagName];
                self.loadACLTagOwners();
                self.showMessage('success', '标签所有者删除成功');
            }
        },

        deleteHost: function(hostName) {
            var self = this;
            if (confirm('确定要删除主机定义 "' + hostName + '" 吗？')) {
                delete self.currentACL.hosts[hostName];
                self.loadACLHosts();
                self.showMessage('success', '主机定义删除成功');
            }
        },

        deletePolicy: function(index) {
            var self = this;
            if (confirm('确定要删除这条访问策略吗？')) {
                self.currentACL.acls.splice(index, 1);
                self.loadACLPolicies();
                self.showMessage('success', '访问策略删除成功');
            }
        },

        // ACL Template Functions
        loadACLTemplate: function() {
            var self = this;
            var selectedTemplate = $('.template-option.selected').data('template');

            if (!selectedTemplate) {
                alert('请选择一个模板');
                return;
            }

            var templates = self.getACLTemplates();
            if (templates[selectedTemplate]) {
                self.currentACL = JSON.parse(JSON.stringify(templates[selectedTemplate]));
                self.syncACLToJSON();

                // Reload current section
                var activeSection = $('#aclBuilderNav li.active a').attr('data-href');
                if (activeSection === '#acl-groups-section') {
                    self.loadACLGroups();
                } else if (activeSection === '#acl-tagowners-section') {
                    self.loadACLTagOwners();
                } else if (activeSection === '#acl-hosts-section') {
                    self.loadACLHosts();
                } else if (activeSection === '#acl-policies-section') {
                    self.loadACLPolicies();
                }

                $('#aclTemplateModal').modal('hide');
                self.showMessage('success', '模板加载成功');
            }
        },

        getACLTemplates: function() {
            return {
                empty: {
                    groups: {},
                    tagOwners: {},
                    hosts: {},
                    acls: [],
                    tests: []
                },
                basic: {
                    groups: {
                        "group:admin": ["admin@"],
                        "group:users": ["user1@", "user2@"]
                    },
                    tagOwners: {
                        "tag:server": ["group:admin"]
                    },
                    hosts: {},
                    acls: [
                        {
                            action: "accept",
                            src: ["group:admin"],
                            dst: ["*:*"]
                        },
                        {
                            action: "accept",
                            src: ["group:users"],
                            dst: ["group:users:*"]
                        }
                    ],
                    tests: []
                },
                enterprise: {
                    groups: {
                        "group:admin": ["admin@"],
                        "group:dev": ["dev1@", "dev2@"],
                        "group:intern": ["intern1@"]
                    },
                    tagOwners: {
                        "tag:prod-servers": ["group:admin"],
                        "tag:dev-servers": ["group:admin", "group:dev"],
                        "tag:internal": ["group:admin"]
                    },
                    hosts: {
                        "database.internal": "********0/32",
                        "web.internal": "*********/32"
                    },
                    acls: [
                        {
                            action: "accept",
                            src: ["group:admin"],
                            dst: ["*:*"]
                        },
                        {
                            action: "accept",
                            src: ["group:dev"],
                            dst: ["tag:dev-servers:*", "tag:prod-servers:80,443"]
                        },
                        {
                            action: "accept",
                            src: ["group:intern"],
                            dst: ["tag:dev-servers:80,443"]
                        }
                    ],
                    tests: []
                },
                homelab: {
                    groups: {
                        "group:family": ["dad@", "mom@"],
                        "group:kids": ["kid1@", "kid2@"]
                    },
                    tagOwners: {
                        "tag:servers": ["group:family"],
                        "tag:iot": ["group:family"]
                    },
                    hosts: {
                        "nas.home": "*************/32",
                        "printer.home": "*************/32"
                    },
                    acls: [
                        {
                            action: "accept",
                            src: ["group:family"],
                            dst: ["*:*"]
                        },
                        {
                            action: "accept",
                            src: ["group:kids"],
                            dst: ["group:kids:*", "nas.home:80,443"]
                        }
                    ],
                    tests: []
                }
            };
        },

        // ACL Utility Functions
        syncACLToJSON: function() {
            $('#aclEditor').val(JSON.stringify(this.currentACL, null, 2));
        },

        validateACL: function() {
            var self = this;
            var aclText = $('#aclEditor').val().trim();

            if (!aclText) {
                alert('请输入ACL配置');
                return;
            }

            try {
                var parsedACL = JSON.parse(aclText);

                // Basic validation
                var errors = [];

                // Validate groups
                if (parsedACL.groups) {
                    Object.keys(parsedACL.groups).forEach(function(groupName) {
                        if (!Array.isArray(parsedACL.groups[groupName])) {
                            errors.push('组 "' + groupName + '" 的成员必须是数组');
                        }
                    });
                }

                // Validate tagOwners
                if (parsedACL.tagOwners) {
                    Object.keys(parsedACL.tagOwners).forEach(function(tagName) {
                        if (!Array.isArray(parsedACL.tagOwners[tagName])) {
                            errors.push('标签 "' + tagName + '" 的所有者必须是数组');
                        }
                    });
                }

                // Validate ACLs
                if (parsedACL.acls && Array.isArray(parsedACL.acls)) {
                    parsedACL.acls.forEach(function(acl, index) {
                        if (!acl.action) {
                            errors.push('策略 #' + (index + 1) + ' 缺少 action 字段');
                        }
                        if (!acl.src || !Array.isArray(acl.src)) {
                            errors.push('策略 #' + (index + 1) + ' 的 src 必须是数组');
                        }
                        if (!acl.dst || !Array.isArray(acl.dst)) {
                            errors.push('策略 #' + (index + 1) + ' 的 dst 必须是数组');
                        }
                    });
                }

                if (errors.length > 0) {
                    alert('ACL配置验证失败:\n' + errors.join('\n'));
                } else {
                    self.showMessage('success', 'ACL配置验证通过');
                }

            } catch (e) {
                alert('ACL配置格式错误: ' + e.message);
            }
        },

        // Debug ACL configuration issues
        debugACLConfig: function() {
            var self = this;

            console.log('=== ACL Debug Information ===');
            console.log('Current ACL Object:', self.currentACL);
            console.log('JSON Editor Content:', $('#aclEditor').val());

            // Check Headscale configuration
            $.ajax({
                url: 'headscale/config/check',
                type: 'GET',
                success: function(response) {
                    console.log('Headscale Config Check:', response);
                    if (response.code === 200) {
                        var config = response.data;
                        var debugInfo = [
                            '=== Headscale 配置诊断 ===',
                            '连接状态: ' + (config.apiConnectivity ? '正常' : '失败'),
                            'API响应码: ' + config.apiResponseCode,
                            'Headscale URL: ' + config.headscaleUrl,
                            '有API密钥: ' + (config.hasApiKey ? '是' : '否'),
                            '当前策略存在: ' + (config.currentPolicyExists ? '是' : '否'),
                            '当前策略长度: ' + config.currentPolicyLength
                        ];

                        if (config.apiError) {
                            debugInfo.push('API错误: ' + config.apiError);
                        }
                        if (config.policyError) {
                            debugInfo.push('策略错误: ' + config.policyError);
                        }
                        if (config.connectivityError) {
                            debugInfo.push('连接错误: ' + config.connectivityError);
                        }

                        alert(debugInfo.join('\n'));

                        // Additional recommendations
                        var recommendations = [];
                        if (!config.apiConnectivity) {
                            recommendations.push('- 检查Headscale服务是否运行');
                            recommendations.push('- 检查API密钥是否正确');
                            recommendations.push('- 检查网络连接');
                        }
                        if (!config.currentPolicyExists) {
                            recommendations.push('- 检查Headscale配置中的policy.mode是否设置为"database"');
                            recommendations.push('- 确保Headscale版本支持数据库模式的ACL');
                        }

                        if (recommendations.length > 0) {
                            alert('建议检查以下项目:\n' + recommendations.join('\n'));
                        }
                    } else {
                        alert('配置检查失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('无法获取配置信息');
                }
            });
        }
    };

    // Make HeadscaleManager globally accessible
    window.HeadscaleManager = HeadscaleManager;

    // Initialize
    HeadscaleManager.init();
});
