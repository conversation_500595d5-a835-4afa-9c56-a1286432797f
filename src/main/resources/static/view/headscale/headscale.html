<div id="headscale">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">Headscale 管理</h4>
            <div class="panel-tools">
                <button type="button" class="btn btn-primary btn-sm" id="testConnection">
                    <i class="fa fa-plug"></i> 测试连接
                </button>
            </div>
        </div>
        <div class="panel-body">
            <!-- Connection Status -->
            <div class="alert alert-info" id="connectionStatus" style="display: none;">
                <i class="fa fa-info-circle"></i> <span id="connectionMessage"></span>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" id="myTab">
                <li class="active">
                    <a data-href="#users-tab">
                        <i class="fa fa-users"></i> 用户管理
                    </a>
                </li>
                <li>
                    <a data-href="#namespaces-tab">
                        <i class="fa fa-sitemap"></i> 命名空间
                    </a>
                </li>
                <li>
                    <a data-href="#devices-tab">
                        <i class="fa fa-desktop"></i> 设备管理
                    </a>
                </li>
                <li>
                    <a data-href="#routes-tab">
                        <i class="fa fa-route"></i> 路由
                    </a>
                </li>
                <li>
                    <a data-href="#acl-tab">
                        <i class="fa fa-shield"></i> ACL 配置
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div id="mytab-content2" class="tab-content">
                <!-- Users Tab -->
                <div id="users-tab" class="tab-pane fade active">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">用户列表</h5>
                                    <div class="panel-tools">
                                        <button type="button" class="btn btn-success btn-sm" id="addUser">
                                            <i class="fa fa-plus"></i> 添加用户
                                        </button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-striped" id="usersTable">
                                        <thead>
                                            <tr>
                                                <th>用户名</th>
                                                <th>显示名称</th>
                                                <th>创建时间</th>
                                                <th>更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Users will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Namespaces Tab -->
                <div id="namespaces-tab" class="tab-pane fade">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">命名空间管理</h5>
                                    <div class="panel-tools">
                                        <button type="button" class="btn btn-success btn-sm" id="addNamespace">
                                            <i class="fa fa-plus"></i> 创建命名空间
                                        </button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="alert alert-info">
                                        <i class="fa fa-info-circle"></i>
                                        <strong>说明：</strong> 命名空间功能需要gRPC API支持。如果gRPC不可用，请通过"用户管理"选项卡创建用户时指定显示名称来创建命名空间。
                                    </div>
                                    <table class="table table-striped" id="namespacesTable">
                                        <thead>
                                            <tr>
                                                <th>命名空间名称</th>
                                                <th>用户数量</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Namespaces will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Devices Tab -->
                <div id="devices-tab" class="tab-pane fade">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <!-- Device Status Summary -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="panel panel-primary">
                                        <div class="panel-body text-center">
                                            <h3 id="totalDevices">-</h3>
                                            <p>总设备数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="panel panel-success">
                                        <div class="panel-body text-center">
                                            <h3 id="onlineDevices">-</h3>
                                            <p>在线设备</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="panel panel-warning">
                                        <div class="panel-body text-center">
                                            <h3 id="offlineDevices">-</h3>
                                            <p>离线设备</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Device List -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">设备列表</h5>
                                    <div class="panel-tools">
                                        <button type="button" class="btn btn-info btn-sm" id="refreshDevices">
                                            <i class="fa fa-refresh"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-striped" id="devicesTable">
                                        <thead>
                                            <tr>
                                                <th>设备名称</th>
                                                <th>用户</th>
                                                <th>IP地址</th>
                                                <th>状态</th>
                                                <th>最后在线</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Devices will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Routes Tab -->
                <div id="routes-tab" class="tab-pane fade">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">设备路由管理</h5>
                                    <div class="panel-tools">
                                        <button type="button" class="btn btn-info btn-sm" id="refreshRoutes">
                                            <i class="fa fa-refresh"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="alert alert-info">
                                        <i class="fa fa-info-circle"></i>
                                        <strong>说明：</strong> 这里显示所有设备的路由信息。您可以启用或禁用特定设备的子网路由。
                                    </div>
                                    <table class="table table-striped" id="routesTable">
                                        <thead>
                                            <tr>
                                                <th>设备名称</th>
                                                <th>用户</th>
                                                <th>IP地址</th>
                                                <th>可用路由</th>
                                                <th>已批准路由</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Routes will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ACL Tab -->
                <div id="acl-tab" class="tab-pane fade">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">ACL 策略配置</h5>
                                    <div class="panel-tools">
                                        <button type="button" class="btn btn-info btn-sm" id="loadACLTemplate">
                                            <i class="fa fa-file-text"></i> 加载模板
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" id="validateACL">
                                            <i class="fa fa-check-circle"></i> 验证配置
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" id="debugACL">
                                            <i class="fa fa-bug"></i> 调试配置
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm" id="saveACL">
                                            <i class="fa fa-save"></i> 保存配置
                                        </button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <!-- ACL Builder Navigation -->
                                    <ul class="nav nav-pills" id="aclBuilderNav">
                                        <li class="active">
                                            <a data-href="#acl-groups-section">
                                                <i class="fa fa-users"></i> 用户组
                                            </a>
                                        </li>
                                        <li>
                                            <a data-href="#acl-tagowners-section">
                                                <i class="fa fa-tags"></i> 标签所有者
                                            </a>
                                        </li>
                                        <li>
                                            <a data-href="#acl-hosts-section">
                                                <i class="fa fa-server"></i> 主机定义
                                            </a>
                                        </li>
                                        <li>
                                            <a data-href="#acl-policies-section">
                                                <i class="fa fa-shield"></i> 访问策略
                                            </a>
                                        </li>
                                        <li>
                                            <a data-href="#acl-json-section">
                                                <i class="fa fa-code"></i> JSON 编辑器
                                            </a>
                                        </li>
                                    </ul>

                                    <!-- ACL Builder Content -->
                                    <div id="acl-builder-content" class="tab-content" style="margin-top: 20px;">
                                        <!-- Groups Section -->
                                        <div id="acl-groups-section" class="tab-pane fade active">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>用户组管理</strong></h6>
                                                    <p class="text-muted">用户组是具有相同权限范围的用户集合。一个用户可以属于多个组。</p>
                                                    <button type="button" class="btn btn-primary btn-sm" id="addGroup">
                                                        <i class="fa fa-plus"></i> 添加用户组
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row" style="margin-top: 15px;">
                                                <div class="col-md-12">
                                                    <div id="groupsList" class="acl-items-container">
                                                        <!-- Groups will be rendered here -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Tag Owners Section -->
                                        <div id="acl-tagowners-section" class="tab-pane fade">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>标签所有者管理</strong></h6>
                                                    <p class="text-muted">定义哪些用户或组可以为设备分配特定标签。</p>
                                                    <button type="button" class="btn btn-primary btn-sm" id="addTagOwner">
                                                        <i class="fa fa-plus"></i> 添加标签所有者
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row" style="margin-top: 15px;">
                                                <div class="col-md-12">
                                                    <div id="tagOwnersList" class="acl-items-container">
                                                        <!-- Tag owners will be rendered here -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hosts Section -->
                                        <div id="acl-hosts-section" class="tab-pane fade">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>主机定义管理</strong></h6>
                                                    <p class="text-muted">定义命名的IP地址或网络段，必须使用CIDR格式。</p>
                                                    <button type="button" class="btn btn-primary btn-sm" id="addHost">
                                                        <i class="fa fa-plus"></i> 添加主机定义
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row" style="margin-top: 15px;">
                                                <div class="col-md-12">
                                                    <div id="hostsList" class="acl-items-container">
                                                        <!-- Hosts will be rendered here -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Policies Section -->
                                        <div id="acl-policies-section" class="tab-pane fade">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>访问策略管理</strong></h6>
                                                    <p class="text-muted">定义网络访问规则，控制源和目标之间的通信。</p>
                                                    <button type="button" class="btn btn-primary btn-sm" id="addPolicy">
                                                        <i class="fa fa-plus"></i> 添加访问策略
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row" style="margin-top: 15px;">
                                                <div class="col-md-12">
                                                    <div id="policiesList" class="acl-items-container">
                                                        <!-- Policies will be rendered here -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- JSON Editor Section -->
                                        <div id="acl-json-section" class="tab-pane fade">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>JSON 编辑器</strong></h6>
                                                    <p class="text-muted">高级用户可以直接编辑ACL的JSON配置。</p>
                                                    <div class="form-group">
                                                        <label for="aclEditor">ACL 策略 (JSON格式):</label>
                                                        <textarea class="form-control" id="aclEditor" rows="20" placeholder="请输入ACL策略的JSON配置..."></textarea>
                                                    </div>
                                                    <div class="alert alert-warning">
                                                        <i class="fa fa-warning"></i>
                                                        <strong>注意：</strong> 直接编辑JSON可能会覆盖通过可视化界面所做的更改。建议先备份当前配置。
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Namespace Modal -->
<div class="modal fade" id="addNamespaceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">创建命名空间</h4>
            </div>
            <div class="modal-body">
                <form id="addNamespaceForm">
                    <div class="form-group">
                        <label for="namespaceName">命名空间名称:</label>
                        <input type="text" class="form-control" id="namespaceName" name="namespaceName" required
                               placeholder="请输入命名空间名称">
                        <small class="form-text text-muted">
                            命名空间名称只能包含字母、数字、下划线和连字符。需要gRPC API支持。
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAddNamespace">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">添加用户</h4>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" class="form-control" id="username" name="username" required
                               placeholder="请输入用户名（不能是纯数字）">
                        <small class="form-text text-muted">
                            用户名只能包含字母、数字、下划线和连字符，且不能是纯数字
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="displayName">显示名称 (命名空间):</label>
                        <input type="text" class="form-control" id="displayName" name="displayName"
                               placeholder="可选：输入显示名称/命名空间">
                        <small class="form-text text-muted">
                            可选字段。如果提供，将尝试使用gRPC API创建用户和命名空间。只能包含字母、数字、下划线和连字符
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAddUser">添加</button>
            </div>
        </div>
    </div>
</div>

<!-- PreAuth Keys Modal -->
<div class="modal fade" id="preAuthKeysModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">预授权密钥管理</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm" id="createPreAuthKey">
                            <i class="fa fa-plus"></i> 创建密钥
                        </button>
                    </div>
                </div>
                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-12">
                        <table class="table table-striped" id="preAuthKeysTable">
                            <thead>
                                <tr>
                                    <th>密钥</th>
                                    <th>可重用</th>
                                    <th>临时</th>
                                    <th>已使用</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- PreAuth keys will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Route Management Modal -->
<div class="modal fade" id="routeManagementModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">路由管理</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <h5>设备: <span id="routeDeviceName"></span></h5>
                        <p>用户: <span id="routeDeviceUser"></span></p>
                        <p>IP地址: <span id="routeDeviceIP"></span></p>
                    </div>
                </div>
                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-6">
                        <h6><strong>可用路由</strong></h6>
                        <div id="availableRoutes" class="route-list">
                            <!-- Available routes will be loaded here -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><strong>已批准路由</strong></h6>
                        <div id="approvedRoutes" class="route-list approved-routes">
                            <!-- Approved routes will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="saveRouteChanges">保存更改</button>
            </div>
        </div>
    </div>
</div>

<!-- ACL Group Modal -->
<div class="modal fade" id="aclGroupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">用户组管理</h4>
            </div>
            <div class="modal-body">
                <form id="aclGroupForm">
                    <div class="form-group">
                        <label for="groupName">组名称:</label>
                        <input type="text" class="form-control" id="groupName" name="groupName" required
                               placeholder="例如: group:admin">
                        <small class="form-text text-muted">
                            组名称通常以 "group:" 开头，只能包含字母、数字、下划线和连字符
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="groupUsers">组成员:</label>
                        <textarea class="form-control" id="groupUsers" name="groupUsers" rows="4"
                                  placeholder="每行一个用户，例如:&#10;admin@&#10;user1@&#10;user2@"></textarea>
                        <small class="form-text text-muted">
                            每行输入一个用户名，用户名通常以 @ 结尾
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmGroupAction">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- ACL Tag Owner Modal -->
<div class="modal fade" id="aclTagOwnerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">标签所有者管理</h4>
            </div>
            <div class="modal-body">
                <form id="aclTagOwnerForm">
                    <div class="form-group">
                        <label for="tagName">标签名称:</label>
                        <input type="text" class="form-control" id="tagName" name="tagName" required
                               placeholder="例如: tag:server">
                        <small class="form-text text-muted">
                            标签名称通常以 "tag:" 开头，只能包含字母、数字、下划线和连字符
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="tagOwners">授权用户/组:</label>
                        <textarea class="form-control" id="tagOwners" name="tagOwners" rows="4"
                                  placeholder="每行一个用户或组，例如:&#10;admin@&#10;group:admin&#10;user1@"></textarea>
                        <small class="form-text text-muted">
                            每行输入一个用户名或组名，可以混合使用
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmTagOwnerAction">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- ACL Host Modal -->
<div class="modal fade" id="aclHostModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">主机定义管理</h4>
            </div>
            <div class="modal-body">
                <form id="aclHostForm">
                    <div class="form-group">
                        <label for="hostName">主机名称:</label>
                        <input type="text" class="form-control" id="hostName" name="hostName" required
                               placeholder="例如: database.internal">
                        <small class="form-text text-muted">
                            主机名称用于在策略中引用，建议使用描述性名称
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="hostAddress">IP地址/网络:</label>
                        <input type="text" class="form-control" id="hostAddress" name="hostAddress" required
                               placeholder="例如: ********/32 或 ***********/24">
                        <small class="form-text text-muted">
                            必须使用CIDR格式。单个主机使用 /32，网络段使用相应的子网掩码
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmHostAction">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- ACL Policy Modal -->
<div class="modal fade" id="aclPolicyModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">访问策略管理</h4>
            </div>
            <div class="modal-body">
                <form id="aclPolicyForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="policyAction">动作:</label>
                                <select class="form-control" id="policyAction" name="policyAction" required>
                                    <option value="accept">允许 (accept)</option>
                                    <option value="deny">拒绝 (deny)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="policyProtocol">协议:</label>
                                <select class="form-control" id="policyProtocol" name="policyProtocol">
                                    <option value="">任意协议</option>
                                    <option value="tcp">TCP</option>
                                    <option value="udp">UDP</option>
                                    <option value="icmp">ICMP</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="policySources">源 (Source):</label>
                        <textarea class="form-control" id="policySources" name="policySources" rows="3" required
                                  placeholder="每行一个源，例如:&#10;group:admin&#10;user1@&#10;tag:server"></textarea>
                        <small class="form-text text-muted">
                            可以是用户、组、标签或主机名称，每行一个
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="policyDestinations">目标 (Destination):</label>
                        <textarea class="form-control" id="policyDestinations" name="policyDestinations" rows="3" required
                                  placeholder="每行一个目标，例如:&#10;tag:database:5432&#10;group:dev:*&#10;10.0.0.0/24:80,443"></textarea>
                        <small class="form-text text-muted">
                            格式: 目标:端口。端口可以是具体端口号、端口范围或 * (所有端口)
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmPolicyAction">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- ACL Template Modal -->
<div class="modal fade" id="aclTemplateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">ACL 模板选择</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <p class="text-muted">选择一个预定义的ACL模板作为起点：</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default template-option" data-template="basic">
                            <div class="panel-body text-center">
                                <i class="fa fa-shield fa-2x text-primary"></i>
                                <h5>基础模板</h5>
                                <p class="text-muted">简单的用户组和基本访问规则</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="panel panel-default template-option" data-template="enterprise">
                            <div class="panel-body text-center">
                                <i class="fa fa-building fa-2x text-success"></i>
                                <h5>企业模板</h5>
                                <p class="text-muted">包含管理员、开发者、实习生等角色</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default template-option" data-template="homelab">
                            <div class="panel-body text-center">
                                <i class="fa fa-home fa-2x text-info"></i>
                                <h5>家庭实验室</h5>
                                <p class="text-muted">适合个人或家庭网络环境</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="panel panel-default template-option" data-template="empty">
                            <div class="panel-body text-center">
                                <i class="fa fa-file-o fa-2x text-warning"></i>
                                <h5>空白模板</h5>
                                <p class="text-muted">从头开始创建ACL配置</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmTemplateSelection" disabled>使用选中模板</button>
            </div>
        </div>
    </div>
</div>

<style>
.route-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

.route-list .checkbox {
    margin: 5px 0;
}

.route-list .checkbox label {
    font-weight: normal;
}

.route-item {
    margin: 5px 0;
}

.route-item .label {
    display: inline-block;
    margin: 2px 0;
    padding: 6px 10px;
    font-size: 12px;
}

.approved-routes {
    background-color: #f9f9f9;
}

.approved-routes .route-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.approved-routes .route-item:last-child {
    border-bottom: none;
}

.approved-routes .label-success {
    background-color: #5cb85c;
    color: white;
    font-weight: normal;
}

/* ACL Builder Styles */
.acl-items-container {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
}

.acl-item {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
    transition: all 0.2s ease;
}

.acl-item:hover {
    border-color: #337ab7;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.acl-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.acl-item-title {
    font-weight: bold;
    color: #333;
    margin: 0;
}

.acl-item-actions {
    display: flex;
    gap: 5px;
}

.acl-item-content {
    color: #666;
    font-size: 14px;
}

.acl-item-content .label {
    margin-right: 5px;
    margin-bottom: 3px;
}

.acl-empty-state {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px 20px;
}

.acl-empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

/* Template Selection Styles */
.template-option {
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 15px;
}

.template-option:hover {
    border-color: #337ab7;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.template-option.selected {
    border-color: #337ab7;
    background-color: #f0f8ff;
}

.template-option .panel-body {
    padding: 20px;
}

.template-option i {
    margin-bottom: 10px;
}

.template-option h5 {
    margin: 10px 0 5px 0;
    font-weight: bold;
}

/* ACL Builder Navigation */
#aclBuilderNav {
    border-bottom: 1px solid #ddd;
    margin-bottom: 0;
}

#aclBuilderNav li a {
    border-radius: 4px 4px 0 0;
    margin-right: 2px;
}

#aclBuilderNav li.active a {
    background-color: #337ab7;
    border-color: #337ab7;
    color: white;
}

#aclBuilderNav li a:hover {
    background-color: #f5f5f5;
}

#aclBuilderNav li.active a:hover {
    background-color: #337ab7;
    color: white;
}

/* Policy specific styles */
.policy-rule {
    background-color: #f8f9fa;
    border-left: 4px solid #28a745;
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 0 4px 4px 0;
}

.policy-rule.deny {
    border-left-color: #dc3545;
}

.policy-rule-header {
    font-weight: bold;
    margin-bottom: 5px;
}

.policy-rule-details {
    font-size: 13px;
    color: #666;
}

.policy-rule-protocol {
    display: inline-block;
    background-color: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 5px;
}

/* Validation styles */
.validation-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.validation-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.validation-message {
    font-size: 12px;
    margin-top: 5px;
}

.validation-message.error {
    color: #dc3545;
}

.validation-message.success {
    color: #28a745;
}
</style>

<script type="text/javascript" src="view/headscale/headscale.js"></script>
