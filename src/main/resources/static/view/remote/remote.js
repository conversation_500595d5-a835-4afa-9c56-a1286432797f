!function () {
    var i,confirmPass , l = $("#deviceTable"), c = Frame.INFO.device;

    function r(e) {
        return 0 == e ? '<div class="abnormal"><b class="fa fa-times-circle"></b><span>' + c.offline + "</span></div>" : '<div class="normal"><b class="fa fa-check-circle"></b><span>' + c.online + "</span></div>"
    }

    $('#myTab a').click(function () {
        $("#myTab").find("li[class ='active']").removeClass("active");
        $(this).parent().addClass("active");
        let href = $(this).attr("data-href");
        $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
        $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');
    })

    function u(e) {
        let a = {page: 1, size: $(".toolbar select.pagenum", l).val()};
        let inputArr = $(".filter input", l);
        let t = "";
        let sn = "";
        if (inputArr.length > 1) {
            t = $(inputArr[0]).val();
            sn = $(inputArr[1]).val();
        } else if (inputArr.length == 1) {
            sn = $(inputArr[0]).val();
        }
        let n = $(".filter select", l).val();
        let version = $(".filter select", l).eq(1).val();
        t && (a.name = t);
        sn && (a.sn = sn);
        "all" != n && (a.model = n);
        "all" != version && (a.verNo = version);
        e && $.extend(a, e), i.refresh(a)
    }

    function remote(e) {
        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "remote/runContainer", data: {userDeviceId: e.id, passwd: md5(pass)}, onSuccess: function(data){
                let href = window.location.origin+"/"+data;
                setHref(href);
            }})
        }
        Frame.Msg.confirm({
            content: "确认远程该设备？",
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: c.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function setHref(href){
        let src1 = $("#remoteFrame2").attr("src");
        let src2 = $("#remoteFrame3").attr("src");
        let src3 = $("#remoteFrame4").attr("src");
        let src4 = $("#remoteFrame5").attr("src");
        let src5 = $("#remoteFrame6").attr("src");

        if (!Frame.Util.isNotBlank(src1)){
            $("#remoteFrame2").attr("src",href);
            $("#tab2A").click();
        } else if (!Frame.Util.isNotBlank(src2)){
            $("#remoteFrame3").attr("src",href);
            $("#tab3A").click();
        } else if (!Frame.Util.isNotBlank(src3)){
            $("#remoteFrame4").attr("src",href);
            $("#tab4A").click();
        } else if (!Frame.Util.isNotBlank(src4)){
            $("#remoteFrame5").attr("src",href);
            $("#tab5A").click();
        } else if (!Frame.Util.isNotBlank(src5)){
            $("#remoteFrame6").attr("src",href);
            $("#tab6A").click();
        } else{
            $("#remoteFrame2").attr("src",href);
            $("#tab2A").click();
        }
    }

    function lanRemote(e) {
        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "remote/lanConnection", data: {userDeviceId: e.id, passwd: md5(pass)}, onSuccess: function(data){
                    let href = window.location.origin+"/"+data;
                    setHref(href);
                }})
        }
        Frame.Msg.confirm({
            content: "确认远程该设备？",
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: c.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function a() {
        var e = {
            url: "device/userDeviceList?online=1",
            //url: "device/userDeviceList",
            pagination: !0,
            columns: [
                {name: "id", title: "id"},
                {name: "name", title: c.devname},
                {name: "model", title: c.type},
                {name: "sn", title: c.sn},
                {name: "subIp",title: c.subnet},
                {name: "dumIp", title: c.vip},
                {name: "status", title: c.status, format: r},
                {name: "deviceStatus", title: c.deviceStatus, format: deviceStatusFormatter},
                {name: "verNo",title: c.version},
                {name: "company", title: c.company, role: "0"},
                {name: "username",title: c.username,role: "0"},
                {name: "describ", title: c.describ, role: "0,1,2"}
            ],
            operations: [
                {name: "upload", title: "VPN远程", action: remote},
                {name: "upload", title: "局域网远程", action: lanRemote},
            ],
            toolbar: {
                search: [
                    {name: "input", placeholder: Frame.INFO.search.username, action: u, role: "0"},
                    {name: "input", placeholder: Frame.INFO.search.sn, action: u},
                    {name: "select", content: Frame.createModel(!0), action: u},
                    {name: "select", content: verNoSelect, action: u},
                ],
                operations: [{name: "refresh", action: u}]
            },
            onSelectPage: function (e) {
                u({size: e})
            },
            onChangePageSize: function (e) {
                u({page: e})
            }
        };
        i = l.datagrid(e);

        var t = $("#confirm-pass").html();
        t = Frame.replaceTitle(t, {"{againPass}": c.passwd});
        confirmPass = $(t);
        $("#confirm-pass").empty().remove();

        let height = document.body.clientHeight - 130;
        $("iframe[class='remoteFrame']").height(height);
    }


    function deviceStatusFormatter(e) {
        return e == 0 ? c.normal : c.alarm;
    }

    function verNoSelect() {
        let str = `<option value="all">${c.version}</option>`;
        Frame.getRequest({
            url: "/device/versionList", async: false, onSuccess: function (data) {
                for (let j = 0; j < data.length; j++) {
                    str += ` <option value="${data[j]}">${data[j]}</option>`;
                }
            }
        });
        return str;
    }

    function onlineSelect() {
        let str = `<option value="all">${c.status}</option>
                    <option value="0">${c.offline}</option>
                     <option value="1">${c.online}</option>`;
        return str;
    }


    Frame.destory = function () {
        i && i.destory(), c = confirmPass = l = null;
    }, $(function (e) {
        a()
    })
}();