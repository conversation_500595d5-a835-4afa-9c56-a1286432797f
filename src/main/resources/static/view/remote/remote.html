<div id="remote">
    <ul class="nav nav-tabs" id="myTab">
        <li class="active"><a data-href="#tab1">设备</a></li>
        <li><a id="tab2A" data-href="#tab2">远程1</a></li>
        <li><a id="tab3A" data-href="#tab3">远程2</a></li>
        <li><a id="tab4A" data-href="#tab4">远程3</a></li>
        <li><a id="tab5A" data-href="#tab5">远程4</a></li>
        <li><a id="tab6A" data-href="#tab6">远程5</a></li>
    </ul>
    <div id="mytab-content2" class="tab-content">
        <div id="tab1" class="tab-pane fade active">
            <style type="text/css" scoped>
                #tab1 .toolbar select[class='form-control'],#tab1 .toolbar input[class='form-control']{
                    width: 30%;
                }
                #tab1{
                    overflow: auto;
                }
            </style>
            <div id="deviceTable"></div>
        </div>
        <div  class="tab-pane fade" id="tab2">
            <iframe id="remoteFrame2" class="remoteFrame"  src="" style="width: 100%;"></iframe>
        </div>
        <div  class="tab-pane fade" id="tab3">
            <iframe id="remoteFrame3" class="remoteFrame" src="" style="width: 100%;"></iframe>
        </div>
        <div  class="tab-pane fade" id="tab4">
            <iframe id="remoteFrame4" class="remoteFrame" src="" style="width: 100%;"></iframe>
        </div>
        <div  class="tab-pane fade" id="tab5">
            <iframe id="remoteFrame5" class="remoteFrame" src="" style="width: 100%;"></iframe>
        </div>
        <div  class="tab-pane fade" id="tab6">
            <iframe id="remoteFrame6" class="remoteFrame" src="" style="width: 100%;"></iframe>
        </div>
    </div>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script src="view/remote/remote.js?v=88888" type="text/javascript"></script>