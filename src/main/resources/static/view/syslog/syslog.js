!function () {
    var i, l = $("#syslog"), a = Frame.INFO.users;

    function n(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", l).val()}
        , a = $(".filter input:first-child", l).val();
        let content = $(".filter input", l).eq(1).val();
        a && (t.name = a);

        $(".filter select", l).val();
        var n = $(".filter input:last-child", l).val().split(" ~ ");
        2 == n.length && (t.start = n[0], t.end = n[1]);
        content && (t.content = content);
        if (content != "" && t.start == undefined){
            Frame.Msg.error("根据内容查询时，需指定时间范围");
            return;
        }
        e && $.extend(t, e), i.refresh(t)
    }

    function delList(){
        let start,end = null;
        let param = {};
        var element = $(".filter input:last-child", l).val().split(" ~ ");
        if (2 == element.length) {
            start = element[0];
            end = element[1]
        }
        if (start == undefined || start == null
            || end == undefined || end == null ) {
            Frame.Msg.error(Frame.INFO.msg.deleteDateTips);
            return ;
        }
        param.startDate =  new Date(Date.parse(start));
        param.endDate =  new Date(Date.parse(end));
        Frame.Msg.confirm({
            content:  `确认要删除${start} - ${end}(不包含) 数据吗？`,
            onConfirm: function i() {
                Frame.setRequest({
                    url: "log/delByDate",
                    data:param,
                    onSuccess: n
                })
            }
        })
    }

    function tableContentFormat(cellValue){
        if (!Frame.Util.isNotBlank(cellValue)){
            return cellValue;
        }
        cellValue = cellValue.trim();
        if (cellValue.length > 65) {

        }
        let num =  Math.floor(cellValue.length/65);
        for (let j = num; j > 0; j--) {
            cellValue = cellValue.slice(0, j * 65) + "<br/>" + cellValue.slice(j * 65);
        }
        return cellValue;
    }

    function t() {
        !function t() {
            var e = {
                url: "log/list",
                pagination: !0,
                columns: [
                    {name: "name", title: a.username},
                    {name: "content", title: a.content,format:tableContentFormat},
                    {name: "gmtCreate",title: a.time,format: Frame.Time.formatTime}],
                toolbar: {
                    search: [{name: "input", placeholder: Frame.INFO.search.username, action: n},
                        {name: "input", placeholder: a.content, action: n},
                        {
                        name: "input",
                        placeholder: Frame.Time.getDay(-7, "Y-M-D") + " ~ " + Frame.Time.getDay(0, "Y-M-D"),
                        action: n
                    }], operations: [{name: "refresh", action: n} ,{name: "remove", action: delList,role:"0,1"}]
                },
                onSelectPage: function (e) {
                    n({size: e})
                },
                onChangePageSize: function (e) {
                    n({page: e})
                }
            };
            i = l.datagrid(e)
        }(), function e() {
            $("#upload").empty().remove()
        }(), $("input:last-child").attr("readonly", !0), $("input:last-child").css({"background-color": "#fff"}), laydate.render({
            elem: $("input:last-child")[0],
            range: "~",
            btns: ["clear", "confirm"],
            min: "1970-01-01",
            theme: "molv",
            lang: Frame.Default.lang,
            done: function (e) {
                var t = e.split(" ~ ");
                "" == e ? n() : 2 == t.length && n({start: t[0], end: t[1]})
            }
        })
    }

    Frame.destory = function () {
        i && i.destory(), a = l = null
    }, $(function (e) {
        t()
    })
}();