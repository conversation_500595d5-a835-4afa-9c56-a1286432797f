!function () {
    let deviceTable, deviceTableDiv = $("#deviceTable"),logTable,logTableDiv = $("#logTable");
    let staTable,staTableDiv = $("#logStatistics");
    let a = Frame.INFO.users,c = Frame.INFO.device,logText = Frame.INFO.log;
    let logChart;

    function queryDevice(e) {
        let a = {page: 1, size: $(".toolbar select.pagenum", deviceTableDiv).val()};
        let sn = $(".filter input[name='sn']", deviceTableDiv).val();
        let alias = $(".filter input[name='alias']", deviceTableDiv).val();
        sn && (a.sn = sn);
        alias && (a.alias = alias);
        e && $.extend(a, e), deviceTable.refresh(a)
    }

    function showLog(e){
        e.find("a").on("click", function () {
            var e = $(this).closest("tr").data("data");
            $("#myTab").find("a[data-href='#rule3']").click();
            if (logTable == null) {
                initLog(e.sn);
            } else {
                let param = {"sn":e.sn};
                queryLog(param);
            }
            $(".filter input[name='sn']", logTableDiv).val(e.sn);
        })
    }

    function initDevice() {
        var e = {
            url: "device/userDeviceList?havePath=1",
            pagination: true,
            columns: [
                {name: "id", title: "id"},
                {name: "name", title: c.devname, format: function (e) {return "<a>" + e + "</a>"}, action: showLog},
                {name: "alias", title: c.alias},
                {name: "model", title: c.type},
                {name: "sn", title: c.sn},
                {name: "signal5G", title: c.signal5G},
                {name: "villageNum", title: c.villageNum},
                {name: "status", title: c.status, format: statusFormatter},
                {name: "deviceStatus", title: c.deviceStatus, format: deviceStatusFormatter},
                {name: "company", title: c.company, role: "0"},
                {name: "username",title: c.username,role: "0"},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"sn", placeholder: Frame.INFO.search.sn, action: queryDevice},
                    {name: "input",name2:"alias", placeholder: Frame.INFO.search.alias, action: queryDevice}
                ],
                operations: [{name: "refresh", action: queryDevice}]
            },
            onSelectPage: function (e) {
                queryDevice({size: e})
            },
            onChangePageSize: function (e) {
                queryDevice({page: e})
            }
        };
        deviceTable = deviceTableDiv.datagrid(e)

        function statusFormatter(e) {
            return 0 == e ? '<div class="abnormal"><b class="fa fa-times-circle"></b><span>' + c.offline + "</span></div>" : '<div class="normal"><b class="fa fa-check-circle"></b><span>' + c.online + "</span></div>"
        }

        function deviceStatusFormatter(e) {
            return e == 0 ? c.normal : `<span style="color: red;">${c.alarm}</span>`;
        }
    };

    function queryStatistics(e){
        let a = {page: 1, size: $(".toolbar select.pagenum", staTableDiv).val()};
        let dateArr = $(".filter input[name='date']", staTableDiv).val().split(" ~ ");
        let model = $(".filter select", staTableDiv).val();
        2 == dateArr.length && (a.start = dateArr[0], a.end = dateArr[1]);
        "all" != model && (a.model = model);
        e && $.extend(a, e), staTable.refresh(a);
    }

    function initStatistics(){
        var e = {
            url: "deviceLog/statistics",
            data:{"level":"ERROR"},
            pagination: true,
            columns: [
                {name: "model", title: c.type},
                {name: "error", title: logText.content},
                {name: "num", title: logText.num},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"date",
                        placeholder: Frame.Time.getDay(-1, "Y-M-D h:m:s") + " ~ " + Frame.Time.getDay(0, "Y-M-D h:m:s"),
                        action: queryStatistics
                    },
                    {name: "select", content: Frame.createModel(!0), action: queryStatistics},
                ],
            },
            onSelectPage: function (e) {
                queryStatistics({size: e})
            },
            onChangePageSize: function (e) {
                queryStatistics({page: e})
            },
        };

        staTable = staTableDiv.datagrid(e);

        $("input[name='date']",staTable).attr("readonly", true);
        $("input[name='date']",staTable).css({"background-color": "#fff"});
        laydate.render({
            elem: $("input[name='date']",staTableDiv)[0],
            range: "~",
            type:"datetime",
            btns: ["clear", "confirm"],
            min: "1970-01-01",
            theme: "molv",
            lang: Frame.Default.lang,
            done: function (e) {
                if ("" == e) {
                    queryStatistics()
                } else {
                    let  t = e.split(" ~ ");
                    queryStatistics({start: t[0], end: t[1]})
                }
            }
        })
    }

    function queryLog(e) {
        let a = {page: 1, size: $(".toolbar select.pagenum", logTableDiv).val()};
        let sn = $(".filter input[name='sn']", logTableDiv).val();
        let content = $(".filter input[name='content']", logTableDiv).val();
        let level = $(".filter select", logTableDiv).val();
        let dateArr = $(".filter input[name='date']", logTableDiv).val().split(" ~ ");
        sn && (a.sn = sn);
        content && (a.content = content);
        level && (a.level = level);
        2 == dateArr.length && (a.start = dateArr[0], a.end = dateArr[1]);
        e && $.extend(a, e), logTable.refresh(a)
    }

    function initLog(sn) {
        var e = {
            url: "deviceLog/list",
            data:{sn:sn},
            pagination: {pageList: [200,500,1000],},
            //pagination: true,
            columns: [
                {name: "content", title: logText.content},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"sn", placeholder: Frame.INFO.search.sn, action: queryLog},
                    {name: "input",name2:"content", placeholder: Frame.INFO.search.content, action: queryLog},
                    {name: "select",name2:"level", content: levelContent, action: queryLog},
                    {name: "input",name2:"date",
                        placeholder: Frame.Time.getDay(-1, "Y-M-D h:m:s") + " ~ " + Frame.Time.getDay(0, "Y-M-D h:m:s"),
                        action: queryLog
                    }
                ],
            },
            onSelectPage: function (e) {
                queryLog({size: e})
            },
            onChangePageSize: function (e) {
                queryLog({page: e})
            },
            onComplete: function (e) {
                //initChart(e);
            }
        };

        logTable = logTableDiv.datagrid(e);

        $("input[name='date']",logTableDiv).attr("readonly", true);
        $("input[name='date']",logTableDiv).css({"background-color": "#fff"});
        laydate.render({
            elem: $("input[name='date']",logTableDiv)[0],
            range: "~",
            type:"datetime",
            btns: ["clear", "confirm"],
            min: "1970-01-01",
            theme: "molv",
            lang: Frame.Default.lang,
            done: function (e) {
                if ("" == e) {
                    queryLog()
                } else {
                    let  t = e.split(" ~ ");
                    queryLog({start: t[0], end: t[1]})
                }
            }
        })

        function levelContent(e) {
            let str = `<option value=""></option>
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARN">WARN</option>
                    <option value="ERROR">ERROR</option>`;
            return str;
        }
        function initChart(e) {
            let option = {
                title: {
                    text: '分时错误统计',
                },
                xAxis: {
                    type: 'category',
                    data: e.dateList
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: e.numList,
                        type: 'bar'
                    }
                ]
            };
            let chartDom = document.getElementById('main');
            if (logChart != null) {
                logChart.dispose();
            }
            logChart = echarts.init(chartDom);
            logChart.setOption(option);
        }
    }

    function init() {
        initDevice();
    }

    $('#myTab a').click(function () {
        $("#myTab").find("li[class ='active']").removeClass("active");
        $(this).parent().addClass("active");
        let href = $(this).attr("data-href");
        $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
        $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');
        if (href == '#rule2' && staTable == null) {
            initStatistics();
        }
    })

    Frame.destory = function () {
        deviceTable && deviceTable.destory(), a = deviceTableDiv = null
    }, $(function (e) {
        init()
    })
}();