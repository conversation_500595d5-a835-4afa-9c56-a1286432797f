<div>
    <style type="text/css" scoped>
        #device .toolbar select[class='form-control'],#device .toolbar input[class='form-control']{
            width: 30%;
        }
        #device{
            overflow: auto;
        }
        .date_input {
            min-width: 100px;
        }
        .center{
            text-align: center;
        }
    </style>
    <div id="device" >

    </div>
</div>
<div class="hide" id="add"></div>
<div class="detail-popover fade" id="deviceDetail" style="display: none">
    <div class="panel panel-default">
        <div class="panel-heading"><span class="title">详细信息</span> <span class="close">X</span></div>
        <div class="panel-body">
            <div class="device-info"></div>
            <hr>
            <div class="row">
                <div class="col-sm-2">
                    <div class="input-group">
                        <input type="text" id="month" class="form-control date_input " aria-describedby="basic-addon2" readonly>
                        <span class="input-group-addon fa fa-calendar" id="basic-addon2"></span>
                    </div>
                </div>
                <div class="col-sm-2">
                    <div class="input-group">
                        <input type="text" id="day" class="form-control date_input " aria-describedby="basic-addon3" readonly>
                        <span class="input-group-addon fa fa-calendar" id="basic-addon3"></span>
                    </div>
                </div>
            </div>

            <div id="main" style="width: 100%;height:300px;"></div>
            <div id="tree" style="width: 100%;height:300px;"></div>
            <div class="input-group">
                <input type="text" id="onlineDate" class="form-control date_input " aria-describedby="basic-addon4" readonly>
                <span class="input-group-addon fa fa-calendar" id="basic-addon4"></span>
            </div>
            <div id="onlineChart" style="width: 100%;height:300px;"></div>
            <div id="rssiChart" style="width: 100%;height:300px;"></div>
            <div id="temperatureChart" style="width: 100%;height:300px;"></div>
            <div id="rsrqChart" style="width: 100%;height:300px;"></div>
            <div id="qualityChart" style="width: 100%;height:300px;"></div>
            <div id="cpuChart" style="width: 100%;height:300px;"></div>
            <div id="memoryChart" style="width: 100%;height:300px;"></div>
        </div>
    </div>
</div>
<div class="detail-popover fade" id="networkDiagnosis" style="display: none">
    <div class="panel panel-default">
        <div class="panel-heading"><span class="title">网络诊断</span> <span class="close">X</span></div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-2">
                    <span>MQTT</span>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2">
                    <button id="oneClickRepairBtn">一键修复</button>
                </div>
            </div>
            <div class="row">
                <div class="row">
                    <div class="col-md-2 center">
                        MQTT容器
                    </div>
                    <div class="col-md-2 center">
                        MySQL插件
                    </div>
                    <div class="col-md-8 center">
                        修复建议
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 center" id="mqttContainerDiv"></div>
                    <div class="col-md-2 center" id="mysqlDiv"></div>
                    <div class="col-md-8 center" id="mqttAdviseDiv"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <span>VPN</span>
                </div>
            </div>
            <div class="row">
                <div class="row">
                    <div class="col-md-2 center">
                        VPN容器
                    </div>
                    <div class="col-md-2 center">
                        VPN进程
                    </div>
                    <div class="col-md-8 center">
                        修复建议
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 center" id="vpnContainerDiv"></div>
                    <div class="col-md-2 center" id="threadDiv"></div>
                    <div class="col-md-8 center" id="vpnAdviseDiv"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <span>网关</span>
                </div>
            </div>
            <div id="deviceNetworkTable"></div>
        </div>
    </div>
</div>
<div class="detail-popover fade" id="onOffLinePanel" style="display: none">
    <div class="panel panel-default">
        <div class="panel-heading"><span class="title">上下线总览</span> <span class="close">X</span></div>
        <div class="panel-body">
            <div class="row">
                <div class="input-group">
                    <input type="text" id="onOffLineDate" class="form-control date_input " aria-describedby="basic-addon5" readonly>
                    <span class="input-group-addon fa fa-calendar" id="basic-addon5"></span>
                </div>
                <div id="onOffLineTableDiv"></div>
            </div>
        </div>
    </div>
</div>
<div id="restForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="sn" class="col-sm-3 control-label required">{sn}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" readonly id="sn" name="sn" ></div>
        </div>
        <div class="form-group"><label for="passwd" class="col-sm-3 control-label required">{passwd}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="passwd" name="passwd" ></div>
        </div>
    </form>
</div>
<div class="hide" id="updateForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="alias" class="col-sm-3 control-label required">{alias}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="alias" name="alias" maxlength="64">
            </div>
        </div>
        <div class="form-group"><label for="position" class="col-sm-3 control-label required">{position}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="position" name="position" maxlength="64"></div>
        </div>
        <div class="form-group"><label for="flow" class="col-sm-3 control-label required">{flow}</label>
            <div class="col-sm-6"><input type="number" class="form-control required" id="flow" name="flow" maxlength="64"></div>
        </div>

    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>

<div id="networkSetUp" style="display: none">
    <div class="panel-heading"><span class="title"></span> <span class="close">X</span></div>
    <ul class="nav nav-tabs" id="myTab">
        <li class="active"><a data-href="#rule1">NTP设置</a></li>
        <!--<li ><a data-href="#rule2">5G设置</a></li>
        <li ><a data-href="#rule3">APN设置</a></li>-->
        <li ><a data-href="#rule4">绑定IP设置</a></li>
        <li ><a data-href="#rule5">网络监控</a></li>
        <li ><a data-href="#rule6">端口映射规则</a></li>
        <li ><a data-href="#rule7">LAN IP</a></li>
        <li ><a data-href="#rule8">NAT规则</a></li>
    </ul>
    <div id="mytab-content2" class="tab-content">
        <div class="tab-pane fade active" id="rule1">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required"  value=""  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="ntpServer" class="col-sm-3 control-label required">NTP服务器：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="ntpServer" name="ntpServer" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="interval" class="col-sm-3 control-label required">间隔：</label>
                    <div class="col-sm-6">
                        <select id="interval" name="interval" class="form-control form-control-placeholder">
                            <option value="0" data-type="0" style="color: black;" selected>自动</option>
                            <option value="10" data-type="1" style="color: black;">10分钟</option>
                            <option value="20" data-type="1" style="color: black;">20分钟</option>
                            <option value="30" data-type="1" style="color: black;">30分钟</option>
                            <option value="1" data-type="2" style="color: black;">1小时</option>
                            <option value="2" data-type="2" style="color: black;">2小时</option>
                            <option value="4" data-type="2" style="color: black;">4小时</option>
                            <option value="8" data-type="2" style="color: black;">8小时</option>
                            <option value="24" data-type="2" style="color: black;">1天</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn1">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule2">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" value=""  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="fiveG" class="col-sm-3 control-label required">5G启用：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="fiveG" name="fiveG" />
                    </div>
                </div>
                <div class="form-group"><label for="networkType" class="col-sm-3 control-label required">网络类型：</label>
                    <div class="col-sm-6">
                        <input type="radio" class="required" value="NSA" name="networkType" />NSA
                    </div>
                    <div class="col-sm-6">
                        <input type="radio" class="required" value="SA" name="networkType" />SA
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn2">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule3">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required"  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="name" class="col-sm-3 control-label required">名称：</label>
                    <div class="col-sm-6"><input type="text" class="form-control required" id="name" name="name" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="apn" class="col-sm-3 control-label required">APN：</label>
                    <div class="col-sm-6"><input type="text" class="form-control required" id="apn" name="apn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="proxy" class="col-sm-3 control-label ">代理：</label>
                    <div class="col-sm-6"><input type="text" class="form-control" id="proxy" name="proxy" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="port" class="col-sm-3 control-label ">端口：</label>
                    <div class="col-sm-6"><input type="number" class="form-control" id="port" name="port" maxlength="5">
                    </div>
                </div>
                <div class="form-group"><label for="userName" class="col-sm-3 control-label ">用户名：</label>
                    <div class="col-sm-6"><input type="text" class="form-control" id="userName" name="userName" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="pass" class="col-sm-3 control-label ">密码：</label>
                    <div class="col-sm-6"><input type="text" class="form-control" id="pass" name="pass" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="authenticationType" class="col-sm-3 control-label">身份验证类型：</label>
                    <div class="col-sm-6">
                        <input type="radio"  value="" name="authenticationType" />无
                        <input type="radio"  value="PAP" name="authenticationType" />PAP
                        <input type="radio"  value="CHAP" name="authenticationType" />CHAP
                        <input type="radio"  value="PAP:CHAP" name="authenticationType"/>PAP或CHAP
                    </div>
                </div>
                <div class="form-group"><label for="apnType" class="col-sm-3 control-label">APN类型：</label>
                    <div class="col-sm-6">
                        <input type="radio" value="Default"  name="apnType" >Default
                        <input type="radio" value="Supl"  name="apnType" >Supl
                        <input type="radio" value="Dun"  name="apnType" >Dun
                        <input type="radio" value="Hipri"  name="apnType" >Hipri
                    </div>
                </div>
                <div class="form-group"><label for="apnProtocol" class="col-sm-3 control-label">APN协议：</label>
                    <div class="col-sm-6">
                        <input type="radio" value="IPV4" name="apnProtocol">IPV4
                        <input type="radio" value="IPV6" name="apnProtocol">IPV6
                        <input type="radio" value="IPV4/IPV6" name="apnProtocol">IPV4/IPV6
                    </div>
                </div>
                <div class="form-group"><label for="apnRoamingProtocol" class="col-sm-3 control-label">APN漫游协议：</label>
                    <div class="col-sm-6">
                        <input type="radio" value="IPV4" name="apnRoamingProtocol">IPV4
                        <input type="radio" value="IPV6" name="apnRoamingProtocol">IPV6
                        <input type="radio" value="IPV4/IPV6" name="apnRoamingProtocol">IPV4/IPV6
                    </div>
                </div>
                <div class="form-group"><label for="mvnoType" class="col-sm-3 control-label">MVNO类型：</label>
                    <div class="col-sm-6">
                        <input type="radio" value="" name="mvnoType">无
                        <input type="radio" value="SPN" name="mvnoType">SPN
                        <input type="radio" value="IMSI" name="mvnoType">IMSI
                        <input type="radio" value="GID" name="mvnoType">GID
                        <input type="radio" value="ICCID" name="mvnoType">ICCID
                    </div>
                </div>
                <div class="form-group"><label for="mvnpValue" class="col-sm-3 control-label">MVNO值：</label>
                    <div class="col-sm-6"><input type="text" class="form-control " id="mvnpValue" name="mvnpValue" maxlength="64">
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn3">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule4">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required"  value=""  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="enableBindIP" class="col-sm-3 control-label required">启用绑定IP：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="enableBindIP" name="enableBindIP" />
                    </div>
                </div>
                <div class="form-group"><label for="bindIP" class="col-sm-3 control-label">绑定IP：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control" id="bindIP" name="bindIP" maxlength="64">
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn4">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule5">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required"  value=""  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="enableNetworkMonitor" class="col-sm-3 control-label required">启用网络监控：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="enableNetworkMonitor" name="enableNetworkMonitor" />
                    </div>
                </div>
                <div class="form-group"><label for="ipOrDomainName" class="col-sm-3 control-label required">IP或域名：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="ipOrDomainName" name="ipOrDomainName" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="ipOrDomainName2" class="col-sm-3 control-label">第二IP或域名：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control" id="ipOrDomainName2" name="ipOrDomainName2" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="probeInterval" class="col-sm-3 control-label required">探测间隔(1-300s)：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="probeInterval" name="probeInterval" oninput="this.value = Frame.Util.inputNum(this.value,1,300)" />

                    </div>
                </div>
                <div class="form-group"><label for="messageNum" class="col-sm-3 control-label required">连续探测报文数(1-20)：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="messageNum" name="messageNum" oninput="this.value = Frame.Util.inputNum(this.value,1,20)" />
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn5">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule6">
            <div id="portMappingDiv"></div>
        </div>
        <div class="tab-pane fade" id="rule7">
            <form class="form-horizontal" novalidate>
                <div class="form-group hide"><label class="col-sm-3 control-label required">sn：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required"  value=""  name="sn" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="lanIP" class="col-sm-3 control-label">IP：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="lanIP" name="lanIP" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="netmask" class="col-sm-3 control-label">子网掩码：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="netmask" name="netmask" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="lanIPFailMsg" class="col-sm-3 control-label">失败原因：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control" id="lanIPFailMsg" name="lanIPFailMsg" readonly maxlength="64">
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn7">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule8">
            <div id="natDiv"></div>
        </div>
    </div>
</div>

<div id="columnForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group">
            <label for="column" class="col-sm-3 control-label">{column}</label>
            <div class="col-sm-6" id="columnDiv">
            </div>
        </div>
    </form>
</div>

<!--添加NAT-->
<div id="addNatForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group">
            <label for="enable" class="col-sm-3 control-label required">是否启用：</label>
            <div class="col-sm-6">
                <input type="checkbox" id="enable" name="enable" value="enable" />
            </div>
        </div>
        <div class="form-group">
            <label for="displayName" class="col-sm-3 control-label required">名称：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="displayName" name="displayName" maxlength="32">
            </div>
        </div>
        <div class="form-group">
            <label for="target" class="col-sm-3 control-label required">动作：</label>
            <div class="col-sm-6">
                <select id="target" name="target" class="form-control form-control-placeholder required">
                    <option value="SNAT" style="color: black;">SNAT</option>
                    <option value="MASQUERADE" style="color: black;">MASQUERADE</option>
                    <option value="ACCEPT" style="color: black;">ACCEPT</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="protocol" class="col-sm-3 control-label required">协议：</label>
            <div class="col-sm-6">
                <select id="protocol" name="protocol" class="form-control form-control-placeholder required">
                    <option value="all" style="color: black;">ALL</option>
                    <option value="tcp,udp" style="color: black;">TCP/UDP</option>
                    <option value="tcp" style="color: black;">TCP</option>
                    <option value="udp" style="color: black;">UDP</option>
                    <option value="icmp" style="color: black;">ICMP</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="srcIP" class="col-sm-3 control-label">源地址：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="srcIP" name="srcIP" maxlength="64">
            </div>
        </div>
        <!-- protocol 不为 all 时显示 源起始、终止端口号 -->
        <div class="form-group" id="srcPortSItem">
            <label for="srcPortS" class="col-sm-3 control-label">源起始端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="srcPortS" name="srcPortS" maxlength="64">
            </div>
        </div>
        <div class="form-group" id="srcPortEItem">
            <label for="srcPortE" class="col-sm-3 control-label">源终止端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="srcPortE" name="srcPortE" maxlength="64">
            </div>
        </div>
        <div class="form-group">
            <label for="destIP" class="col-sm-3 control-label">目的地址：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="destIP" name="destIP" maxlength="64">
            </div>
        </div>
        <!-- protocol 不为 all 时显示 起始、终止端口号 -->
        <div class="form-group" id="destPortSItem">
            <label for="destPortS" class="col-sm-3 control-label">目的起始端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="destPortS" name="destPortS" maxlength="64">
            </div>
        </div>
        <div class="form-group" id="destPortEItem">
            <label for="destPortE" class="col-sm-3 control-label">目的终止端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="destPortE" name="destPortE" maxlength="64">
            </div>
        </div>
        <!--target为S时显示-->
        <div class="form-group" id="snatIPItem">
            <label for="snatIP" class="col-sm-3 control-label required">重写地址：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="snatIP" name="snatIP" maxlength="64">
            </div>
        </div>
        <!-- protocol 不为 all且target为S时显示 -->
        <div class="form-group" id="snatPortSItem">
            <label for="snatPortS" class="col-sm-3 control-label">重写起始端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="snatPortS" name="snatPortS" maxlength="64">
            </div>
        </div>
        <div class="form-group" id="snatPortEItem">
            <label for="snatPortE" class="col-sm-3 control-label">重写终止端口号：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="snatPortE" name="snatPortE" maxlength="64">
            </div>
        </div>
        <!--target为MASQUERADE时显示-->
        <div class="form-group" id="netInterfaceItem">
            <label for="netInterface" class="col-sm-3 control-label required">网络接口：</label>
            <div class="col-sm-6">
                <select id="netInterface" name="netInterface" class="form-control form-control-placeholder">
                    <option value="WAN" style="color: black;">WAN</option>
                    <option value="5G" style="color: black;">5G</option>
                    <option value="LAN" style="color: black;">LAN</option>
                </select>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/common/flow.js?v=a2c5845ac9"></script>
<script type="text/javascript" src="view/common/device.js?v=d459cce937"></script>
<script type="text/javascript" src="view/device/device.js?v=bfa4759232"></script>
