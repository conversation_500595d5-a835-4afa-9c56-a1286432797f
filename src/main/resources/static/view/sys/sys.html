
<div id="sys">
    <ul class="nav nav-tabs" id="myTab">
        <li class="active"><a data-href="#bulletin1">字典</a></li>
        <li id="companyDictTab" style="display: none;"><a data-href="#rule3">公司字典</a></li>
        <li ><a data-href="#rule1">报警参数</a></li>
        <li ><a data-href="#rule2">公司参数</a></li>

    </ul>
    <div id="mytab-content2" class="tab-content">
        <div class="tab-pane fade active" id="bulletin1">
            <div id="dictionary">
            </div>
            <div id="dictForm">
                <form class="form-horizontal" novalidate>
                    <div class="form-group"><label for="dicKey" class="col-sm-3 control-label required">{dicKey}</label>
                        <div class="col-sm-6"><input type="text" class="form-control required" id="dicKey" name="dicKey" maxlength="64">
                        </div>
                    </div>
                    <div class="form-group"><label for="dicContent" class="col-sm-3 control-label required">{dicContent}</label>
                        <div class="col-sm-6"><input type="text" class="form-control required" id="dicContent" name="dicContent" maxlength="64">
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade" id="rule3">
            <div id="companyDict">
            </div>
        </div>
        <div class="tab-pane fade" id="rule1">
            <div id="param">
            </div>
            <div id="paramForm">
                <form class="form-horizontal" novalidate>
                    <div class="form-group"><label for="parameterKey" class="col-sm-3 control-label required">{parameterKey}</label>
                        <div class="col-sm-6"><select class="form-control" id="parameterKey" name="parameterKey" ></select></div>
                    </div>
                    <div class="form-group"><label for="parameterContent" class="col-sm-3 control-label required">{parameterContent}</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control required"  id="parameterContent" name="parameterContent" maxlength="18" >
                        </div>
                    </div>
                    <div class="form-group"><label for="parameterCode" class="col-sm-3 control-label required">{parameterCode}</label>
                        <div class="col-sm-6">
                            <select class="form-control required" id="parameterCode" name="parameterCode" ></select>
                        </div>
                    </div>
                    <div class="form-group"><label for="parameterValue" class="col-sm-3 control-label required">{parameterValue}</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control required" id="parameterValue" name="parameterValue"  value="0"
                                   oninput="if(value.length>5)value=value.slice(0,8)"
                            >
                        </div>
                    </div>
                    <div class="form-group"><label for="parameterValueMax" class="col-sm-3 control-label">{parameterValueMax}</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" id="parameterValueMax" name="parameterValueMax" value=""
                                   oninput="if(value.length>5)value=value.slice(0,8)">
                        </div>
                    </div>
                    <div class="form-group"><label for="parameterOperator" class="col-sm-3 control-label required">{parameterOperator}</label>
                        <div class="col-sm-6"><select class="form-control" id="parameterOperator" name="parameterOperator"  ></select></div>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade" id="rule2">
            <div id="flowFormDiv" class="form-horizontal">

                <div class="form-group">
                    <label for="flowCompany" class="col-sm-2 control-label">流量计算起始时间</label>
                    <div class="col-sm-2">
                        <select id="flowCompanyMonth" name="flowCompanyMonth" class="form-control form-control-placeholder">
                            <option value="0">未选择</option>
                            <option value="-1">上月</option>
                            <option value="1">本月</option>
                        </select>
                    </div>
                    <div class="col-sm-2">
                        <select id="flowCompany" name="flowCompany" class="form-control form-control-placeholder">
                            <option value="0">未选择</option>
                            <option value="1">1号</option>
                            <option value="2">2号</option>
                            <option value="3">3号</option>
                            <option value="4">4号</option>
                            <option value="5">5号</option>
                            <option value="6">6号</option>
                            <option value="7">7号</option>
                            <option value="8">8号</option>
                            <option value="9">9号</option>
                            <option value="10">10号</option>
                            <option value="11">11号</option>
                            <option value="12">12号</option>
                            <option value="13">13号</option>
                            <option value="14">14号</option>
                            <option value="15">15号</option>
                            <option value="16">16号</option>
                            <option value="17">17号</option>
                            <option value="18">18号</option>
                            <option value="19">19号</option>
                            <option value="20">20号</option>
                            <option value="21">21号</option>
                            <option value="22">22号</option>
                            <option value="23">23号</option>
                            <option value="24">24号</option>
                            <option value="25">25号</option>
                            <option value="26">26号</option>
                            <option value="27">27号</option>
                            <option value="28">28号</option>
                        </select>
                    </div>
                    <div class="col-sm-2">
                        <button id="flowBtn" class="btn btn-info">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript" src="view/sys/sys.js?v=060e8860b2"></script>
<!--
<script type="text/javascript" src="view/common/device.js?v=d459cce937"></script>-->
