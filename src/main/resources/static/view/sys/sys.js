!function () {
    var dictTable, dictForm, dictDiv = $("#dictionary"), dict = Frame.INFO.sysedit.dict
        , paramTable, paramForm, paramDiv = $("#param"), params = Frame.INFO.sysedit.params;
    let companyDictTable,companyDictDiv = $("#companyDict");

    $('#myTab a').click(function () {
        $("#myTab").find("li[class ='active']").removeClass("active");
        $(this).parent().addClass("active");
        let href = $(this).attr("data-href");
        $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
        $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');
    })

    function query(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", dictDiv).val()},
            a = $(".filter input:first-child", dictDiv).val();
        a && (t.dicKey = a);
        e && $.extend(t, e);
        dictTable.refresh(t);
    }

    function addDict(e) {
        Frame.Msg.prompt({
            title: "",
            content: dictForm,
            onConfirm: function a() {
                let errMsg = null;
                $.ajax({
                    url: "dictionary/dic?key=" + $("#dicKey", dictForm).val(),
                    type: "get",
                    async: false,
                    success: function (data) {
                        data.length > 0 &&
                        data.forEach(item => {
                            item.dicContent == $("#dicContent", dictForm).val() && (errMsg = Frame.INFO.msg.error);
                        })
                    }
                })
                errMsg != null && Frame.Msg.error(errMsg);
                errMsg == null && Frame.formRequest({url: "/dictionary/dic/add", el: dictForm, onSuccess: query});

            }
        });
    }

    function deleteDict(e) {
        Frame.Msg.confirm({
            content: Frame.INFO.msg.confirmDelete, onConfirm: function a() {
                Frame.setRequest({url: "/dictionary/dic/delete", data: {id: e.id}, onSuccess: query})
            }
        })
    }

    function initDict() {
        var e = {
            url: "dictionary/dicList",
            pagination: !0,
            columns: [
                {name: "id", title: "id",},
                {name: "dicKey", title: dict.key},
                {name: "dicContent", title: dict.value},
            ],
            operations: [
                {
                    name: "remove", action: deleteDict, role: "0",
                    fShow: function (e) {
                        return 0 != e.type || 0 == Frame.Default.role
                    }
                }],
            toolbar: {
                search: [
                    {name: "input", placeholder: Frame.INFO.search.dicKey, action: query}
                ],
                operations: [
                    {name: "refresh", action: query},
                    {name: "add", action: addDict, role: "0"}
                ]
            },
            onSelectPage: function (e) {
                query({size: e})
            },
            onChangePageSize: function (e) {
                query({page: e})
            }
        };
        dictTable = dictDiv.datagrid(e)

        !function replace() {
            var e = $("#dictForm").html(), a = {
                "{dicKey}": dict.key,
                "{dicContent}": dict.value
            };
            e = Frame.replaceTitle(e, a);
            dictForm = $(e);
            $("#dictForm").empty().remove(), dictForm.validateForm();
        }();
    }

    function queryParam(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", dictDiv).val()},
            a = $(".filter input:first-child", dictDiv).val();
        a && (t.dictKey = a);
        var parameterKey = $(".filter select", paramDiv).val();
        "all" != parameterKey && (t.parameterKey = parameterKey);
        e && $.extend(t, e);
        paramTable.refresh(t);
    }

    function addParam(e) {
        Frame.Msg.prompt({
            title: "",
            content: paramForm,
            onConfirm: function a() {
                "" == $("#parameterValueMax", paramForm).val() && $("#parameterValueMax", paramForm).val(0);
                "" == $("#parameterValue", paramForm).val() && $("#parameterValue", paramForm).val(0);
                Frame.formRequest({url: "/parm/add", el: paramForm, onSuccess: queryParam});
            }
        });
    }

    function deleteParam(e, elemet) {
        Frame.Msg.confirm({
            content: Frame.INFO.msg.confirmDelete, onConfirm: function a() {
                Frame.setRequest({
                    url: "/parm/delete", data: {id: e.id}, onSuccess: function () {
                        $(elemet).parent().parent().remove();
                    }
                })
            }
        })
    }

    function editParam(e) {
        Frame.Msg.prompt({
            title: "",
            content: paramForm,
            data: e,
            onConfirm: function t() {
                var form = Frame.getFormData(paramForm);
                form.id = e.id;
                "" == form.parameterValueMax && (form.parameterValueMax = 0);
                Frame.setRequest({
                    url: "parm/updateByPrimaryKey",
                    data: form,
                    onSuccess: queryParam
                })
            }
        })
    }

    function parameterOperatorFromat(cellValue) {
        if (cellValue == undefined || cellValue == null || cellValue == "") {
            return;
        }
        for (let i = 0; i < params.operatorEnum.length; i++) {
            let obj = params.operatorEnum[i];
            if (cellValue == obj.value) {
                return obj.content;
            }
        }
        return cellValue;
    }

    function initParam() {
        let e = {
            url: "parm/findGroubByModel",
            pagination: !0,
            columns: [
                {name: "id", title: "id"},
                {name: "company", title: params.company},
                {name: "parameterKey", title: params.parameterKey},
            ],

            toolbar: {
                search: [
                    {name: "select", content: Frame.createModel(!0), action: queryParam}
                ],
                operations: [
                    {name: "refresh", action: queryParam},
                    {name: "add", action: addParam}
                ]
            },
            onSelectPage: function (e) {
                queryParam({size: e})
            },
            onChangePageSize: function (e) {
                queryParam({page: e})
            },
            subGrid: {
                options: {
                    url: "parm/findListById",
                    pagination: !1,
                    columns: [
                        {name: "company", title: params.company},
                        {name: "parameterKey", title: params.parameterKey},
                        {name: "parameterContent", title: params.parameterContent},
                        {name: "parameterCode", title: params.parameterCode},
                        {name: "parameterValue", title: params.parameterValue},
                        {name: "parameterValueMax", title: params.parameterValueMax},
                        {name: "parameterOperator", title: params.parameterOperator, format: parameterOperatorFromat},
                        {name: "insertTime", title: params.insertTime, format: Frame.Time.formatTime},
                    ],
                    operations: [
                        {
                            name: "remove", action: deleteParam,
                            fShow: function (e) {
                                return 0 != e.type || 0 == Frame.Default.role
                            }
                        },
                        {
                            name: "edit", action: editParam,
                            fShow: function (e) {
                                return 0 != e.type || 0 == Frame.Default.role
                            }
                        }
                    ],
                }
            }
        };
        paramTable = paramDiv.datagrid(e)

        !function replace() {
            var e = $("#paramForm").html(), a = {
                "{parameterKey}": params.parameterKey,
                "{parameterContent}": params.parameterContent,
                "{parameterCode}": params.parameterCode,
                "{parameterValue}": params.parameterValue,
                "{parameterValueMax}": params.parameterValueMax,
                "{parameterOperator}": params.parameterOperator,
            };
            e = Frame.replaceTitle(e, a);
            paramForm = $(e);
            paramForm.find("#parameterKey").append(Frame.createModel());
            paramForm.find("#parameterCode").append(getParam());
            let operatorStr = "";
            params.operatorEnum.forEach(item => {
                operatorStr += `<option value="${item.value}">${item.content}</option>`
            })
            paramForm.find("#parameterOperator").append(operatorStr);

            $("#parameterOperator", paramForm).change(function () {
                var form = Frame.getFormData(paramForm);
                let operatorValue = 6;// 实际值不在范围内，异常
                let b = paramForm.find("label[for='parameterValueMax']").hasClass("required");
                if (b == false) {
                    if (form.parameterOperator == operatorValue) {
                        paramForm.find("label[for='parameterValueMax']").addClass("required");
                    }
                } else {
                    if (form.parameterOperator != operatorValue) {
                        paramForm.find("label[for='parameterValueMax']").removeClass("required");
                    }
                }
            });
            $("#parameterCode", paramForm).change(function () {
                var form = Frame.getFormData(paramForm);
                let operatorValue = "ip";// 如果时Ip报警
                let b = paramForm.find("label[for='parameterValue']").hasClass("required");
                if (b == false) {
                    if (form.parameterCode.toLowerCase() != operatorValue) {
                        paramForm.find("label[for='parameterValue']").addClass("required");
                        paramForm.find("#parameterValue").addClass("required");
                    }
                } else {
                    if (form.parameterCode.toLowerCase() == operatorValue) {
                        paramForm.find("label[for='parameterValue']").removeClass("required");
                        paramForm.find("#parameterValue").removeClass("required");
                    }
                }
            });
            $("#paramForm").empty().remove();
            paramForm.validateForm();
        }();
    }


    function getParam() {
        let str = "";
        let key = "ALARM_CODE";
        $.ajax({
            url: `dictionary/dic?key=${key}`,
            type: "get",
            async: false,
            success: function (data) {
                for (let i = 0; i < data.length; i++) {
                    let config = data[i];
                    str += `<option value="${config.dicContent}">${config.dicContent}</option>`;
                }
            }
        })
        return str;
    }

    function initCompanyTable() {
        if (Frame.Default.role == 0) {
            // 如果是超级管理员，才能看到公司字典
            $("#companyDictTab").show();
        } else {
            return ;
        }
        function queryComDict(param) {
            var t = {page: 1, size: $(".toolbar select.pagenum", companyDictDiv).val()},
                a = $(".filter input:first-child", companyDictDiv).val();
            a && (t.company = a);
            param && $.extend(t, param);
            companyDictTable.refresh(t);
        }

        var e = {
            url: "companyDict/dictPage",
            pagination: !0,
            columns: [
                {name: "id", title: "id",},
                {name: "company", title: params.company},
                {name: "dictType", title: dict.type},
                {name: "dictLabel", title: dict.key},
                {name: "dictValue", title: dict.value},
            ],
            toolbar: {
                search: [
                    {name: "input", placeholder: Frame.INFO.search.company, action: queryComDict}
                ],
                operations: [
                    {name: "refresh", action: queryComDict},
                ]
            },
            onSelectPage: function (e) {
                queryComDict({size: e})
            },
            onChangePageSize: function (e) {
                queryComDict({page: e})
            }
        };
        companyDictTable = companyDictDiv.datagrid(e);
    }

    function initCompany() {
        let data = {company:Frame.Default.user.company};
        Frame.getRequest({ url: "companyDict/dictList",data:data,onSuccess:function (param){
            let flow = null;
            for (let i = 0; i < param.length; i++) {
                let temp = param[i];
                if (temp.dictType == Frame.companyDict.companyFlow.dictType
                    && temp.dictLabel == Frame.companyDict.companyFlow.startTime.dictLabel) {
                    flow = temp;
                }
            }
            initFlow(flow);
            }},)
        function initFlow(flow){
            if (flow != null) {
                let val = flow.dictValue;
                $("#flowCompanyMonth").val("-1");
                if (parseInt(val) > 0) {
                    $("#flowCompanyMonth").val("1");
                }
                $("#flowCompany").val(Math.abs(val));
            }
        }
    }

    $("#flowBtn").click(function (){
        let month = $("#flowCompanyMonth").val();
        let val = $("#flowCompany").val();
        if (month * val == 0) {
            return Frame.Msg.error("不能为空");
        }
        let url = "companyDict/save";
        let data = {company:Frame.Default.user.company,
            dictType:Frame.companyDict.companyFlow.dictType,
            dictLabel:Frame.companyDict.companyFlow.startTime.dictLabel,dictValue:month * val,};
        Frame.getRequest({ url: url,data:data, success: function (){
                Frame.Msg.info("成功");
            }})
    })


    Frame.destory = function () {
        dictTable && dictTable.destory(), dictDiv = dict = dictForm = null;
        paramTable && paramTable.destory(), paramDiv = params = paramForm = null;
    }, $(function (e) {
        initDict();
        initParam();
        initCompany();
        initCompanyTable();
    })


}();
