<div id="firmware"></div>
<div class="hide" id="addFile">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="name" class="col-sm-3 control-label required">{name}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="name" name="name" maxlength="64">
            </div>
        </div>
        <div class="form-group"><label for="model" class="col-sm-3 control-label required">{model}</label>
            <div class="col-sm-6"><select class="form-control" id="model" name="model"></select></div>
        </div>
        <div class="form-group"><label for="version" class="col-sm-3 control-label required">{version}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="version" name="version"
                                         maxlength="64"></div>
        </div>
        <div class="form-group"><label for="file" class="col-sm-3 control-label required">{file}</label>
            <div class="col-sm-6"><input type="file" name="file" id="file" class="hide" accept=".img,.bin,.zip">
                <button type="button" class="btn btn-info" id="uploadFile">{uploadFile}</button>
                <i></i></div>
        </div>
        <div class="form-group"><label for="describ" class="col-sm-3 control-label">{describ}</label>
            <div class="col-sm-6"><input type="text" class="form-control" id="describ" name="describ" maxlength="64">
            </div>
        </div>
        <!--<div class="form-group"><label for="describ" class="col-sm-3 control-label">{configId}</label>
            <div class="col-sm-6"><select class="form-control" id="configId" name="configId"></select></div>
        </div>-->
    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/firmware/firmware.js?v=060e8860b2"></script>
<script type="text/javascript" src="view/common/device.js?v=d459cce937"></script>