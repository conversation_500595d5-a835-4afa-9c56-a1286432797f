!function () {
    var t, n, confirmPass, r = $("#firmware"), l = Frame.INFO.device;

    function formatterStatus(e) {
        if (0 == e) {
            return '<i class="fa fa-spinner"></i><span>' + l.executing + "</span>";
        } else if (1 == e) {
            return '<i class="fa fa-times-circle"></i><span>' + l.download_fail + "</span>";
        } else if (2 == e) {
            return '<i class="fa fa-times-circle"></i><span>' + l.check_fail + "</span>";
        } else if (3 == e) {
            return '<i class="fa fa-check-circle"></i><span>' + l.success + "</span>";
        } else if (4 == e) {
            return  '<i class="fa fa-check-circle"></i><span>' + l.timeout_upgrade + "</span>";
        } else {
            return '<i class="fa fa-times-circle"></i><span>' + l.download_fail + "</span>";
        }
    }

    function m(e) {
        //删除前检验
        if (delValidate(e) == false) {
            return;
        }

        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "firmware/delete", data: {firmwareId: e.id, passwd: md5(pass)}, onSuccess: f})
        }

        Frame.Msg.confirm({
            content: l.removeFirmware,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function delValidate(e) {
        //删除前检验
        let flag = 1;
        Frame.setRequest({
            url: "firmware/upgradeList", data: {id: e.id}, async: false, loading: false, success: function (data) {
                if (200 == data.code) {
                    let arr = data.data;
                    for (let j = 0; j < arr.length; j++) {
                        if (0 == arr[j].status) {
                            flag = 0;
                            return;
                        }
                    }
                }
            }
        });
        if (flag == 0) {
            Frame.Msg.error(l.cannotDelete);
            return false;
        }
        return true
    }

    function s(e) {
        $("#uploadFile", n).on("click", function () {
            $("input#file", n).click()
        }), $("input#file", n).on("change", function () {
            var e = $(this).val().split("\\"), a = e[e.length - 1];
            $(this).siblings("i").html(a)
        }), $("#file", n).siblings("i").empty(), Frame.Msg.prompt({
            title: l.addFirmware,
            content: n,
            onConfirm: function a() {
                Frame.formRequest({url: "firmware/upload", el: n, onSuccess: f})
            }
        });

    }

    function c(a) {
        var e = $("<div></div>");
        let param = {model: a.model,upgrade: 0, online: 1};
        Table.init(e, param);
        Frame.Msg.prompt({
            title: l.uploadFirm,
            validate: !1,
            content: e,
            width: 700,
            maxHeight: 500,
            onConfirm: function i() {
                var e = Table.getDeviceID();
                Frame.setRequest({url: "firmware/upgrade", data: {firmwareId: a.id, deviceIds: e}})
            }
        })
    }

    function f(e) {
        var a = {page: 1, size: $(".toolbar select.pagenum", r).val()}, i = $(".filter input", r).val();
        i && (a.name = i);
        var n = $(".filter select", r).val();
        "all" != n && (a.model = n), e && $.extend(a, e), t.refresh(a)
    }

    function delSubGrid(e) {
        if (delSubGridValid(e) == false) {
            return;
        }

        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "firmware/deleteUpgrade", data: {firmwareId: e.id, passwd: md5(pass)}, onSuccess: f})
        }

        Frame.Msg.confirm({
            content: l.delFirmwareUpgradeWarn,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function delSubGridValid(e) {
        // 删除前检验
        let msg = null;
        Frame.setRequest({
            url: "firmware/upgradeList", data: {id: e.id}, async: false, loading: false, success: function (data) {
                if (200 == data.code) {
                    let arr = data.data;
                    if (arr == undefined || arr == null || arr.length < 1) {
                        msg = l.noRecord;
                    }
                } else {
                    msg = l.fail;
                }
            }
        });
        if (msg != null) {
            Frame.Msg.error(msg);
            return false;
        }
        return true
    }

    function forceComplete(e) {
        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "firmware/completeUpgrade", data: {id: e.id, passwd: md5(pass)}, onSuccess: f})
        }

        Frame.Msg.confirm({
            content: l.forceCompleteConfirm,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }


    function a() {
        var option = {
            url: "firmware/list",
            pagination: !0,
            columns: [
                {name: "name", title: l.firmwareName},
                {name: "model", title: l.type},
                {name: "company", title: l.company},
                {name: "version", title: l.version},
                {name: "gmtCreate", title: l.gmtCreate, format: Frame.Time.formatTime},
                {name: "describ", title: l.describ}
            ],
            operations: [{name: "upload", title: l.uploadFirm, action: c, role: "0,1"}, {
                name: "remove",
                action: m,
                fShow: function (e) {
                    return 0 != e.type || 0 == Frame.Default.role
                }
            },
                {name: "delSubGrid", title: l.delSubGrid, action: delSubGrid, role: "0,1"},
            ],
            toolbar: {
                search: [{
                    name: "input",
                    placeholder: Frame.INFO.search.firmwareName,
                    action: f
                }, {name: "select", content: Frame.createModel(!0), action: f}],
                operations: [{name: "add", action: s}]
            },
            onSelectPage: function (e) {
                f({size: e})
            },
            onChangePageSize: function (e) {
                f({page: e})
            },
            subGrid: {
                gridArray:[],
                options: {
                    url: "firmware/upgradePage",
                    pagination: !0,
                    columns: [
                        {name: "id", title: "id"},
                        {name: "name", title: l.devname},
                        {name: "sn", title: l.sn},
                        {name: "version", title: l.version},
                        {name: "gmtCreate", title: l.gmtCreate,format: Frame.Time.formatTime},
                        {
                            name: "status",
                            title: l.status,
                            format: formatterStatus,
                            classFormat: {0: "executing", 1: "fail", 2: "fail", 3: "ok"}
                        }],
                    operations: [
                        {
                            name: "restart2",
                            icon: {icon: "fa fa-repeat", title: l.forceComplete},
                            action: forceComplete
                        },
                    ],
                    details: {inner: !0},
                    onSelectPage: function (e) {
                        this.query({size: e});
                    },
                    onChangePageSize: function (e) {
                        this.query({page: e})
                    },
                    query: function f(e) {
                        let id = this.data.id;
                        let gridInfo = null;
                        for (let i = 0; i < t.options.subGrid.gridArray.length; i++) {
                            let gridTemp = t.options.subGrid.gridArray[i];
                            if (id == gridTemp.id) {
                                gridInfo = gridTemp;
                                break;
                            }
                        }
                        var a = {page: 1, size: $(".toolbar select.pagenum", gridInfo.gridDiv).val()};
                        e && $.extend(a, e);
                        $.extend(a, this.data);
                        gridInfo.gridObj.refresh(a)
                    }
                },

            }
        };
        t = r.datagrid(option)

        !function i() {
            var e = $("#addFile").html(), a = {
                "{name}": l.firmwareName,
                "{model}": l.type,
                "{version}": l.version,
                "{file}": l.importFile,
                "{describ}": l.describ,
                "{uploadFile}": l.uploadFile,
                // "{configId}": l.configId
            };
            e = Frame.replaceTitle(e, a),
                (n = $(e)).find("#model").append(Frame.createModel());
            $("#addFile").empty().remove(), n.validateForm();

            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}": l.passwd}), confirmPass = $(t), $("#confirm-pass").empty().remove();
        }();
    }

    Frame.destory = function () {
        t && t.destory(), l = r = confirmPass = n = null, Table = null
    };
    $(function (e) {
        a()
    })
}();
