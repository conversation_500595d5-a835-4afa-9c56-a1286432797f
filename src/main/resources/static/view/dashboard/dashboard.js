! function () {
    var a, t, c = $("#dashboard"),
        i = Frame.INFO.dashboard;

    $.ajax({
        url:'device/online',
        type:"get",
        dataType:"json",
        async:false,
        success:function (date){
            var arr = [];
            arr.push(date);
            var servicedata = [];
            for (var i = 0; i < 2; i++) {
                var obj1 = new Object();
                var obj2 = new Object();
                obj1.name = "在线数";
                obj1.value = arr[0].online;
                obj2.name = "下线数";
                obj2.value = arr[0].offline;
                servicedata = [obj1, obj2];
                ppt=servicedata
            }
        }
    })


    var myChart = echarts.init(document.getElementById('main'));

    var option = {
        color:["#4682B4","#ADD8E6","#DAA520"],
        title: {
            text: '设备在线看板',
            subtext: '在线数/下线数',
            x: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/> {b} :{c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            itemWidth: 30,   // 设置图例图形的宽
            itemHeight: 20,
            itemGap: 20,
            data: ppt,
        },
        series: [
            {
                name: '访问来源',
                type: 'pie',
                radius: '60%',
                data:ppt,

                // [
                //     {value: 2, name: 'online'},
                //     {value: 4, name: 'offline'},
                //
                // ],
                roseType: 'angle',
                itemStyle: {
                    normal: {
                        shadowBlur: 150,
                        shadowColor: 'rgba(160,144,144,0.5)'
                    }
                },
                label: {
                    normal: {
                        textStyle: {
                            color: 'rgba(33,30,30,0.95)'
                        }
                    }
                },
                labelLine: {
                    normal: {
                        lineStyle: {
                            color: 'rgb(22,24,24)'
                        },
                        smooth: 0.2,
                        length: 10,
                        length2: 20
                    }
                },
            }
        ]
    }
    myChart.setOption(option)

    Frame.destory = function () {
        a && a.destory(), i = c = t = null;
    };
}(echarts);




