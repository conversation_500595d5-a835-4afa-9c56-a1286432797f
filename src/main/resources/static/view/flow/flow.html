<div id="flow"></div>
<div class="hide" id="updateFlow">
    <form class="form-horizontal" novalidate>

        <div class="form-group"><label for="flowInput" class="col-sm-3 control-label required">{flow}</label>
            <div class="col-sm-6"><input type="number" class="form-control required" id="flowInput" name="flow" maxlength="64" oninput="value=value.replace(/[^\d.]/g,'')"></div>
        </div>
    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/flow/flow.js?v=060e8860b2"></script>
