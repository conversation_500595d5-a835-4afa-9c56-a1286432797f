!function () {
    var tableJq, updateDiv, confirmPass, tableDiv = $("#flow"),
        textConst = Frame.INFO.text,divConst = Frame.INFO.device;


   
    function update(e) {
        Frame.Msg.confirm({
            title: textConst.monthFlow,
            content: updateDiv,
            onConfirm: function () {
                let formData = Frame.getFormData(updateDiv);
                let flow = formData.flowInput * 1024 * 1024;
                Frame.Msg.prompt({
                    title: textConst.passwd,
                    content: confirmPass,
                    onConfirm: function (){
                        var pass = $("#password", confirmPass).val();
                        let data = {id: e.id,passwd:md5(pass),flow:flow};
                        Frame.setRequest({url: "device/flow/month/update", data: data, onSuccess: query})
                    }
                })
            }
        })
    }

    function query(e) {
        var a = {page: 1, size: $(".toolbar select.pagenum", tableDiv).val()};
        let company = $(".filter input[name='company']", tableDiv).val();
        let sn = $(".filter input[name='sn']", tableDiv).val();
        let alias = $(".filter input[name='alias']", tableDiv).val();
        let trueIp = $(".filter input[name='trueIp']", tableDiv).val();
        let dateArr = $(".filter input[name='date']", tableDiv).val().split(" ~ ");
        2 == dateArr.length && (a.dateStart = dateArr[0], a.dateEnd = dateArr[1]);
        company && (a.company = company);
        sn && (a.sn = sn);
        alias && (a.alias = alias);
        trueIp && (a.trueIp = trueIp);
        e && $.extend(a, e);
        tableJq.refresh(a)
    }


    function init() {
        var option = {
            url: "device/flow/month/page",
            pagination: !0,
            columns: [
                {name: "company", title: textConst.company,role:"0"},
                {name: "sn", title: textConst.sn},
                {name: "alias", title: divConst.alias},
                {name: "trueIp", title: divConst.ip},
                {name: "upFlow", title: textConst.monthFlow,format:flowFormat,orderBy:true},
                {name: "date", title: textConst.date},
                {name: "gmtModify", title: textConst.updatDate, format: Frame.Time.formatTime},
            ],
            operations: [
                {name: "edit",action: update},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"company",placeholder: Frame.INFO.search.company,action: query},
                    {name: "input",name2:"sn",placeholder: Frame.INFO.search.sn,action: query}
                    ,{name: "input",name2:"alias",placeholder: Frame.INFO.search.alias,action: query}
                    ,{name: "input",name2:"trueIp",placeholder: divConst.ip,action: query}
                    ,{
                        name: "input",name2:"date",
                        placeholder: Frame.Time.getDay(-1, "Y-M") + " ~ " + Frame.Time.getDay(0, "Y-M"),
                        action: query
                    }
                    ],
            },
            onSelectPage: function (e) {
                query({size: e})
            },
            onChangePageSize: function (e) {
                query({page: e})
            },
        };
        tableJq = tableDiv.datagrid(option);

        $("input[name='date']",tableJq).attr("readonly", true);
        $("input[name='date']",tableJq).css({"background-color": "#fff"});

        laydate.render({
            elem: $("input[name='date']",tableDiv)[0],
            range: "~",
            type:"month",
            btns: ["clear", "confirm"],
            min: "1970-01-01",
            theme: "molv",
            lang: Frame.Default.lang,
            done: function (e) {
                if ("" == e) {
                    query()
                } else {
                    let  t = e.split(" ~ ");
                    query({dateStart: t[0], dateEnd: t[1]})
                }
            }
        })

        !function replaceText() {
            var e = $("#updateFlow").html();
            let a = {
                "{flow}": textConst.monthFlow,
            };
            e = Frame.replaceTitle(e, a);
            updateDiv = $(e);
            $("#updateFlow").empty().remove();

            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}": textConst.passwd});
            confirmPass = $(t);
            $("#confirm-pass").empty().remove();
        }();

        function flowFormat(e) {
            return (e/1024/1024).toFixed(2);
        }
    }

    Frame.destory = function () {
        tableJq && tableJq.destory(), textConst = tableDiv = confirmPass = updateDiv = null, Table = null
    };
    $(function (e) {
        init()
    })
}();
