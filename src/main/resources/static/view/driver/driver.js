!function () {
    var tableJq, addDiv, confirmPass, tableDiv = $("#driver"),
        textConst = Frame.INFO.text,driveConst = Frame.INFO.drive
        ,editText = Frame.INFO.editText;

    function del(e) {
        //删除前检验
        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "driver/delete", data: {id: e.id, passwd: md5(pass)}, onSuccess: query})
        }
        Frame.Msg.confirm({
            content: editText.remove,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: textConst.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }
   
    function add(e) {
        $("#uploadFile", addDiv).on("click", function () {
            $("input#file", addDiv).click()
        });
        $("input#file", addDiv).on("change", function () {
            var e = $(this).val().split("\\"), a = e[e.length - 1];
            $(this).siblings("i").html(a)
        });
        $("#file", addDiv).siblings("i").empty();
        Frame.Msg.prompt({
            title: textConst.file,
            content: addDiv,
            onConfirm: function a() {
                let val = $("#protocol", addDiv).val();
                if ("all" == val) {
                    return Frame.Msg.error("请选择驱动");
                }
                Frame.formRequest({url: "driver/add", el: addDiv, onSuccess: query})
            }
        });
    }

    function query(e) {
        var a = {page: 1, size: $(".toolbar select.pagenum", tableDiv).val()};
        let company = $(".filter input[name='company']", tableDiv).val();
        let name = $(".filter input[name='name']", tableDiv).val();
        let protocol = $(".filter select", tableDiv).val();
        company && (a.company = company);
        name && (a.name = name);
        "all" != protocol && (a.protocol = protocol);
        e && $.extend(a, e);
        tableJq.refresh(a)
    }


    function init() {
        var option = {
            url: "driver/page",
            pagination: !0,
            columns: [
                {name: "company", title: textConst.company},
                {name: "name", title: textConst.name},
                {name: "protocol", title: driveConst.protocol},
                {name: "gmtCreate", title: textConst.gmtCreate, format: Frame.Time.formatTime},
                {name: "remark", title: textConst.describ}
            ],
            operations: [
                {name: "remove",action: del,fShow: function (e) {
                    return 0 != e.type || 0 == Frame.Default.role
                }},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"company",placeholder: Frame.INFO.search.company,action: query},
                    {name: "input",name2:"name",placeholder: Frame.INFO.search.driveName,action: query},
                    {name: "select",name2:"protocol", content: Frame.createDictSelect(!0,"DRIVER_PROTOCOL"), action: query}],
                operations: [{name: "add", action: add}]
            },
            onSelectPage: function (e) {
                query({size: e})
            },
            onChangePageSize: function (e) {
                query({page: e})
            },
        };
        tableJq = tableDiv.datagrid(option)

        !function replaceText() {
            var e = $("#addFile").html(), a = {
                "{name}": textConst.name,
                "{protocol}": driveConst.protocol,
                "{file}": textConst.file,
                "{remark}": textConst.describ,
                "{uploadFile}": textConst.file,
            };
            e = Frame.replaceTitle(e, a);
            addDiv = $(e);
            addDiv.find("#protocol").append(Frame.createDictSelect(!0,"DRIVER_PROTOCOL"));
            $("#addFile").empty().remove();

            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}": textConst.passwd});
            confirmPass = $(t);
            $("#confirm-pass").empty().remove();
        }();
    }

    Frame.destory = function () {
        tableJq && tableJq.destory(), textConst = tableDiv = confirmPass = addDiv = null, Table = null
    };
    $(function (e) {
        init()
    })
}();
