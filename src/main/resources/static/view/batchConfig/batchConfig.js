!function () {
    var  confirmPass, portDiv = $("#portMappingDiv"),portTableJq
        ,portMappingForm
        ,deviceConst = Frame.INFO.device,textConst = Frame.INFO.text;


    function showDeviceTable() {
        var div = $("<div></div>");
        let param = {};
        Table.init(div, param);
        Frame.Msg.prompt({
            title: textConst.table,
            validate: !1,
            content: div,
            onConfirm: function () {
                var e = Table.getDeviceSn();
                if (e && e.length > 400) {
                    return Frame.Msg.error("字符长度过长！");
                }
                $("#snList").val(e);
            }
        })
    }


    function init() {
        function portMappingQuery(param) {
            let n = {page: 1, size: $(".toolbar select.pagenum", portDiv).val()};
            param && $.extend(n, param);
            portTableJq.refresh(n);
        }

        function portMappingRemove(e) {
            Frame.setRequest({url: `/device/delPortMapping?id=${e.id}` , onSuccess: function(){
                    Frame.Msg.info("成功");
                    portMappingQuery();
                }});
        }

        function portMappingAdd() {
            Frame.Msg.prompt({
                title:textConst.add,
                content: portMappingForm,
                onConfirm: function () {
                    let status = 1;
                    let name = portMappingForm.find("#portName").val();
                    let protocol = portMappingForm.find("#portProtocol").val();
                    let gatewayPort = portMappingForm.find("#gatewayPort").val();
                    let devIp = portMappingForm.find("#devIp").val();
                    let devPort = portMappingForm.find("#devPort").val();
                    if (!Frame.Util.isDottedIPv4(devIp)) {
                        return Frame.Msg.error("必须是合法IP");
                    }
                    let param = {status,name,protocol,gatewayPort,devIp,devPort};
                    Frame.setRequest({url: "/device/savePortMapping", data: param, onSuccess: function(){
                            Frame.Msg.info("成功");
                            portMappingQuery();
                        }});
                }
            })
        }

        // 向网关新增 将端口映射规则给网关
        function portMappingDownAdd(record) {
            // record为当前映射规则行
            let snListStr = $("#snList").val();
            if (!Frame.Util.isNotBlank(snListStr)) {
                Frame.Msg.error("请选择设备");
                return ;
            }
            if (!record.name) {
                return Frame.Msg.error("映射规则名称为空");
            }
            let nameList = [record.name];
            let nameListStr = nameList.join(",");
            let param = {snList:snListStr,nameList:nameListStr};
            Frame.setRequest({url: "/device/sendPortMappingAdd", data: param, onSuccess: function(){
                    Frame.Msg.info("成功");
                    portMappingQuery();
                }});
        }

        // 从网关删除映射规则，不知道网关有没有，只是下发这个命令给网关，后续网关自己判断处理
        function portMappingDownDel(record) {
            let snListStr = $("#snList").val();
            if (!Frame.Util.isNotBlank(snListStr)) {
                Frame.Msg.error("请选择设备");
                return ;
            }
            if (!record.name) {
                return Frame.Msg.error("映射规则名称为空");
            }
            let nameList = [record.name];
            let nameListStr = nameList.join(",");
            let param = {snList:snListStr,nameList:nameListStr};
            Frame.setRequest({url: "/device/sendPortMappingDel", data: param, onSuccess: function(){
                    Frame.Msg.info("成功");
                    portMappingQuery();
                }});
        }

        var e = {
            url: "device/portMappingPage",
            pagination: true,
            multiSelect: false,
            columns: [
                {name: "name", title: textConst.name}
                ,{name: "protocol", title: textConst.protocol}
                ,{name: "gatewayPort", title: textConst.sourcePort}
                ,{name: "devIp", title: textConst.destinationIP}
                ,{name: "devPort", title: textConst.destinationPort}
                ,{name: "gmtCreate",title: textConst.gmtCreate,format: Frame.Time.formatTime}
                ],
            operations: [
                {name: "upload", title: '向网关新增', action: portMappingDownAdd},
                {name: "restart", title: '从网关移除', action: portMappingDownDel},
                {name: "remove", action: portMappingRemove,  fShow: function (e) {
                    return 0 != e.type || 0 == Frame.Default.role
                }},
            ],
            toolbar: {
                operations: [
                    {name: "add", action: portMappingAdd}
                ]
            },
            onSelectPage: function (e) {
                portMappingQuery({size: e})
            },
            onChangePageSize: function (e) {
                portMappingQuery({page: e})
            },
        };
        portTableJq = portDiv.datagrid(e)

        !function deviceTable() {
            $('#snList').click(function () {
                showDeviceTable();
            })
        }()

        !function portMappingFormFun() {
            let t = $("#portMappingForm").html();
            portMappingForm = $(t);
            $("#portMappingForm").empty().remove(), portMappingForm.validateForm();
        }();

        !function replaceNetworkSetUp(){
            $("#rule1").validateForm();
            $("#rule2").validateForm();
            $("#rule3").validateForm();
            $("#rule4").validateForm();
            $("#rule5").validateForm();

            $('#myTab a').click(function () {
                $("#myTab").find("li[class ='active']").removeClass("active");
                $(this).parent().addClass("active");
                let href = $(this).attr("data-href");
                $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
                $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');

            })

            $("#btn1").click(function (){
                let snListStr = $("#snList").val();
                let split = snListStr.split(",");
                if (split.length < 1) {
                    Frame.Msg.error("请选择设备");
                    return ;
                }
                let ntpServer = $("#ntpServer").val();
                let interval = $("#interval").find('option:selected').val()
                let intervalType = $("#interval").find('option:selected').attr("data-type")
                let validateForm = Frame.Validate($("#rule1"),"form");
                if (validateForm == false) {
                    return ;
                }
                if (Frame.Util.checkChinese(ntpServer)) {
                    Frame.Msg.error("不能输入中文");
                    return ;
                }
                for (let i = 0; i < split.length; i++) {
                    let sn = split[i];
                    let param = {sn,ntpServer,interval,intervalType};
                    Frame.setRequest({url: "/device/ntpSetUp", data: param, onSuccess: function(){
                            Frame.Msg.info("成功");
                        }});
                }

            })
            $("#btn2").click(function (){
                let snListStr = $("#snList").val();
                let split = snListStr.split(",");
                if (split.length < 1) {
                    Frame.Msg.error("请选择设备");
                    return ;
                }
                let status = $("#rule2").find("#portStatus").prop("checked");
                status = status ? 1 : 0;
                let name = $("#rule2").find("#portName").val();
                let protocol = $("#rule2").find("#portProtocol").val();
                let gatewayPort = $("#rule2").find("#gatewayPort").val();
                let devIp = $("#rule2").find("#devIp").val();
                let devPort = $("#rule2").find("#devPort").val();
                if (!Frame.Util.isDottedIPv4(devIp)) {
                    return Frame.Msg.error("必须是合法IP");
                }

                for (let i = 0; i < split.length; i++) {
                    let sn = split[i];
                    let param = {sn,status,name,protocol,gatewayPort,devIp,devPort};
                    Frame.setRequest({url: "/device/savePortMapping", data: param, onSuccess: function(){
                            Frame.Msg.info("成功");
                        }});
                }
            })

            $("#btn3").click(function (){
                let snListStr = $("#snList").val();
                let split = snListStr.split(",");
                if (split.length < 1) {
                    Frame.Msg.error("请选择设备");
                    return ;
                }

                let name = $("#name").val();
                let apn = $("#apn").val();
                let proxy = $("#proxy").val();
                let port = $("#port").val();
                let userName = $("#userName").val();
                let pass = $("#pass").val();
                let mvnpValue = $("#mvnpValue").val();
                if (Frame.Util.checkChinese(name,apn,proxy,port,userName,pass,mvnpValue)) {
                    Frame.Msg.error("不能输入中文");
                    return ;
                }
                if (!Frame.Util.isNotBlank(name) || !Frame.Util.isNotBlank(apn)) {
                    return ;
                }
                Frame.formRequest({url: "/device/apnSetUp", el: $("#rule3"), onSuccess: function(){
                        Frame.Msg.info("成功");
                    }});
            })

            $("#btn4").click(function (){
                let snListStr = $("#snList").val();
                let split = snListStr.split(",");
                if (split.length < 1) {
                    Frame.Msg.error("请选择设备");
                    return ;
                }

                let enableBindIP = $("#enableBindIP").prop("checked");
                enableBindIP = enableBindIP == true ? "1" : "0";
                let bindIP = $("#bindIP").val();
                if (enableBindIP == "1" && !Frame.Util.isDottedIPv4(bindIP)) {
                    Frame.Msg.error("请输入合法IP地址");
                    return ;
                }
                for (let i = 0; i < split.length; i++) {
                    let sn = split[i];
                    let param = {sn:sn,enable:enableBindIP,bindIP:bindIP};
                    Frame.setRequest({url: "/device/bindIPSetUp", data: param, onSuccess: function(){
                            Frame.Msg.info("成功");
                        }});
                }

            })

            $("#btn5").click(function (){
                let snListStr = $("#snList").val();
                let split = snListStr.split(",");
                if (split.length < 1) {
                    Frame.Msg.error("请选择设备");
                    return ;
                }

                let validateForm = Frame.Validate($("#rule5"),"form");
                if (validateForm == false) {
                    return ;
                }
                let sn = $("#rule5").find("input[name='sn']").val();
                let monitor = $("#enableNetworkMonitor").prop("checked");
                monitor = monitor == true ? "1" : "0";
                let ip = $("#ipOrDomainName").val();
                let ipBackup = $("#ipOrDomainName2").val();
                let probeInterval = $("#probeInterval").val();
                let messageNum = $("#messageNum").val();
                if (!Frame.Util.isDottedIPv4(ip)) {
                    if (!Frame.Util.checkdomain(ip)) {
                        Frame.Msg.error("请输入合法IP地址或域名");
                        return ;
                    }
                }
                if (Frame.Util.isNotBlank(ipBackup)) {
                    if (!Frame.Util.isDottedIPv4(ipBackup) && !Frame.Util.checkdomain(ipBackup)) {
                        Frame.Msg.error("请输入合法IP地址或域名");
                        return ;
                    }
                }
                if (!Frame.Util.isNotBlank(probeInterval)) {
                    Frame.Msg.error("探测间隔必填(1-300)");
                    return ;
                }
                if (!Frame.Util.isNotBlank(messageNum)) {
                    Frame.Msg.error("连续探测报文数必填(1-20)");
                    return ;
                }
                for (let i = 0; i < split.length; i++) {
                    let sn = split[i];
                    let param = {sn,monitor,ip,probeInterval,messageNum,ipBackup};
                    Frame.setRequest({url: "/device/networkMonitor", data: param, onSuccess: function(){
                            Frame.Msg.info("成功");
                        }});
                }
            })

        }();
    }



    Frame.destory = function () {
        portTableJq && portTableJq.destory();
        portTableJq =  portDiv = portMappingForm = deviceConst = textConst = null;
        Table = null;
    };
    $(function (e) {
        init()
    })
}();
