<style>
    .modal-dialog{
        width: 900px!important;
    }
</style>

<div id="networkSetUp" >
    <div class="panel-heading"><span class="title"></span> <span class="close">X</span></div>
    <ul class="nav nav-tabs" id="myTab">
        <li class="active"><a data-href="#rule1">NTP设置</a></li>
        <li ><a data-href="#rule2">端口映射规则</a></li>
      <!--  <li ><a data-href="#rule3">NAT规则</a></li>-->
        <!--<li ><a data-href="#rule4">绑定IP设置</a></li>-->
        <li ><a data-href="#rule5">网络监控</a></li>
    </ul>
    <div  style="height: 5rem;margin-top: 2rem;">
        <form class="form-horizontal" novalidate>
            <div class="form-group">
                <label for="snList" class="col-sm-3 control-label required">网关：</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control required" id="snList" name="snList" maxlength="500" >
                </div>
            </div>
        </form>
    </div>
    <div id="mytab-content2" class="tab-content">
        <div class="tab-pane fade active" id="rule1">
            <form class="form-horizontal" novalidate>
                <div class="form-group"><label for="ntpServer" class="col-sm-3 control-label required">NTP服务器：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="ntpServer" name="ntpServer" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="interval" class="col-sm-3 control-label required">间隔：</label>
                    <div class="col-sm-6">
                        <select id="interval" name="interval" class="form-control form-control-placeholder">
                            <option value="0" data-type="0" style="color: black;" selected>自动</option>
                            <option value="10" data-type="1" style="color: black;">10分钟</option>
                            <option value="20" data-type="1" style="color: black;">20分钟</option>
                            <option value="30" data-type="1" style="color: black;">30分钟</option>
                            <option value="1" data-type="2" style="color: black;">1小时</option>
                            <option value="2" data-type="2" style="color: black;">2小时</option>
                            <option value="4" data-type="2" style="color: black;">4小时</option>
                            <option value="8" data-type="2" style="color: black;">8小时</option>
                            <option value="24" data-type="2" style="color: black;">1天</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn1">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule2">
            <div id="portMappingDiv"></div>

        </div>
        <div class="tab-pane fade" id="rule3">
            <div id="natDiv"></div>
            <form class="form-horizontal" novalidate>
                <div class="form-group"><label for="natStatus" class="col-sm-3 control-label required">启用：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="natStatus" />
                    </div>
                </div>
                <div class="form-group"><label for="natName" class="col-sm-3 control-label required">名称：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="natName" />
                    </div>
                </div>
                <div class="form-group"><label for="natType" class="col-sm-3 control-label required">动作：</label>
                    <div class="col-sm-6">
                        <select id="natType"  class="form-control form-control-placeholder">
                            <option value="SNAT"  style="color: black;" selected>SNAT</option>
                            <option value="MASQUERADE"  style="color: black;">MASQUERADE</option>
                            <option value="ACCEPT"  style="color: black;">ACCEPT</option>
                        </select>
                    </div>
                </div>
                <div class="form-group"><label for="natProtocol" class="col-sm-3 control-label required">协议：</label>
                    <div class="col-sm-6">
                        <select id="natProtocol"  class="form-control form-control-placeholder">
                            <option value="TCP"  style="color: black;" selected>TCP</option>
                            <option value="UDP"  style="color: black;">UDP</option>
                            <option value="TCP,UDP"  style="color: black;">TCP/UDP</option>
                        </select>
                    </div>
                </div>
                <div class="form-group"><label for="natSrouceIP" class="col-sm-3 control-label required">源地址：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="natSrouceIP" />
                    </div>
                </div>
                <div class="form-group"><label for="destinationIP" class="col-sm-3 control-label required">目的地址：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="destinationIP"  />
                    </div>
                </div>
                <div class="form-group"><label for="rewriteIP" class="col-sm-3 control-label required">重写地址：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="rewriteIP"  />
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn3">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule4">
            <form class="form-horizontal" novalidate>
                <div class="form-group"><label for="enableBindIP" class="col-sm-3 control-label required">启用绑定IP：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="enableBindIP" name="enableBindIP" />
                    </div>
                </div>
                <div class="form-group"><label for="bindIP" class="col-sm-3 control-label">绑定IP：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control" id="bindIP" name="bindIP" maxlength="64">
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn4">确认</button></div>
                </div>
            </form>
        </div>
        <div class="tab-pane fade" id="rule5">
            <form class="form-horizontal" novalidate>
                <div class="form-group"><label for="enableNetworkMonitor" class="col-sm-3 control-label required">启用网络监控：</label>
                    <div class="col-sm-6">
                        <input type="checkbox" class="required" id="enableNetworkMonitor" name="enableNetworkMonitor" />
                    </div>
                </div>
                <div class="form-group"><label for="ipOrDomainName" class="col-sm-3 control-label required">IP或域名：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="ipOrDomainName" name="ipOrDomainName" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="ipOrDomainName2" class="col-sm-3 control-label">第二IP或域名：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control" id="ipOrDomainName2" name="ipOrDomainName2" maxlength="64">
                    </div>
                </div>
                <div class="form-group"><label for="probeInterval" class="col-sm-3 control-label required">探测间隔(1-300s)：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="probeInterval" name="probeInterval" oninput="this.value = Frame.Util.inputNum(this.value,1,300)" />

                    </div>
                </div>
                <div class="form-group"><label for="messageNum" class="col-sm-3 control-label required">连续探测报文数(1-20)：</label>
                    <div class="col-sm-6">
                        <input type="text" class="form-control required" id="messageNum" name="messageNum" oninput="this.value = Frame.Util.inputNum(this.value,1,20)" />
                    </div>
                </div>
                <div class="form-group">
                    <label for="interval" class="col-sm-3 control-label"></label>
                    <div class="col-sm-6"><button type="button" class="btn btn-info" id="btn5">确认</button></div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="hide" id="portMappingForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="portStatus" class="col-sm-3 control-label required">启用：</label>
            <div class="col-sm-6">
                <input type="checkbox" class="required" id="portStatus" disabled checked />
            </div>
        </div>
        <div class="form-group"><label for="portName" class="col-sm-3 control-label required">名称：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="portName" />
            </div>
        </div>
        <div class="form-group"><label for="portProtocol" class="col-sm-3 control-label required">协议：</label>
            <div class="col-sm-6">
                <select id="portProtocol"  class="form-control form-control-placeholder">
                    <option value="TCP"  style="color: black;" selected>TCP</option>
                    <option value="UDP"  style="color: black;">UDP</option>
                    <option value="TCP,UDP"  style="color: black;">TCP/UDP</option>
                </select>
            </div>
        </div>
        <div class="form-group"><label for="gatewayPort" class="col-sm-3 control-label required">源端口：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control  required" id="gatewayPort"oninput="this.value = Frame.Util.inputNum(this.value,1,65535)" />
            </div>
        </div>
        <div class="form-group"><label for="devIp" class="col-sm-3 control-label required">目的IP地址：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="devIp" />
            </div>
        </div>
        <div class="form-group"><label for="devPort" class="col-sm-3 control-label required">目的端口：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="devPort"  oninput="this.value = Frame.Util.inputNum(this.value,1,65535)"/>
            </div>
        </div>
    </form>
</div>


<div class="hide" id="natForm">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="portStatus" class="col-sm-3 control-label required">启用：</label>
            <div class="col-sm-6">
                <input type="checkbox" class="required" id="natStatus" disabled checked />
            </div>
        </div>
        <div class="form-group"><label for="portName" class="col-sm-3 control-label required">名称：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="natDisplayName" />
            </div>
        </div>
        <div class="form-group"><label for="portProtocol" class="col-sm-3 control-label required">规则类型：</label>
            <div class="col-sm-6">
                <select id="natTarget"  class="form-control form-control-placeholder">
                    <option value="all"  style="color: black;" selected>SNAT</option>
                    <option value="tcp"  style="color: black;" >tcp</option>
                    <option value="udp"  style="color: black;">udp</option>
                    <option value="tcp/udp"  style="color: black;">tcp/udp</option>
                    <option value="icmp"  style="color: black;">icmp</option>
                </select>
            </div>
        </div>
        <div class="form-group"><label for="portProtocol" class="col-sm-3 control-label required">协议：</label>
            <div class="col-sm-6">
                <select id="natProtocol"  class="form-control form-control-placeholder">
                    <option value="all"  style="color: black;" selected>ALL</option>
                    <option value="tcp"  style="color: black;" >tcp</option>
                    <option value="udp"  style="color: black;">udp</option>
                    <option value="tcp/udp"  style="color: black;">tcp/udp</option>
                    <option value="icmp"  style="color: black;">icmp</option>
                </select>
            </div>
        </div>
        <div class="form-group"><label for="gatewayPort" class="col-sm-3 control-label required">源端口：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control  required" id="gatewayPort"oninput="this.value = Frame.Util.inputNum(this.value,1,65535)" />
            </div>
        </div>
        <div class="form-group"><label for="devIp" class="col-sm-3 control-label required">目的IP地址：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="devIp" />
            </div>
        </div>
        <div class="form-group"><label for="devPort" class="col-sm-3 control-label required">目的端口：</label>
            <div class="col-sm-6">
                <input type="text" class="form-control required" id="devPort"  oninput="this.value = Frame.Util.inputNum(this.value,1,65535)"/>
            </div>
        </div>
    </form>
</div>
<!--<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>-->
<script type="text/javascript" src="view/common/device.js?v=d459cce937"></script>
<script type="text/javascript" src="view/batchConfig/batchConfig.js?v=060e8860b2"></script>
