!function () {
    var totalTable,totalTableDiv = $("#total"),totalInfoDiv = $("#totalDiv"), user =  {
            company : "公司",
            userNum: "用户数",
            deviceNum: "设备数",
            username: "用户名",
            lastLoginTime: "上次登录时间",
    }
    , activeTable, activeDiv = $("#active"),
    treeDiv = $("#tree");


    $('#myTab a').click(function () {
        $("#myTab").find("li[class ='active']").removeClass("active");
        $(this).parent().addClass("active");
        let href = $(this).attr("data-href");
        $("#mytab-content2 div[class ='tab-pane fade active']").attr("class", 'tab-pane fade');
        $(`#mytab-content2 ${href}`).attr("class", 'tab-pane fade active');
    })

    function queryStatistic(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", totalTableDiv).val()};
        e && $.extend(t, e);
        totalTable.refresh(t);

        initTotalInfo();
    }

    function initTotalInfo(){
        Frame.setRequest({url: "userStatistics/statistic",loading:false, success: function (resp){
                if (resp.code == 200) {
                    let data = resp.data;
                    $("#companyNumSpan", totalInfoDiv).html(data.companyNum);
                    $("#userNumSpan", totalInfoDiv).html(data.userNum);
                    $("#deviceNumSpan", totalInfoDiv).html(data.deviceNum);
                    $("#alarmNumSpan", totalInfoDiv).html(data.alarmNum);
                } else {
                    Frame.Msg.error(resp.msg);
                }
            }})
    }

    function initTotal() {
        var e = {
            url: "userStatistics/userStatisticsTable",
            pagination: !0,
            pageList: [1, 2, 60, 80, 100],
            columns: [
                {name: "company", title: user.company},
                {name: "userNum", title: user.userNum},
                {name: "deviceNum", title: user.deviceNum},
            ],
            toolbar: {
                operations: [
                    {name: "refresh", action: queryStatistic},
                ]
            },
            onSelectPage: function (e) {
                queryStatistic({size: e})
            },
            onChangePageSize: function (e) {
                queryStatistic({page: e})
            }
        };
        totalTable = totalTableDiv.datagrid(e);
    }

    function queryActive(e) {
        var t = {page: 1, size: $(".toolbar select.pagenum", activeDiv).val()};
        e && $.extend(t, e);
        activeTable.refresh(t);
    }

    function initActive() {
        let e = {
            url: "userStatistics/activeUser",
            pagination: !0,
            columns: [
                //{name: "id", title: "id"},
                {name: "company", title: user.company},
                {name: "name", title: user.username},
                {name: "lastLoginTime", title: user.lastLoginTime, format: Frame.Time.formatTime},
            ],
            toolbar: {
                operations: [
                    {name: "refresh", action: queryActive},
                ]
            },
            onSelectPage: function (e) {
                queryActive({size: e})
            },
            onChangePageSize: function (e) {
                queryActive({page: e})
            },
        };
        activeTable = activeDiv.datagrid(e)
    }

    function tree() {
        let height = document.body.clientHeight - 130;
        let width = document.body.clientWidth - 250;
        treeDiv.height(height);
        treeDiv.width(width);
        let chartDom = document.getElementById('tree');
        var myChart = echarts.init(chartDom);
        Frame.setRequest({url: "userStatistics/deviceTree",loading:false,async:false,success: function(resp){
            if (resp.code == 200) {
                let data = resp.data;
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function (node){
                            if (Frame.Util.isNotBlank(node.data.status)) {
                                return `状态：${node.data.status}`;
                            }
                            return node.name;
                        }
                    },
                    series: [
                        {
                            type: 'tree',
                            data: [data],
                            left: '2%',
                            right: '2%',
                            top: '8%',
                            bottom: '20%',
                            symbol: 'emptyCircle',
                            orient: 'vertical',
                            expandAndCollapse: true,
                            label: {
                                position: 'top',
                                rotate: -90,
                                verticalAlign: 'middle',
                                align: 'right',
                                fontSize: 9
                            },
                            leaves: {
                                label: {
                                    position: 'bottom',
                                    rotate: -90,
                                    verticalAlign: 'middle',
                                    align: 'left'
                                }
                            },
                            animationDurationUpdate: 750

                        }
                    ]
                }
                myChart.setOption(option);
            }

        }})

    }

    Frame.destory = function () {
        totalTable && totalTable.destory(), totalTableDiv = totalInfoDiv = user = null;
        activeTable && activeTable.destory(), activeDiv  =  null;
        treeDiv = null;
        $("#myTab a").off("click");
    };
    $(function (e) {
        initTotal();
        initActive();
        initTotalInfo();
        tree();
    })

}();
