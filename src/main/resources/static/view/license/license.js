! function () {
    var a, t, c = $("#license"),
        i = Frame.INFO.license;

    Frame.destory = function () {
        a && a.destory(), i = c = t = null
    }
}();

const btn = document.querySelector("#btn");
//获取文本框
const data = document.querySelector('#expireDate');
btn.addEventListener('click', function() {
    //创建ajax对象
    const xhr = new XMLHttpRequest();
    const dataValue= data.value;
    //拼接求参数
    var params = 'expireDate=' + dataValue ;
    let url = `${window.location.protocol}//${window.location.host}/account/license?` + params;
    xhr.open('GET', url);
    xhr.send();
    //监听响应
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
                console.log(xhr.responseText);
                var blob = new Blob([xhr.responseText])
                var downloadElement = document.createElement('a');
                var href = window.URL.createObjectURL(blob); //创建下载的链接
                downloadElement.href = href;
                downloadElement.download = "license.nms"; //下载后文件名
                document.body.appendChild(downloadElement);
                downloadElement.click(); //点击下载
                document.body.removeChild(downloadElement); //下载完成移除元素
                window.URL.revokeObjectURL(href); //释放掉blob对象

            }
        }
    }


})