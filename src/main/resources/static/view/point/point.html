<div id="point"></div>
<div class="hide" id="addFile">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="file" class="col-sm-3 control-label required">{file}</label>
            <div class="col-sm-6"><input type="file" name="file" id="file" class="hide" accept=".zip">
                <button type="button" class="btn btn-info" id="uploadFile">{uploadFile}</button>
                <i></i></div>
        </div>
    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/point/point.js?v=060e8860b2"></script>
