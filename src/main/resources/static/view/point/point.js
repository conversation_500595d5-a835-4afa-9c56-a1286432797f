!function () {
    var tableJq, addDiv, confirmPass, tableDiv = $("#point"),driverDivJq,driverGrid
        ,pointDivJq,pointGrid,
        deviceConst = Frame.INFO.device,textConst = Frame.INFO.text
        ,editText = Frame.INFO.editText,driveConst = Frame.INFO.drive;

    function query(e) {
        let a = {page: 1, size: $(".toolbar select.pagenum", tableDiv).val()};
        let sn = $(".filter input[name='sn']", tableDiv).val();
        let alias = $(".filter input[name='alias']", tableDiv).val();
        let n = $(".filter select", tableDiv).val();
        sn && (a.sn = sn);
        alias && (a.alias = alias);
        "all" != n && (a.model = n);
        e && $.extend(a, e), tableJq.refresh(a)
    }

    function init() {
        var option = {
            url: "device/point/deviceList",
            pagination: !0,
            columns: [
                {name: "id", title: "id"},
                {name: "name", title: deviceConst.devname},
                {name: "alias", title: deviceConst.alias,orderBy:true},
                {name: "model", title: deviceConst.type},
                {name: "sn", title: deviceConst.sn},
                {name: "position", title: deviceConst.position},
                {name: "company", title: deviceConst.company, role: "0"},
                {name: "username",title: deviceConst.username,role: "0"},
                {name: "describ", title: deviceConst.describ, role: "1,2"},
                {name: "uploadPoint", title: deviceConst.point,format:function (e){
                    return e == true ?"已导入" : "未导入";
                }},
                {name: "driverUpStatus", title: deviceConst.driverUpgradeStatus,format:function (e){
                    if(-1 === e) return "通知升级";
                        if(0 === e) return "升级成功";
                        else if(1 === e) return "cmd=100 数据格式不正确";
                        else if(2 === e) return "文件Md5校验失败";
                        else if(3 === e) return "default_config.json文件缺失";
                        else if(4 === e) return "驱动文件缺失";
                        else if(5 === e) return "default_config.json文件格式不正确";
                        else if(100 === e) return "强制完成";
                        else if (parseInt(e) > 1000) return e;
                        return "";
                    }},
            ],
            toolbar: {
                search: [
                    {name: "input",name2:"sn", placeholder: Frame.INFO.search.sn, action: query},
                    {name: "input",name2:"alias", placeholder: Frame.INFO.search.alias, action: query},
                    {name: "select", content: Frame.createModel(!0), action: query},
                ],
                operations: [
                    {name: "refresh", action: query},
                ]
            },
            operations: [
                {name: "upload", title: deviceConst.uploadPoint, action: uploadPoint},
                {name: "upload", title: deviceConst.upgradeDriver, action: selectDriver, role: "0,1"},
                {name: "refresh", title: deviceConst.forceComplete, action: forceComplete, role: "0,1"},
                {name: "download", title: deviceConst.downPoint, action: downloadJson, role: "0,1"},
            ],
            onSelectPage: function (e) {
                query({size: e})
            },
            onChangePageSize: function (e) {
                query({page: e})
            },
            subGrid: {
                format: function (e) {
                    return !(!e.baseDevNum || 0 == e.baseDevNum)
                },
                options: {
                    url: "device/point/userBaseDevice",
                    pagination: !1,
                    columns: [
                        {name: "userDeviceId", title: "id",role:"0"},
                        {name: "baseName", title: deviceConst.name},
                        {name: "baseIp", title: deviceConst.ip},
                        {name: "baseDumIp", title: deviceConst.vip},
                        {name: "baseMfrsName", title: deviceConst.companyinfo},
                        {name: "baseDescrib", title: deviceConst.describ},
                    ],
                    operations: [
                        {name: "upload", title: deviceConst.uploadPoint, action: uploadPoint},
                        {name: "upload", title: deviceConst.upgradeDriver, action: selectDriver, role: "0,1"},
                        {name: "add", title: deviceConst.point, action: showPointTable, role: "0,1"},

                    ],
                }
            }
        };

        tableJq = tableDiv.datagrid(option);

        !function replaceText() {
            var e = $("#addFile").html(), a = {
                "{file}": textConst.file,
                "{uploadFile}": textConst.file,
            };
            e = Frame.replaceTitle(e, a);
            addDiv = $(e);
            $("#addFile").empty().remove();

            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}": textConst.passwd});
            confirmPass = $(t);
            $("#confirm-pass").empty().remove();
        }();
    }

    function uploadPoint(e) {
        $("#uploadFile", addDiv).on("click", function () {
            $("input#file", addDiv).click()
        });
        $("input#file", addDiv).on("change", function () {
            var e = $(this).val().split("\\"), a = e[e.length - 1];
            $(this).siblings("i").html(a)
        });
        $("#file", addDiv).siblings("i").empty();
        Frame.Msg.prompt({
            title: textConst.file,
            content: addDiv,
            onConfirm: function a() {
                let url = null;
                if (e.userDeviceId ) {
                    url = `device/point/uploadDev?deviceId=${e.userDeviceId}&devName=${e.baseName}`;
                } else {
                    url = `device/point/upload?deviceId=${e.id}`;
                }
                Frame.formRequest({url: url, el: addDiv, onSuccess: query})
            }
        });
    }

    function showPointTable(dev) {
        let userDeviceId =  dev.userDeviceId;
        let devName = dev.baseName;
        let column = [];
        Frame.setRequest({url: "device/point/pointKey",async:false
            ,data:{deviceId:userDeviceId,"devName":devName},onSuccess:function (data){
                if (data != null && data.length > 0) {
                    for (let i = 0; i < data.length; i++) {
                        let obj = {name: data[i], title: data[i]};
                        column.push(obj);
                    }
                }
            }
        })
        pointDivJq = $("<div></div>");
        function page(param){
            var a = {page: 1, size: $(".toolbar select.pagenum", pointDivJq).val()};
            param && $.extend(a, param), pointGrid.refresh(a)
        }
        var option = {
            url: "device/point/pointTable",
            data: {page: 1, size: 20,deviceId:userDeviceId,"devName":devName},
            pagination: !0,
            columns: column,
            onSelectPage: function (size) {
                let a = {size: size,deviceId:userDeviceId,"devName":devName};
                page(a)
            },
            onChangePageSize: function (pageNum) {
                let a = {page: pageNum,deviceId:userDeviceId,"devName":devName};
                page(a)
            },
        };
        pointGrid = pointDivJq.datagrid(option);

        Frame.Msg.prompt({
            title: textConst.table,
            validate: !1,
            content: pointDivJq,
            width: 700,
            maxHeight: 500,
            onConfirm: function i() {
            }
        })
    }

    function downloadJson(basedev) {
        if (basedev.point == false) {
            Frame.Msg.error(Frame.INFO.error[6001]);
            return ;
        }
        window.open(`device/point/downloadJson?deviceId=${basedev.id}`
            , "_blank")

    }

    function selectDriver(devObj) {
        driverDivJq = $("<div></div>");

        function getDriver() {
            var e = driverGrid.getSelectedData(), n = [];
            for (let i = 0; i < e.length; i++) n.push(e[i].id);
            return n;
        };
        function page(param){
            var a = {page: 1, size: $(".toolbar select.pagenum", driverDivJq).val()};
            param && $.extend(a, param), driverGrid.refresh(a)
        }
        var option = {
            url: "driver/page",
            data: {page: 1, size: 20},
            pagination: !0,
            multiSelect: true,
            columns: [
                {name: "id", title: "id",role:"0"},
                {name: "company", title: textConst.company},
                {name: "name", title: textConst.name},
                {name: "protocol", title: driveConst.protocol},
                {name: "remark", title: textConst.describ}],
            onSelectPage: function (size) {
                let a = {size: size};
                page(a)
            },
            onChangePageSize: function (pageNum) {
                let a = {page: pageNum};
                page(a)
            },
        };
        driverGrid = driverDivJq.datagrid(option)


        Frame.Msg.prompt({
            title: textConst.upgrade,
            validate: !1,
            content: driverDivJq,
            width: 700,
            maxHeight: 500,
            onConfirm: function i() {
                var idArray = getDriver();
                if (idArray.length == 0) {
                    Frame.Msg.error("请选择数据");
                    return;
                }
                let data = null;
                let driverStr = idArray.join(",");
               /* if (devObj.userDeviceId ) {
                    data = {deviceId: devObj.userDeviceId, devName: devObj.baseName,driverId:driverStr}
                } else {
                    data = {deviceId: devObj.id,driverId:driverStr}
                }*/
                data = {deviceId: devObj.id,driverId:driverStr}
                Frame.setRequest({url: "device/point/upgradeDriver", data: data, onSuccess: query})
            }
        })
    }


    function forceComplete(row) {
        Frame.Msg.confirm({
            content: textConst.confirm,
            onConfirm: function () {
                Frame.setRequest({url: "device/point/forceComplete"
                    , data: {deviceId: row.id}, onSuccess: query})
            }
        })
    }

    Frame.destory = function () {
        tableJq && tableJq.destory();
        deviceConst = textConst = tableDiv = confirmPass = addDiv = null;
        Table = null;
    };
    $(function (e) {
        init()
    })
}();
