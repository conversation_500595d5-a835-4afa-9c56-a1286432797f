!function () {
    var r = Frame.INFO.users, t = Frame.INFO.search, n = $("#editpass");
    function o() {
        return !!Frame.Validate($("#repeatPasswd", n), "input") && ($("#newPasswd", n).val() != $("#repeatPasswd", n).val() ? ($("#repeatPasswd", n).setError(Frame.INFO.validate.repeat), !1) : ($("#repeatPasswd", n).setError(""), !0))
    }

    function d() {
        var a = $("#oldPasswd", n).val(), e = $("#newPasswd", n).val();
        $("#repeatPasswd", n).val();
        if (!Frame.Validate(n, "form") || !o()) return !1;
        var s = {oldPasswd: md5(a), newPasswd: e};
        Frame.setRequest({
            url: "account/changePasswd", data: s, onSuccess: function t() {
                Frame.Msg.confirm({
                    content: r.editOk, onConfirm: function () {
                        window.location.href = "login.html"
                    }
                })
            }
        })
    }

    Frame.destory = function () {
        $("button", n).off("click"), $("#repeatPasswd", n).off("focusout"), n = t = r = null
    }, $(function (a) {
        !function e() {
            $("label[for='oldPasswd']", n).html(r.oldPasswd), $("label[for='newPasswd']", n).html(r.newPasswd), $("label[for='repeatPasswd']", n).html(r.repeatPasswd), $("#oldPasswd", n).attr("placeholder", t.oldPasswd), $("#newPasswd", n).attr("placeholder", t.newPasswd), $("#repeatPasswd", n).attr("placeholder", t.repeatPasswd), $("button", n).html(Frame.INFO.msg.ok), $("input", n).placeholder(), $("#username", n).val(Frame.Default.user.name), n.validateForm()
        }(), function s() {
            $("button", n).on("click", d), $("#repeatPasswd", n).on("focusout", function () {
                return o(), !1
            })
        }()
    })
}();