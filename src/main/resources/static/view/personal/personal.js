!function(){var i=Frame.INFO.users;function l(e){0==e.role?e.role=i.systemManager:1==e.role?e.role=i.accountManager:e.role=i.customerOperator;for(var t=[{title:i.username,value:e.name},{title:i.company,value:e.company},{title:i.role,value:e.role},{title:i.lastLoginTime,value:Frame.Time.formatTime(e.lastLoginTime)},{title:i.lastLogoutTime,value:Frame.Time.formatTime(e.lastLogoutTime)},{title:i.lastLoginIp,value:e.lastLoginIp}],l="",a=0;a<t.length;a++){var o=t[a];l+='<div class="col-sm-12 info"><div class="col-sm-2 title">'+o.title+':</div><div class="col-sm-10">'+o.value+"</div></div>"}$("#personal").html(l)}Frame.destory=function(){i=null},$(function(e){!function t(){$("h4").html(i.personalInfo),Frame.getRequest({url:"account/currentUser",onSuccess:l})}()})}();