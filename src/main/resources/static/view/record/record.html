<div id="record"></div>
<div class="hide" id="addDevice">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="model" class="col-sm-3 control-label required">{model}</label>
            <div class="col-sm-6"><select class="form-control" id="model"></select></div>
        </div>
        <div class="form-group"><label for="sn" class="col-sm-3 control-label required">{sn}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="sn" maxlength="16"></div>
        </div>
    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<form class="hide" id="uploadForm"><input type="file" name="file" id="upload"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"></form>
<script type="text/javascript" src="view/record/record.js?v=a1206b5fbb"></script>