! function () {
    var a, t, c = $("#record"),confirmPass,
        i = Frame.INFO.device,userVar = Frame.INFO.users,userTable;

    function r() {
        Frame.Msg.prompt({
            title: i.addDevice,
            content: t,
            onConfirm: function e() {
                $("#model").val(), $("#sn").val();
                Frame.setRequest({
                    url: "device/addDevice",
                    data: Frame.getFormData(t),
                    onSuccess: d
                })
            }
        })
    }

    function l() {
        $("#upload").click()
    }

    function e() {
        Frame.formRequest({
            url: "device/importDevice",
            el: $("#uploadForm"),
            onSuccess: d,
            complete: function (param) {
                $("#uploadForm").clearForm();
                let resp =  param.responseJSON;
                if (2000 == resp.code) {
                    download("sn.txt",resp.rightMsg);
                }
            }
        })
        function download(filename, text) {
            var element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
            element.setAttribute('download', filename);
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        }
    }

    function m(e) {
        Frame.Msg.confirm({
            content: i.removeDevice,
            onConfirm: function n() {
                Frame.setRequest({
                    url: "device/removeDevice",
                    data: {
                        sn: e.sn
                    },
                    onSuccess: d
                })
            }
        })
    }

     //重置
    function restartDevice(e){
        Frame.Msg.prompt({
            title: Frame.INFO.text.passwd,
            content: confirmPass,
            data: {username: Frame.Default.user.name},
            onConfirm: function () {
                var pass = $("#password", confirmPass).val();
                let data={
                    sn:e.sn,
                    id:e.id,
                    passwd:md5(pass)
                }
                Frame.setRequest({url: "device/reactivate", data: data, onSuccess: d})
            }
        })
    }


    //分配一个设备
    function allocateMultiDevice(e){
        var e = $("<div></div>");
        userTable.init(e,null);
        Frame.Msg.prompt({
            title: Frame.INFO.text.confirm,
            validate: !1,
            content: e,
            width: 700,
            maxHeight: 500,
            onConfirm: function i() {
                let name = userTable.getUsername();
                if (name && name.indexOf(",") >= 0) {
                    Frame.Msg.error(Frame.INFO.msg.radioUser);
                    return ;
                }
                let idarr = [];
                for (let e = a.getSelectedData(), n = [], i = 0; i < e.length; i++) idarr.push(e[i].id);
                if (idarr.length < 1) {
                    Frame.Msg.error(Frame.INFO.msg.selectDevice);
                    return ;
                }
                let data ={
                    deviceIds:null,
                    username: name
                }
                data.deviceIds = idarr.join(",");
                Frame.setRequest({url: "device/allocateMultiDevice", data: data,onSuccess:d})
            }
        })
    }

    function d(param) {
        var n = {
                page: 1,
                size: $(".toolbar select.pagenum", c).val()
            },
            o = $(".filter select", c).val(),
        t = $(".filter input", c).val();
        "all" != o && (n.model = o);
        t && (n.sn = t);
        param && $.extend(n, param), a.refresh(n)
    }

    function exportTemplate(){
        let url = `${window.location.protocol}//${window.location.host}/view/record/recordTemplate.xlsx` ;
        window.open(url);
    }

    function n() {
        ! function n() {
            var e = {
                url: "device/deviceList",
                pagination: !0,
                multiSelect: !0,
                columns: [
                    {name: "id",title: "id",role:"0"}
                ,{
                    name: "model",
                    title: i.type
                }, {
                    name: "sn",
                    title: i.sn
                }],
                operations: [{
                    name: "remove",
                    action: m
                },{
                    name: "reset",
                    action: restartDevice
                }
                ],
                toolbar: {
                    search: [{
                        name: "select",
                        content: Frame.createModel(!0),
                        action: d
                    }, {name: "input",placeholder: Frame.INFO.search.sn,action:d },
                    ],
                    operations: [{name: "add",title: i.allocateDevice,action: allocateMultiDevice},{
                        name: "add",
                        action: r
                    }, {
                        name: "upload",
                        action: l
                    },
                        {name: "download",title: i.downloadTemplate,action: exportTemplate},
                    ]
                },
                onSelectPage: function (e) {
                    d({
                        size: e
                    })
                },
                onChangePageSize: function (e) {
                    d({
                        page: e
                    })
                }
            };
            a = c.datagrid(e)
        }(),
        function o() {
            var e = $("#addDevice").html(),
                n = {
                    "{model}": i.type,
                    "{sn}": i.sn
                };
            e = Frame.replaceTitle(e, n)
                , (t = $(e)).find("#model").append(Frame.createModel())
                , $("#addDevice").empty().remove(), t.validateForm();
        }(), $("#upload").on("change", e);
        let passText = $("#confirm-pass").html();
        passText = Frame.replaceTitle(passText, {"{againPass}": Frame.INFO.text.passwd});
        confirmPass = $(passText);
        $("#confirm-pass").empty().remove();
        initUserTable();
    }

    function initUserTable() {
        var tableGrid = null;
        userTable = {
            init: function i(divJq, param) {
                function page(){
                    var a = {page: 1, size: $(".toolbar select.pagenum", divJq).val()};
                    tableGrid.refresh(a)
                };
                !function t(e, n) {
                    var i = {
                        url: "account/findTenantAdmin",
                        data: {page: 1, size: 20},
                        pagination: !0,
                        multiSelect: !0,
                        columns: [
                            {name: "name", title: userVar.username},
                            {name: "company", title: userVar.company},
                            {name: "describ", title: userVar.describ},
                            ],
                        onSelectPage: function (size) {
                            let a = {size: size};
                            page(a)
                        },
                        onChangePageSize: function (pageNum) {
                            let a = {page: pageNum};
                            page(a)
                        },
                    };
                    $.extend(i.data,n);
                    tableGrid = e.datagrid(i)
                }(divJq, param);
            },
            getUsername: function t() {
                for (var e = tableGrid.getSelectedData(), n = [], i = 0; i < e.length; i++) n.push(e[i].name);
                return n.join(",")
            },
            getDeviceSn: function t() {
                for (var e = tableGrid.getSelectedData(), n = [], i = 0; i < e.length; i++) n.push(e[i].sn);
                return n.join(",")
            },
            destory: function n() {
                 tableGrid = null
            }
        };
    }

    Frame.destory = function () {
        a && a.destory(), i = c = t = confirmPass = userVar = userTable = null
    }, $(function (e) {
        n()
    })
}();