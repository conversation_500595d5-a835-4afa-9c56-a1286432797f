<div id="config"></div>
<div class="hide" id="addConfig">
    <form class="form-horizontal" novalidate>
        <div class="form-group"><label for="name" class="col-sm-3 control-label required">{name}</label>
            <div class="col-sm-6"><input type="text" class="form-control required" id="name" maxlength="64" name="name">
            </div>
        </div>
        <div class="form-group"><label for="model" class="col-sm-3 control-label required">{model}</label>
            <div class="col-sm-6"><select class="form-control" id="model" name="model"></select></div>
        </div>
        <div class="form-group"><label for="file" class="col-sm-3 control-label required">{file}</label>
            <div class="col-sm-6"><input type="file" name="file" id="file" class="hide" accept=".txt,.ini,.cfg,.tar.gz">
                <button type="button" class="btn btn-info" id="uploadFile">{uploadFile}</button>
                <i></i></div>
        </div>
        <div class="form-group"><label for="describ" class="col-sm-3 control-label">{describ}</label>
            <div class="col-sm-6"><input type="text" class="form-control" id="describ" maxlength="64" name="describ">
            </div>
        </div>
    </form>
</div>

<div class="hide" id="companyDiv">
    <form class="form-horizontal" novalidate>
    <div class="form-group"><label for="company" class="col-sm-3 control-label required">{company}</label>
        <div class="col-sm-6"><select class="form-control" id="company" name="company"></select></div>
    </div>
    </form>
</div>
<div class="hide" id="confirm-pass">
    <form class="form-horizontal" autocomplete="off">
        <div class="form-group">
            <label for="password" class="col-sm-3 control-label required">{againPass}</label>
            <div class="col-sm-6">
                <input type="text" id="username" name="username" class="hide" autocomplete="off">
                <input type="password" name="password" class="form-control required" id="password" autocomplete="new-password"></div>
        </div>
    </form>
</div>
<script type="text/javascript" src="view/config/config.js?v=d88f165230"></script>
<script type="text/javascript" src="view/common/device.js?v=d459cce937"></script>