!function () {
    var t, i,companyDiv,confirmPass, o = $("#config"), l = Frame.INFO.device;

    function formatStatus(e) {
        if (0 == e) {
            return '<i class="fa fa-spinner"></i><span>' + l.executing + "</span>";
        } else if (1 == e) {
            return '<i class="fa fa-times-circle"></i><span>' + l.download_fail + "</span>";
        } else if (2 == e) {
            return '<i class="fa fa-times-circle"></i><span>' + l.check_fail + "</span>";
        } else if (3 == e) {
            return '<i class="fa fa-check-circle"></i><span>' + l.success + "</span>";
        } else if (4 == e) {
            return  '<i class="fa fa-check-circle"></i><span>' + l.timeout_upgrade + "</span>";
        } else {
            return '<i class="fa fa-times-circle"></i><span>' + l.download_fail + "</span>";
        }
    }

    function r(e) {
        $("#uploadFile", i).on("click", function () {
            $("input#file", i).click()
        }), $("input#file", i).on("change", function () {
            var e = $(this).val().split("\\"), n = e[e.length - 1];
            $(this).siblings("i").html(n)
        }), $("#file", i).siblings("i").empty(), Frame.Msg.prompt({
            title: l.addConfig,
            content: i,
            onConfirm: function n() {
                Frame.formRequest({url: "config/upload", el: i, onSuccess: f})
            }
        })
    }

    function s(e) {
        //删除前检验
        if (delValidate(e) == false) {
            return ;
        }
        function t(){
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "config/delete", data: {configId: e.id,passwd:md5(pass)}, onSuccess: f})
        }
        Frame.Msg.confirm({
            content: l.removeConfig,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function delValidate(e){
        //删除前检验
        let flag = 1;
        Frame.setRequest({url: "config/upgradeList", data: {id: e.id},async:false,loading: false ,success: function(data){
                if (200 == data.code){
                    let arr = data.data;
                    for (let j = 0; j < arr.length; j++) {
                        if (0 == arr[j].status){
                            flag = 0;
                            return ;
                        }
                    }
                }
            }});
        if (flag == 0) {
            Frame.Msg.error(l.cannotDelete);
            return false;
        }
        return true
    }

    function m(n) {
        var e = $("<div></div>");
        let param = {model:n.model,upgrade: 0,online:1};
        Table.init(e, param), Frame.Msg.prompt({
            title: l.uploadConfig,
            validate: !1,
            content: e,
            onConfirm: function a() {
                var e = Table.getDeviceID();
                Frame.setRequest({url: "config/upgrade", data: {configId: n.id, deviceIds: e}, onSuccess: f})
            }
        })
    }

    function f(e) {
        var n = {page: 1, size: $(".toolbar select.pagenum", o).val()}, a = $(".filter input", o).val();
        a && (n.name = a);
        var i = $(".filter select", o).val();
        "all" != i && (n.model = i), e && $.extend(n, e), t.refresh(n)
    }

    function share(e){
        Frame.Msg.confirm({
            content: companyDiv, onConfirm: function n() {
                let company = $("#company",companyDiv).val();
                Frame.setRequest({url: "config/share", data: {configId:e.id,company:company}, onSuccess: f})
            }
        })
    }

    function forceComplete(e){
        function t(){
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "config/completeUpgrade", data: {id:e.id,passwd:md5(pass)}, onSuccess: f})
        }
        Frame.Msg.confirm({
            content: l.forceCompleteConfirm,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title: l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function delSubGrid(e){
        if (delSubGridValid(e) == false){
            return ;
        }
        function t() {
            var pass = $("#password", confirmPass).val();
            Frame.setRequest({url: "config/deleteUpgrade", data: {configId: e.id, passwd: md5(pass)}, onSuccess: f})
        }
        Frame.Msg.confirm({
            content: l.delConfigUpgradeWarn,
            onConfirm: function () {
                Frame.Msg.prompt({
                    title:l.passwd,
                    content: confirmPass,
                    data: {username: Frame.Default.user.name},
                    onConfirm: t
                })
            }
        })
    }

    function delSubGridValid(e){
        //删除前检验
        let msg = null;
        Frame.setRequest({url: "config/upgradeList", data: {id: e.id},async:false,loading: false ,success: function(data){
                if (200 == data.code){
                    let arr = data.data;
                    if (arr == undefined || arr == null || arr.length < 1){
                        msg = l.noRecord;
                    }
                } else {
                    msg = l.fail;
                }
            }});
        if (msg != null) {
            Frame.Msg.error(msg);
            return false;
        }
        return true
    }


    function n() {
        var e = {
            url: "config/list",
            pagination: !0,
            columns: [
                {name: "id", title: "id",role: "0"},
                {name: "name", title: l.configName}, {name: "model", title: l.type},
                {name: "company", title: l.company},
                {
                name: "gmtCreate",
                title: l.gmtCreate,
                format: Frame.Time.formatTime
            }, {name: "describ", title: l.describ}],
            operations: [{name: "upload", title: l.uploadConfig, action: m, role: "0,1"}, {
                name: "remove",
                action: s,
                fShow: function (e) {
                    return 0 != e.type || 0 == Frame.Default.role
                }
            },
            {name: "restart2",  icon: {icon: "fa fa-repeat", title: l.share},action: share, role: "0"},
            {name: "delSubGrid", title: l.delSubGrid, action: delSubGrid, role: "0,1"},
            ],
            toolbar: {
                search: [{
                    name: "input",
                    placeholder: Frame.INFO.search.configName,
                    action: f
                }, {name: "select", content: Frame.createModel(!0), action: f}],
                operations: [{name: "add", action: r}]
            },
            onSelectPage: function (e) {
                f({size: e})
            },
            onChangePageSize: function (e) {
                f({page: e})
            },
            subGrid: {
                gridArray:[],
                options: {
                    url: "config/upgradePage",
                    pagination: 1,
                    columns: [
                        {name: "id", title: "id",role: "0"},
                        {name: "name", title: l.devname},
                        {name: "sn", title: l.sn},
                        {name: "gmtCreate", title: l.gmtCreate,format: Frame.Time.formatTime},
                        {name: "status",title: l.status,format: formatStatus,
                            classFormat: {0: "executing", 1: "fail", 2: "fail", 3: "ok"}}
                            ],
                    operations: [
                        {name: "restart2",  icon: {icon: "fa fa-repeat", title: l.forceComplete},action: forceComplete},
                    ],
                    onSelectPage: function (e) {
                        this.query({size: e});
                    },
                    onChangePageSize: function (e) {
                        this.query({page: e})
                    },
                    query: function f(e) {
                        let id = this.data.id;
                        let gridInfo = null;
                        for (let i = 0; i < t.options.subGrid.gridArray.length; i++) {
                            let gridTemp = t.options.subGrid.gridArray[i];
                            if (id == gridTemp.id) {
                                gridInfo = gridTemp;
                                break;
                            }
                        }
                        var a = {page: 1, size: $(".toolbar select.pagenum", gridInfo.gridDiv).val()};
                        e && $.extend(a, e);
                        $.extend(a, this.data);
                        gridInfo.gridObj.refresh(a)
                    }
                }
            }
        };
        t = o.datagrid(e)
        !function a() {
            var e = $("#addConfig").html(), n = {
                "{name}": l.configName,
                "{model}": l.type,
                "{file}": l.importFile,
                "{describ}": l.describ,
                "{uploadFile}": l.uploadFile
            };
            e = Frame.replaceTitle(e, n), (i = $(e)).find("#model").append(Frame.createModel()), $("#addConfig").empty().remove(), i.validateForm()
        }(),
        function company(){
            let comD = $("#companyDiv").html(), n = {"{company}": l.company,};
            comD = Frame.replaceTitle(comD, n),
            companyDiv = $(comD);
            Frame.setRequest({url: "account/company",type:"GET",loading:false,  success: function(data){
                if (200 == data.code){
                    let options = "";
                    let arr = data.data;
                    for (let j = 0; j < arr.length; j++) {
                        options += `<option value="${arr[j]}" >${arr[j]}</option>`;
                    }
                    companyDiv.find("#company").append(options);
                }

            }})
        }(),
        function password(){
            var t = $("#confirm-pass").html();
            t = Frame.replaceTitle(t, {"{againPass}":  l.passwd}), confirmPass = $(t)
        }()
    }

    Frame.destory = function () {
        t && t.destory(), l = o = companyDiv = confirmPass = i = null, Table = null
    }, $(function (e) {
        n()
    })
}();