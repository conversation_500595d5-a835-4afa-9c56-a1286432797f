li, ul {
    margin: 0;
    padding: 0;
    list-style: none
}

body {
    background: #f3f3f4
}

.btn {
    padding: 9px 12px;
    line-height: 20px
}

a {
    cursor: pointer
}

.navbar {
    width: 180px;
    background: #2a3240;
    color: #b8c6d6;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 90;
    height: 100%;
    border-radius: unset;
    min-height: 530px;
    bottom:0;
    overflow-y:scroll;
    overflow-x:hidden;
}

.logo {
    text-align: center
}

img {
    width: 108px;
    height: 60px
}

.loginfo {
    margin-top: 20px;
    margin-bottom: 30px;
    font-size: 14px;
    padding: 0 15px 10px 15px;
    border-bottom: 1px solid #b8c6d6;
    height: 30px
}

.username {
    cursor: pointer;
    max-width: 60px;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.loginfo ul.dropdown-menu {
    background: #2a3240;
    opacity: .9;
    min-width: 90px
}

.loginfo a {
    color: #b8c6d6
}

.dropdown-menu > li > a:hover, .loginfo .dropdown-menu > li > a:focus {
    background: #38404f;
    color: #fff
}

.list-group-item {
    color: #b8c6d6;
    border-left: 4px solid #2a3240;
    border-radius: 0
}

a.list-group-item:focus, a.list-group-item:hover {
    color: #b8c6d6;
    background: #222834
}

.list-group i {
    width: 16px;
    height: 14px;
    margin-right: 38px
}

.list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
    border-left: 4px solid #49a1ea
}

.logout, .navbar .download {
    width: 100%;
    text-align: center;
    /*position: absolute;
    bottom: 15px;*/
    font-size: 30px
}

.navbar .download {
    /*bottom: 75px;*/
    font-size: 16px
}

.logout a, .navbar .download a {
    color: #b8c6d6
}

.content {
    padding: 20px 15px 20px 195px
}

div.container {
    width: 100%;
    background: #fff;
    padding: 15px
}

.table {
    width: 98%
}

.table thead th {
    background-color: #e8e8f2
}

tr.empty {
    text-align: center
}

.toolbar {
    width: 98%;
    height: 50px
}

.page-info {
    float: left;
    height: 40px;
    line-height: 40px
}

nav .pagination {
    margin: 0
}

select.pagenum {
    padding: 2px;
    border-radius: 4px
}

.toolbar .form-control {
    display: inline-block;
    width: 40%;
    margin-right: 5px
}

.toolbar .filter {
    margin-left: -15px
}

.abnormal, .normal {
    display: inline-block
}

.abnormal b {
    color: #172b4d;
    margin-right: 3px
}

.abnormal span {
    background: #dfe1e6;
    color: #42526e
}

.normal b {
    color: #52c41a;
    margin-right: 3px
}

.normal span {
    color: #064;
    background: #e3fcef
}

td {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    -icab-text-overflow: ellipsis;
    -khtml-text-overflow: ellipsis;
    -moz-text-overflow: ellipsis;
    -webkit-text-overflow: ellipsis
}

td a {
    margin-right: 5px;
    margin-left: 5px
}

td.ok {
    color: #52c41a;
    background: #e3fcef
}

td.fail {
    color: #f5222d;
    background: #ffebe6
}

td.waiting {
    color: #0052cc;
    background: #e0ecfe
}

td.executing {
    color: #fa8c16;
    background: #fff5cf
}

td.executing i, td.fail i, td.ok i, td.waiting i {
    float: right
}

tr span.fa {
    cursor: pointer
}

.panel .close {
    float: right;
    cursor: pointer
}

.detail-popover {
    position: absolute;
    left: 195px;
    top: 10px;
    right: 15px;
    bottom: 10px;
    z-index: 1020;
    padding: 10px;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    white-space: normal;
    background-color: #fff;
    background-clip: padding-box;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
    line-break: auto;
    visibility: hidden
}

.panel .input-group {
    width: 20%;
    margin-bottom: 10px
}

.panel .row {
    margin-bottom: 20px
}

label.required:after {
    padding-left: 5px;
    color: #cb2431;
    content: "*"
}

.information div.info {
    margin-bottom: 15px
}

.form-group i.warning, .form-group span.error {
    display: inline-block;
    height: 34px;
    line-height: 34px;
    color: #a94442
}

.form-group i.warning {
    color: #8a6d3b;
    margin-right: 5px
}

.noticejs {
    position: fixed;
    z-index: 1030;
    width: 320px;
    top: 5%;
    right: -320px;
    opacity: 0
}

.noticejs .success {
    background-color: #64ce83
}

.noticejs .error {
    background-color: #f54444
}

.noticejs .item {
    margin: 0 0 10px;
    border-radius: 3px;
    overflow: hidden
}

.noticejs .item .close {
    float: right;
    font-size: 18px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-shadow: 0 1px 0 #fff;
    opacity: 1;
    margin-right: 7px
}

.noticejs .noticejs-body {
    color: #fff;
    padding: 10px
}

.noticejs .noticejs-progressbar {
    width: 100%;
    background-color: #64ce83;
    margin-top: -1px
}

.noticejs-progressbar .noticejs-bar {
    width: 100%;
    height: 5px;
    background: #3da95c
}

.error .noticejs-progressbar {
    background-color: #f54444
}

.error .noticejs-bar {
    background-color: #c32903
}

.loading .modal-dialog {
    top: 20%;
    left: 20%;
    font-size: 75px;
    color: #fff
}

.modal-body {
    overflow-y: auto
}

button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0
}

.close {
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .2
}

table table {
    border: 1px solid #ddd
}