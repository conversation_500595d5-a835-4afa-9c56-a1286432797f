! function (i) {
    function n(e, t) {
        var r = Frame.INFO.error,
            o = {
                type: "POST",
                cache: !1,
                async: !0,
                loading: !0,
                dataType: "json",
                onSuccess: function () {},
                onError: function () {},
                success: function (e) {
                    o.loading && Frame.Msg.loading(!1);
                    var n = e.code;
                    if (200 == n) {
                        "get" != t && Frame.Msg.info(), o.onSuccess(e.data);
                    } else if (1007 == n) {
                        (Frame.Msg.error(r[n + ""]), window.location.href = "login.html")
                    } else if (r[n + ""]) {
                        if (e.rightMsg != null && e.rightMsg != undefined ) {
                            Frame.Msg.error(r[n + ""] + e.rightMsg);
                        } else {
                            Frame.Msg.error(r[n + ""]);
                        }
                    } else {
                        Frame.Msg.error(e.message)
                    };
                        /*function a(e) {
                            // 200 == n ? ("get" != t && Frame.Msg.info(), o.onSuccess(e.data)) : 1007 == n ? (Frame.Msg.error(r[n + ""]), window.location.href = "login.html") : r[n + ""] ? Frame.Msg.error(r[n + ""]) : Frame.Msg.error(e.message)
                        }(e)*/
                },
                error: function (e, n, a) {
                    switch (o.loading && Frame.Msg.loading(!1), e.status) {
                        case 404:
                            Frame.Msg.error(r.notFound);
                            break;
                        case 403:
                            Frame.Msg.error(r.forbidden);
                            break;
                        case 302:
                            window.location.href = "login.html";
                            break;
                        default:
                            Frame.Msg.error(n)
                    }
                }
            };
        i.extend(o, e);
        if (o.loading) {
            Frame.Msg.loading(!0);
        }
        if ("form" == t ) {
            o.el.ajaxSubmit(o)
        } else {
            i.ajax(o)
        }
        //o.loading && Frame.Msg.loading(!0), "form" == t ? o.el.ajaxSubmit(o) : i.ajax(o)
    }
    var e = {
        formRequest: function a(e) {
            n(e, "form")
        },
        setRequest: function t(e) {
            n(e, "set")
        },
        getRequest: function r(e) {
            e.loading = !1, n(e, "get")
        },
        changeLang: function o(e, n) {
            var a = n.closest(".dropdown-menu").siblings("a.dropdown-toggle");
            "cn" == e ? a.find("span").text("中文") : a.find("span").text("English"), window.Cookie.setCookie("lang", e || "cn"), n.closest(".dropdown-menu").hide(), window.location.reload()
        },
        debounce: function s(a, t) {
            var r;
            return function (e) {
                var n = this;
                clearTimeout(r), r = setTimeout(function () {
                    a.call(n, e)
                }, t)
            }
        },
        throttle: function u(t, r) {
            var o, i;
            return function (e) {
                var n = this,
                    a = +new Date;
                o && a < o + r ? (clearTimeout(i), i = setTimeout(function () {
                    t.call(n, e)
                }, r)) : (o = a, t.call(n, e))
            }
        },
        randomPass: function $(e, n) {
            e = e || 8, n = n || 16;
            var a = "1234567890",
                t = "abcdefghijklmnopqrstuvwxyz",
                r = "ABCDEFGHJKLMNPQRSTUVWXYZ",
                o = "$@$!%*?&_",
                i = a + t + r + o,
                s = F(a),
                u = F(t),
                c = F(r),
                d = F(o),
                l = new RegExp(s + "|" + u + "|" + c + "|\\" + d, "g");
            i = i.replace(l, "");
            for (var f = h(8, 16), m = s + u + c + d, v = 4; v < f; v++) {
                var p = F(i);
                i = i.replace(p, ""), m += p
            }

            function h(e, n) {
                return Math.floor(Math.max(e, Math.random() * (n + 1)))
            }

            function F(e) {
                return e[h(0, e.length - 1)]
            }
            return function g(e) {
                for (var n, a, t = e.length; t; n = parseInt(Math.random() * t), a = e[--t], e[t] = e[n], e[n] = a);
                return e
            }(m.split("")).join("")
        },
        createModel: function c(e) {
            var b = null;
            $.ajax({
                url:"dictionary/dic?key=GATEWAY_MODEL",
                type:"get",
                async: false,
                success:function(data){
                    for (var n = data, a = e ? "<option value='all'>" + Frame.INFO.device.allType + "</option>" : "", t = 0; t < n.length; t++) a += "<option value='" + n[t].dicContent + "'>" + n[t].dicContent + "</option>";
                    b = a;
                }
            })
            return b;
        },
        createDictSelect: function c(e,key) {
            var b = null;
            $.ajax({
                url:"dictionary/dic?key="+key,
                type:"get",
                async: false,
                success:function(data){
                    for (var n = data, a = e ? "<option value='all'>" + Frame.INFO.text.unselected + "</option>" : "", t = 0; t < n.length; t++) a += "<option value='" + n[t].dicContent + "'>" + n[t].dicContent + "</option>";
                    b = a;
                }
            })
            return b;
        },
        replaceTitle: function d(e, n) {
            for (var a in n) e = e.replace(a, n[a]);
            return e
        },
        getFormData: function l(e) {
            var n = e.find("input:not([type='file']),select"),
                t = {};
            return i.each(n, function (e, n) {
                if (i(n).is(":hidden") || i(n).is(":disabled")) return !0;
                var a = i(n).prop("id");
                a && (t[a] = i(n).val())
            }), t
        },
        setFormData: function f(e, n) {
            if (n)
                for (var a in n) i("#" + a, e).val(n[a]);
            else {
                var t = e.find("input");
                i.each(t, function (e, n) {
                    i(n).val("")
                })
            }
        },
        getPrivilege: function m(e) {
            if (!e) return !0;
            var n = Frame.Default.role;
            return -1 != e.indexOf(n)
        }
    };
    window.Frame ? i.extend(window.Frame, e) : window.Frame = e;

}(jQuery);
// menu
!function (e) {
    var n = window.Cookie.getCookie("lang") || "cn",
        a = Frame.INFO.menu,
        t = {
            dashboard: {
                name: a.dashboard,
                path: "dashboard/dashboard.html",
                icon: "fa fa-television",
                role: "0,1,2"
            },
            kanban: {
                name: a.kanban,
                path: "kanban.html",
                icon: "fa fa-television",
                role: "0,1,2"
            },
            userStatistics: {
                name: a.userStatistics,
                path: "userStatistics/users.html",
                icon: "fa fa-users",
                role: "0,1"
            },
            device: {
                name: a.device,
                path: "device/device.html",
                icon: "fa fa-television",
                role: "0,1,2"
            },
            record: {
                name: a.record,
                path: "record/record.html",
                icon: "fa fa-cloud-upload",
                role: "0"
            },
            alarm: {
                name: a.alarm,
                path: "alarm/alarm.html",
                icon: "fa fa-television",
                role: "0,1,2"
            },
            account: {
                name: a.account,
                path: "account/account.html",
                icon: "fa fa-users",
                role: "0,1"
            },
            profile: {
                name: a.profile,
                path: "config/config.html",
                icon: "fa fa-file-text-o",
                role: "0,1"
            },
            firmware: {
                name: a.firmware,
                path: "firmware/firmware.html",
                icon: "fa fa-diamond",
                role: "0,1"
            },
            driver: {
                name: a.driver,
                path: "driver/driver.html",
                icon: "fa fa-diamond",
                role: "0,1"
            },
            point: {
                name: a.point,
                path: "point/point.html",
                icon: "fa fa-diamond",
                role: "0,1"
            },
            flow: {
                name: a.flow,
                path: "flow/flow.html",
                icon: "fa fa-diamond",
                role: "0,1"
            },
            syslog: {
                name: a.syslog,
                path: "syslog/syslog.html",
                icon: "fa fa-flask",
                role: "0,1,2"
            },
            license: {
                name: a.license,
                path: "license/license.html",
                icon: "fa fa-users",
                role: "0"
            },
            setting: {
                name: a.editpass,
                path: "personal/editPass.html",
                icon: "fa fa-key",
                role: "0,1,2"
            },
            sys: {
                name: a.sysedit,
                path: "sys/sys.html",
                icon: "fa fa-users",
                role: "0,1"
            },
            batchConfig: {
                name: a.batchConfig,
                path: "batchConfig/batchConfig.html",
                icon: "fa fa-flask",
                role: "0,1"
            },
            devicelog: {
                name: a.devicelog,
                path: "syslog/devicelog.html",
                icon: "fa fa-flask",
                role: "0,1"
            },
            remote: {
                name: a.remote,
                path: "remote/remote.html",
                icon: "fa fa-television",
                role: "0"
            },
            headscale: {
                name: a.headscale,
                path: "headscale/headscale.html",
                icon: "fa fa-shield",
                role: "0"
            }
        };
    e.Default = {
        home: ["dashboard", "dashboard", "dashboard"],
        menu: t,
        lang: n,
        role: 2,
        user: {}
    }
}(Frame);
// prompt
!function (i) {
    var e = {
            info: function r(e) {
                t(e = e || n.success, "info", 1e3)
            },
            confirm: function o(e) {
                if (!Frame.Util.isNotBlank(e.title)) {
                    e.title = n.hint
                }
                return  a(e, "confirm")
            },
            error: function s(e) {
                t(e = e || n.error, "error", 2e3)
            },
            prompt: function u(e) {
                return a($.extend({validate: !0}, e), "prompt")
            },
            loading: function c(e) {
                e ? $("body").append(function n() {
                    var e = $('<div class="modal loading fade"  tabindex="-1" role="dialog" aria-hidden="true"><div class="modal-dialog"><span class="fa fa-spinner fa-pulse"></span></div></div>');
                    return e.modal({
                        backdrop: "static"
                    }), e
                }()) : ($("body .modal.loading").modal("hide"), $(".modal-backdrop").remove(), $("body .modal.loading").on("hidden.bs.modal", function (e) {
                    $("body .modal.loading").remove()
                }))
            }
        },
        n = i.INFO.msg;

    function a(a, t) {
        var e = '<div class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">' +
                    '<div class="modal-dialog">' +
                        '<div class="modal-content" >' +
                            '<div class="modal-header">' +
                                '<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>' +
                                '<h4 class="modal-title">' + a.title + '</h4>' +
                            '</div>' +
                            '<div class="modal-body"></div>' +
                            '<div class="modal-footer">' +
                                '<button type="button" class="cancel btn btn-default" data-dismiss="modal">' + n.cancel + '</button>' +
                                '<button type="button" class="confirm btn btn-primary">' + n.ok + "</button>" +
                            "</div>" +
                        "</div>" +
                "</div>" +
            "</div>";
        var r = $(e);
        if (a.width != undefined && a.width != null){
            r.find("div[class='modal-content']").width(a.width);
        }
        if ( a.maxHeight != undefined && a.maxHeight != null){
            r.find("div[class='modal-body']").css("max-height",a.maxHeight);
        }
        return r.on("keydown", function (e) {
                if (13 == e.keyCode) return !1
            }), $("body").append(r),
            function o() {
                a.onConfirm = a.onConfirm || function () {};
                var e = window.innerHeight - 180;
                $(".modal-body", r).css("max-height", e + "px"), $(".modal-body", r).html(a.content), r.modal({
                    backdrop: "static"
                });
                var n = a.content;
                "prompt" == t && (a.validate && n.validateForm(), i.setFormData(n, a.data)), $(".modal-footer .btn.confirm", r).on("click", function () {
                    return "prompt" == t && a.validate && !i.Validate(n, "form") || a.closeByHand ? a.onConfirm(r) : (a.onConfirm(), r.modal("hide")), !1
                }), r.on("hidden.bs.modal", function (e) {
                    "prompt" == t && i.Validate(n, "clear"), $(".modal-body", r).empty(), $(".modal-footer .btn.confirm", r).off("click"), r.remove()
                })
            }(), r
    }

    function t(e, n, a) {
        var t = "success";
        "error" == n && (t = "error");
        var r = $('<div class="noticejs"><div class="item"><div class="close">×</div><div class="noticejs-body"><div class="noticejs-content">' + e + '</div></div><div class="noticejs-progressbar"><div class="noticejs-bar"></div></div></div></div>');

        function o() {
            $(".noticejs").animate({
                right: "-320px",
                opacity: 0
            }, 1e3), setTimeout(i, 1e3 + a)
        }

        function i() {
            r.remove()
        }
        r.find(".close").on("click", function () {
                o(), clearTimeout(s), s = null
            }), r.find(".item").addClass(t), $("body").append(r),
            function u() {
                $(".noticejs").animate({
                    right: "10%",
                    opacity: 1
                }, 1e3)
            }();
        var s = setTimeout(o, 1e3 + a);
        return r
    }
    i.Msg = e
}(Frame);
// date
!function (e) {
    function a(e) {
        return (e = e.toString())[1] ? e : "0" + e
    }

    function t(e, n) {
        var a = ["Y", "M", "D", "h", "m", "s"];
        for (var t in n = n || "Y-M-D h:m:s", e) n = n.replace(a[t], e[t]);
        return n
    }

    function r(e) {
        var n = [];
        return n.push(e.getFullYear()), n.push(a(e.getMonth() + 1)), n.push(a(e.getDate())), n.push(a(e.getHours())), n.push(a(e.getMinutes())), n.push(a(e.getSeconds())), n
    }
    Frame.Time = {
        formatTime: function o(e, n) {
            return e ? t(r(new Date(parseInt(e))), n) : ""
        },
        formatNumber: a,
        getDay: function i(e, n) {
            n = n || "Y-M-D h:m:s";
            var a = new Date;
            return a.setDate(a.getDate() + e), t(r(a), n)
        },
        getMonthFirstDay: function (date){
            let year = date.getFullYear();
            let month = date.getMonth();
            return new Date(year,month,1);
        },
        getNextMonthFirstDay: function (date){
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            return new Date(year,month,1);
        },
        formatDate: function formatDate(date, fmt) {
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
            }
            let o = {
                'M+': date.getMonth() + 1,
                'd+': date.getDate(),
                'h+': date.getHours(),
                'm+': date.getMinutes(),
                's+': date.getSeconds()
            };
            for (let k in o) {
                if (new RegExp(`(${k})`).test(fmt)) {
                    let str = o[k] + '';
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : Frame.Time.padLeftZero(str));
                }
            }
            return fmt;
        },
        padLeftZero: function padLeftZero(str) {
            return ('00' + str).substr(str.length);
        },
        getDaysBetween: function (dateString1,dateString2){
            let  startDate = Date.parse(dateString1);
            let  endDate = Date.parse(dateString2);
            let days=(endDate - startDate)/(1*24*60*60*1000);
            return  days;
        },
        getDateDiff: function (startTime, endTime, diffType) {
            var sTime = new Date(startTime);      //开始时间
            var eTime = new Date(endTime);  //结束时间

            if (startTime instanceof String && endTime instanceof String) {
                //将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式
                startTime = startTime.replace(/\-/g, "/");
                endTime = endTime.replace(/\-/g, "/");
                sTime = new Date(startTime);      //开始时间
                eTime = new Date(endTime);  //结束时间
            } else if (startTime instanceof Date && endTime instanceof Date) {
                sTime = startTime;      //开始时间
                eTime = endTime;  //结束时间
            }
            //将计算间隔类性字符转换为小写
            diffType = diffType.toLowerCase();

            //作为除数的数字
            var divNum = 1;
            switch (diffType) {
                case "second":
                    divNum = 1000;
                    break;
                case "minute":
                    divNum = 1000 * 60;
                    break;
                case "hour":
                    divNum = 1000 * 3600;
                    break;
                case "day":
                    divNum = 1000 * 3600 * 24;
                    break;
                default:
                    break;
            }
            return parseInt((eTime.getTime() - sTime.getTime()) / parseInt(divNum));
        },
        timeInterval: function (stime, etime) {
            // 两个时间戳相差的毫秒数
            let usedTime = etime - stime;
            // 计算相差的天数
            let days = Math.floor(usedTime / (24 * 3600000 ));
            // 计算天数后剩余的毫秒数
            let leave1 = usedTime % (24 * 3600000 );
            // 计算出小时数
            let hours = Math.floor(leave1 / (60 * 60 * 1000));
            // 计算小时数后剩余的毫秒数
            let leave2 = leave1 % 3600000;
            // 计算相差分钟数
            let minutes = Math.floor(leave2 / (60 * 1000));
            // 计算分钟数剩余的毫秒数
            let leave3 = leave2 % (60 * 1000);
            // 计算相差秒数
            let second = Math.floor(leave3/1000);
            let time = days + 'd'+ hours + 'H' + minutes + 'm' + second + "s";
            return time;
        }
    }
}();
// validate
!function (n) {
    var p = {
            number: function t(e) {
                return a(/^[1-9]+[0-9]*]*$/, e)
            },
            email: function o(e) {
                return a(/^([a-zA-Z\d])(\w|\-)+@[a-zA-Z\d]+\.[a-zA-Z]{2,4}$/, e)
            },
            en: function i(e) {
                return ! function n(e) {
                    return a(/[\u2E80-\u2EFF\u2F00-\u2FDF\u3000-\u303F\u31C0-\u31EF\u3200-\u32FF\u3300-\u33FF\u3400-\u4DBF\u4DC0-\u4DFF\u4E00-\u9FBF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF]+/g, e)
                }(e)
            },
            ip: function s(e) {
                return a(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/, e)
            },
            password: function u(e) {
                return !!(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d$@$!%*?&_]{8,16}$/.test(e) || /^(?=.*[a-z])(?=.*[A-Z])(?=.*[$@$!%*?&_])[A-Za-z\d$@$!%*?&_]{8,16}$/.test(e) || /^(?=.*[$@$!%*?&_])(?=.*[A-Z])(?=.*\d)[A-Za-z\d$@$!%*?&_]{8,16}$/.test(e) || /^(?=.*[a-z])(?=.*[$@$!%*?&_])(?=.*\d)[A-Za-z\d$@$!%*?&_]{8,16}$/.test(e))
            }
        },
        h = n.INFO.validate;

    function a(e, n) {
        return e.test(n)
    }

    function F(e, n) {
        var a = e.next("span"),
            t = e.parent(),
            r = t.siblings("span");
        if (n === undefined) return t.removeClass("has-success has-error"), a.removeClass("fa-check-circle fa-times-circle"), void r.empty().removeClass("error");
        t.addClass("has-feedback"), 0 == a.length && (a = $('<span class="form-control-feedback"></span>'), e.after(a)), 0 == r.length && (r = $("<span></span>"), e.closest(".form-group").append(r)), "" == n ? (t.addClass("has-success").removeClass("has-error"), a.addClass("fa fa-check-circle").removeClass("fa-times-circle"), r.empty().removeClass("error")) : (t.addClass("has-error").removeClass("has-success"), a.addClass("fa fa-times-circle").removeClass("fa-check-circle"), r.html(n).addClass("error"))
    }
    var r = function (e) {
        return !! function v(e) {
            var n = e.val(),
                a = e.attr("type");
            if (e.attr("nocheck") || e.is(":disabled") || e.attr("readonly")) return !0;
            if ("file" == a) {
                var t = e.attr("accept");
                if (!n) return F(e, h.file), !1;
                var r = n.split("\\");
                if (t) {
                    var o = r[r.length - 1].split("."),
                        i = o[o.length - 1] || "";
                    if (!(i = i.toLowerCase()) || -1 == t.indexOf(i)) return F(e, h.format), !1
                }
                var s = e[0].size;
                return e.attr("size") > s ? (F(e, h.size.replace("{size}", s)), !1) : 0 != e.attr("size") || (F(e, h.empty), !1)
            }
            if (e.hasClass("required")) {
                if ("" == n) {
                    var u = h.required;
                    return "number" == a && (u = h.number), F(e, h.required), !1
                }
            } else if ("" == n) return F(e, ""), !0;
            var c = e.attr("min"),
                d = e.attr("max");
            if (c && d && (parseInt(n) > d || parseInt(n) < c)) return F(e, u = (u = (u = h.range).replace("{min}", c)).replace("{max}", d)), !1;
            var l = e.attr("maxlength");
            if (l && function m(e) {
                    return e.replace(/[\u2E80-\u2EFF\u2F00-\u2FDF\u3000-\u303F\u31C0-\u31EF\u3200-\u32FF\u3300-\u33FF\u3400-\u4DBF\u4DC0-\u4DFF\u4E00-\u9FBF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF]/g, "**").length
                }(n) > l) return F(e, u = h.maxlength.replace("{maxlength}", l)), !1;
            for (var f in p)
                if (u = h[f] || h["default"], e.hasClass(f) && !p[f](n)) return F(e, u), !1;
            return F(e, ""), !0
        }(e)
    };
    n.Validate = function (e, n) {
        return "form" == n ? (a = e.find("input"), t = 0, $.each(a, function (e, n) {
            if (!r($(n))) return t++, !1
        }),
        selectArr = e.find("select"),
        $.each(selectArr, function (e, n) {
            if (!r($(n))) return t++, !1
        }),
        0 == t) : "input" == n ? r(e) : "clear" == n ? function (e) {
            e.resetForm();
            var n = e.find("input");
            return $.each(n, function (e, n) {
                F($(n))
            }), !0
        }(e) : void 0;
        var a, t
    }, $.fn.setError = function (e) {
        F($(this), e)
    }, $.fn.validate = function () {
        var e = $(this);
        $(this).on("focusout", function () {
            return n.Validate(e, "input"), !1
        })
    }, $.fn.validateForm = function () {
        $(this).find("input").on("focusout", function () {
            return n.Validate($(this), "input"), !1
        })
    }
}(Frame);
// util
!function (n) {
    Frame.Util = {
        isNotBlank: function (obj){
            if (obj == undefined || obj == null || (typeof(obj)=='string' && obj.trim().length == 0)){
                return false;
            }
            return true;
        },
        checkChinese: function (){ // 是否包含中文
            var reg = new RegExp("[\\u4E00-\\u9FFF]+","g");
            for (let i = 0; i < arguments.length; i++) {
                let val = arguments[i];
                if(reg.test(val)){
                    return true;
                }
            }
            return false;
        },
        isDottedIPv4: function (s){ // 是否是合法IP
            var match = s.match(/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/);
            return match != null &&
                match[1] <= 255 && match[2] <= 255 &&
                match[3] <= 255 && match[4] <= 255;
        },
        checkdomain: function (value){
            var doname = /^([\w-]+\.)+((com)|(net)|(org)|(gov\.cn)|(info)|(cc)|(com\.cn)|(net\.cn)|(org\.cn)|(name)|(biz)|(tv)|(cn)|(mobi)|(name)|(sh)|(ac)|   (io)|(tw)|(com\.tw)|(hk)|(com\.hk)|(ws)|(travel)|(us)|(tm)|(la)|(me\.uk)|(org\.uk)|(ltd\.uk)|(plc\.uk)|(in)|(eu)|(it)|(jp))$/;
            return doname.test(value);
        },
        inputNum: function (value,min,max) {
            if(value.length==1){
                value = value.replace(/[^1-9]/g,'')
            } else {
                value=value.replace(/\D/g,'')
            };
            if (!Frame.Util.isNotBlank(value)) {
            } else if(Frame.Util.isNotBlank(min) && value < min) {
                value=min;
            } else if(Frame.Util.isNotBlank(max) && value > max) {
                value = max
            }
            return value;
        },
        downloadFile: function (url, data, method){ // 获得url和data
            if( url && data ){
                // data 是 string 或者 array/object
                data = typeof data == 'string' ? data : jQuery.param(data); // 把参数组装成 form的 input
                var inputs = '';
                jQuery.each(data.split('&'), function(){
                    var pair = this.split('=');
                    inputs+='<input type="hidden" name="'+ pair[0] +'" value="'+ pair[1] +'" />';
                }); // request发送请求
                jQuery('<form action="'+ url +'" method="'+ (method||'post') +'">'+inputs+'</form>').appendTo('body').submit().remove();
            };
        }
    }
}(Frame,jQuery);
// 公司字典
!function (Frame){
    Frame.companyDict = {
        companyFlow:{
            dictType:"companyFlow",
            startTime:{dictLabel:"流量计算起始时间"}
        },
    }
}(Frame);