!function (d) {
    var e = Frame.INFO.icon;
    let s = {
        edit: {icon: "fa fa-edit", title: e.edit},
        remove: {icon: "fa fa-trash", title: e.remove},
        upload: {icon: "fa fa-upload", title: e.upload},
        download: {icon: "fa fa-download", title: e.download},
        add: {icon: "fa fa-plus", title: e.add},
        restart: {icon: "fa fa-repeat", title: e.restart},
        refresh: {icon: "fa fa-refresh", title: e.refresh},
        mqtt: {icon: "fa fa-plus", title: e.mqtt},
        delSubGrid: {icon: "fa fa-remove", title: ""},
        network: {icon: "", title: e.network},
        onOffLine: {icon: "", title: e.onOffLine},
        reset: {icon: "fa fa-repeat", title: e.reset},
        portMappingAdd: {icon: "", title: e.portMappingAdd},
        portMappingDel: {icon: "", title: e.portMappingDel},
    };
    let c = Frame.INFO.table;
    let respData = null;
    let tbodyJq = null;
    let gridObj = null;
    let gridFun = function (t, n) {
        var i = jQuery('<table class="table"></table>')
        let e = jQuery("<thead></thead>")
        let o = jQuery("<tbody></tbody>");
        tbodyJq = o;
        let r = jQuery('<div class="toolbar"></div>');
        this.options = t;
        this.init = function () {
            t.toolbar && this.createTool(n);
            this.createThead(e);
            i.append(e);
            i.append(o);
            this.loadData(t.data);
            n.append(i);
            return this
        };
        this.loadData = function (e) {
            var a = this;
            t.url && Frame.getRequest(
                {
                    url: t.url,
                    data: e,
                    type: t.type,
                    onSuccess: function (e) {
                        respData = e;
                        i.find("input:checkbox").prop("checked", !1);
                        a.createTbody(o, e);
                        r.empty();
                        if (t.pagination) {
                            r.pagination({
                                total: e.total,
                                pageSize: e.size,
                                pageNumber: e.page,
                                pageList: (t.pagination.pageList == undefined ? undefined : t.pagination.pageList),
                                onSelectPage: function (e) {
                                    return t.onSelectPage(e), !1
                                },
                                onChangePageSize: function (e) {
                                    return t.onChangePageSize(e), !1
                                }
                            });
                            n.append(r)
                        }
                        if (t.onComplete) {
                            t.onComplete(e)
                        }
                    }
                })
        };
        this.refresh = function (e) {
            this.loadData(e)
        };
        this.destory = function () {
            e.remove(), o.remove(), r.remove(), i.remove(), n.empty()
        };
        this.getSelectedData = function () {
            var e = o.find("input[type='checkbox']:checked"), n = [];
            return d.each(e, function (e, a) {
                var t = d(a).closest("tr").data("data");
                n.push(t)
            }), n
        }
        this.orderBy = function (param){
            let field = param.data.field;
            let type = param.data.type;
            let respTemp = respData;
            let tbodyTemp = tbodyJq;
            tbodyTemp.empty();
            if (type == 'up') {
                respTemp.records = respTemp.records.sort(function (obj1,obj2){
                    let f1 = obj1[field];
                    let f2 = obj2[field];
                    if (f1 == null && f2 == null) {
                        return 0;
                    } else if (f1 == null || f1 < f2) {
                        return -1;
                    } else if (f2 == null || f1 > f2) {
                        return 1
                    } else {
                        return 0;
                    }
                })
            } else {
                respTemp.records = respTemp.records.sort(function (obj1,obj2){
                    let f1 = obj1[field];
                    let f2 = obj2[field];
                    if (f1 == null && f2 == null) {
                        return 0;
                    } else if (f1 == null || f1 < f2) {
                        return 1;
                    } else if (f2 == null || f1 > f2) {
                        return -1
                    } else {
                        return 0;
                    }
                })
            };
            gridObj.createTbody(tbodyTemp,respTemp)
        }

    };
    gridFun.prototype = {
        createTool: function (e) {
            var a = jQuery('<div class="toolbar"></div>'), t = this.options.toolbar.search,
                n = this.options.toolbar.operations;
            t && this.createFilter(a, t), n && this.createOperation(a, n), e.append(a)
        },
        createFilter: function (e, a) {
            var t = jQuery('<div class="filter col-sm-6"></div>');
            for (key in a) {
                var n = a[key];
                if (!n) return;
                if (Frame.getPrivilege(n.role)) if ("input" == n.name) {
                    var i ;
                    if (Frame.Util.isNotBlank(n.name2)) {
                        i = jQuery(`<input type="text" name="${n.name2}"  class="form-control" />`);
                    } else {
                        i = jQuery('<input type="text"  class="form-control" />');
                    }
                    if (i.attr("placeholder", n.placeholder || ""), n.action) {
                        var o = Frame.debounce(function () {
                            n.action()
                        }, 500);
                        i.on("keyup", o)
                    }
                    i.placeholder(), t.append(i)
                } else {
                    var r = jQuery('<select class="form-control"></select>'), s = n.content;
                    r.on("change", function () {
                        n.action()
                    }), r.append(s), t.append(r)
                }
            }
            e.append(t)
        },
        createOperation: function (e, a) {
            for (var t = jQuery('<div class="btn-group pull-right" role="group" aria-label=""></div>'), n = 0; n < a.length; n++) {
                var i = this.makeButton(a[n], "button");
                i && t.append(i)
            }
            e.append(t)
        },
        createThead: function (e) {
            var a = jQuery("<tr></tr>"), t = this.options.columns;
            this.options.multiSelect && ((o = jQuery('<th width="38px"><input type="checkbox" /></th>')).find("input").on("click", this.checkAll), a.append(o));
            this.options.subGrid && a.append('<th class="detail" width="30px"></th>');
            for (let n = 0; n < t.length; n++) {
                var i = t[n];
                if (!i) return;
                if (!i.role || Frame.getPrivilege(i.role)) {
                    var o = jQuery("<th>" + i.title + "</th>");
                    i.width && o.attr("width", i.width + "px");
                    if (i.orderBy) {
                        let down = jQuery("<i class='fa fa-caret-down' ></i>");
                        let up = jQuery("<i class='fa fa-caret-up' ></i>");
                        down.bind("click",{"type":"down",field:i.name},this.orderBy);
                        up.bind("click",{"type":"up",field:i.name},this.orderBy);
                        o.append(down)
                            .append(up);
                    }
                    a.append(o);
                } else this.options.columns.splice(n, 1), n--
            }
            this.options.operations && a.append(jQuery('<th width="90px">' + c.operation + "</th>")), e.append(a)
        },
        createTbody: function (e, a) {
            var t;
            if (e.empty(), (t = a instanceof Array ? a : a.records || []) && 0 != t.length) for (var n = 0; n < t.length; n++) this.appendRow(t[n], e); else this.appendRow(null, e)
        }, appendRow: function (n, e) {
            var i = jQuery("<tr></tr>"), a = this.options.multiSelect, o = this.options.operations;
            if (a) {
                var t = jQuery('<td><input type="checkbox" /></td>');
                t.find("input").on("click", this.checkSingle), i.append(t)
            }
            if (!n) {
                var r = this.options.columns.length;
                return r = this.options.operations ? ++r : r, i.append('<td colspan="' + r + '"><i class="fa fa-folder-open-o"></i>' + c.empty + "</td>"), i.addClass("empty"), void e.append(i)
            }
            if (i.data("data", n), this.options.subGrid) {
                var s = this.options.subGrid;
                if ((s.format || function () {
                    return !0
                })(n)) {
                    i.append('<td><span class="fa fa-plus"></span></td>'), i.addClass("collapse"), i.show();
                    var l = this.options.columns.length;
                    i.find("td:first-child span").off("click").on("click", function () {
                        var e = d(this).closest("tr");
                        if (e.hasClass("collapse")) {
                            d(this).removeClass("fa-plus").addClass("fa fa-minus"), e.removeClass("collapse").addClass("expand");
                            var a = d('<tr class="sub-table"><td></td></tr>');
                            t = d('<td colspan="' + l + '"></td>');
                            s.options.data = {id: n.id};
                            let gridObj = t.datagrid(s.options);
                            a.append(t);
                            if (s.gridArray instanceof  Array) {
                                s.gridArray.push({id:n.id,gridObj,"gridDiv":t});
                            }
                            o && t.after("<td></td>"), i.after(a);
                        } else {
                            d(this).addClass("fa fa-plus").removeClass("fa-minus"), e.addClass("collapse").removeClass("expand"), e.next("tr.sub-table").empty().remove();
                            if (s.gridArray instanceof  Array) {
                                for (let j = 0; j < s.gridArray.length; j++) {
                                    if (s.gridArray[j].id == n.id){
                                        s.gridArray.splice(j,1);
                                        break;
                                    }
                                }
                            }
                        }
                    })
                } else i.append("<td><span></span></td>")
            }
            for (var p = 0; p < this.options.columns.length; p++) this.appendCell(n, this.options.columns[p], i);
            o && this.appendOperation(i), e.append(i)
        }, appendCell: function (e, a, t) {
            var n = null;
            if (a.name.indexOf(".") > 0) {
                let attrStr = a.name;
                let attrArr = attrStr.split(".");
                n = e[attrArr[0]] == null ? null : e[attrArr[0]][attrArr[1]];
            } else {
                n = e[a.name];
            }
            null == n && (n = "");
            var i = jQuery("<td></td>");
            a.width && i.attr("width", a.width + "px");
            a.classFormat && i.addClass(a.classFormat[n] || "");
            a.format ? (n = a.format(n), i.html(n)) : i.text(n);
            a.action && a.action(i), t.append(i)
        }, appendOperation: function (e) {
            for (var a = jQuery("<td></td>"), t = 0; t < this.options.operations.length; t++) {
                var n = this.options.operations[t], i = e.data("data");
                if (!n.fShow || n.fShow(i)) {
                    var o = this.makeButton(n, null, i);
                    o && a.append(o)
                }
            }
            a.find("a:not(:last-child)").after("|"), e.append(a)
        }, makeButton: function (e, a, t) {
            if (!Frame.getPrivilege(e.role)) return !1;
            var n, i = s[e.name] || e.icon;
            if (i) {
                var o = e.title || i.title || "";
                n = jQuery("<a></a>");
                var r = i.icon;
                n.addClass(r), n.attr("title", o), "button" == a && (n.attr("type", "button"), n.addClass("btn btn-info"), n.html(o))
            } else e.format && (n = jQuery(e.format()));
            return e.action && n.on("click", function () {
                return e.action(t || "", this), !1
            }), n
        }, checkAll: function () {
            var e = d(this).closest("table"), a = d(this).prop("checked");
            e.find("tbody>tr>td>input[type='checkbox']").prop("checked", a)
        }, checkSingle: function () {
            var e = d(this).closest("table"), a = (d(this).prop("checked"), e.find("thead input[type='checkbox']")),
                t = e.find("tbody>tr>td>input[type='checkbox']");
            e.find("tbody>tr>td>input[type='checkbox']:checked").length == t.length ? a.prop("checked", !0) : a.prop("checked", !1)
        }
    };
    d.fn.datagrid = function (e) {
        var a;
        d(this).data("datagrid");
        a = d.extend({}, d.fn.datagrid.defaults, e);
        Frame.getPrivilege(a.role) || (delete a.operations, a.toolbar && delete a.toolbar.operations);
        let temp = d(this);
        var t = new gridFun(a,temp);
        t.init();
        gridObj = t;
        return  t
    };
    d.fn.datagrid.defaults = {url: null, role: "0,1", loadMsg: "Processing, please wait ..."}
}(jQuery),
function (c) {
    var s = Frame.INFO.table;

    function a(e, a) {
        for (var t = c('<select class="pagenum"></select>'), n = e.pageList, i = 0, o = ""; i < n.length; i++) o += '<option value="' + n[i] + '">' + n[i] + "</option>";
        t.append(o + s.perPage), t.val(e.pageSize), a.append(t), function r(e, a) {
            var t = s.pageInfo;
            t = (t = (t = t.replace(/{from}/, e.pageSize * (e.pageNumber - 1) + 1)).replace(/{to}/, Math.min(e.pageSize * e.pageNumber, e.total))).replace(/{total}/, e.total), a.append('<span class="pageinfo">' + t + "</span>")
        }(e, a), function d(i, e) {
            var a = jQuery('<nav aria-label="Page navigation" class="pull-right"></nav>'),
                o = jQuery('<ul class="pagination"></ul>'),
                t = jQuery('<li><a href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>'),
                n = jQuery('<li><a href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>');
            o.append(t);
            var r = Math.ceil(i.total / i.pageSize), s = i.pageNumber;
            r <= 7 ? p(1, r) : s <= 4 ? (p(1, 5), l(), p(r, r)) : r - 3 <= s ? (p(1, 1), l(), p(r - 4, r)) : (p(1, 1), l(), p(s - 1, s + 1), l(), p(r, r));

            function l() {
                var e = jQuery('<li class="disabled"><a href="#">...</a></li>');
                o.append(e), e.on("click", function () {
                    return !1
                })
            }

            function p(e, a) {
                for (var t = e; t <= a; t++) {
                    var n = jQuery('<li><a href="#">' + t + "</a></li>");
                    t == i.pageNumber && n.addClass("active"), function (e) {
                        n.on("click", function () {
                            return i.onChangePageSize(e), !1
                        })
                    }(t), o.append(n)
                }
            }

            o.append(n), a.append(o), e.append(a), 1 == i.pageNumber ? t.addClass("disabled") : t.removeClass("disabled");
            i.pageNumber == r ? n.addClass("disabled") : n.removeClass("disabled");
            i.total || (t.addClass("disabled"), n.addClass("disabled"));
            t.on("click", function (e) {
                return c(this).hasClass("disabled") || c(this).hasClass("active") || i.onChangePageSize(--i.pageNumber), !1
            }), n.on("click", function (e) {
                return c(this).hasClass("disabled") || c(this).hasClass("active") || i.onChangePageSize(++i.pageNumber), !1
            })
        }(e, a), t.on("change", function () {
            return e.onSelectPage(c(this).val()), !1
        })
    }

    c.fn.pagination = function (e) {
        e = e || {};
        a(c.extend({}, c.fn.pagination.defaults, e), c(this))
    };
    c.fn.pagination.defaults = {
        total: 1,
        pageSize: 20,
        pageNumber: 1,
        pageList: [10, 20, 40, 60, 80, 100],
        onSelectPage: function (e, a) {
        },
        onChangePageSize: function (e) {
        }
    }
}(jQuery);