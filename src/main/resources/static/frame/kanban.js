! function (echarts) {
    $("#kanban").height(document.documentElement.clientHeight);
    let intervalArr = [];
    let myChart = null;
    let maxBarWidth = 0;
    let deviceText = Frame.INFO.device;
    let visitorId = null;

    function initModelTable() {
        let header = [
            {text:"型号",field:"model"},
            {text:"数量",field:"num"},
        ];
        $.ajax({
            url: "device/deviceModel",
            type: "post",
            success: function (data) {
                let allNum = 0;
                let onlineNum = 0;
                let offlineNum = 0;
                if (data.code = 200) {
                    let arr = data.data.list;
                    allNum = data.data.allNum;
                    onlineNum = data.data.onlineNum;
                    offlineNum = data.data.offlineNum;
                    appendTable($("#modelTable")[0],header,arr);

                } else {
                    appendTable($("#modelTable")[0],header,[]);
                }
                $("#allNum").html("合计："+allNum);
                $("#onlineNum").html("在线："+onlineNum);
                $("#offlineNum").html("离线："+offlineNum);
            }
        })
    }

    function setFont (p) {
        var whdef = 16 / 1920; // 表示1920的设计图,使用16PX的默认值
        var bodyWidth = document.body.clientWidth; // 当前窗口的宽度
        var rem = bodyWidth * whdef; // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应FONT-SIZE值
        return p / 16 * rem
    }

    function initFlowChart() {
        let dateList = [];
        let downList = [];
        let upList = [];
        $.ajax({
            url: "device/flow/flowStatisticsOfDashboard",
            type: "post",
            async:false,
            success: function (data) {
                if (data.code = 200) {
                    dateList = data.data.dateList;
                    downList = data.data.down;
                    upList = data.data.up;
                }
            }
        })
        myChart = echarts.init(document.getElementById('flowChart'));
        var option  = {
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '4%',
                top: '16%',
                right: '4%',
                bottom: '2%',
                containLabel: true
            },
            legend: {
                data: ['上行(M)', '下行(M)'],
                top: '0%',
                textStyle:{
                    color:"#ffffff",
                    fontSize: setFont(12)
                }
            },
            color: ["#ff0000",'#00FF00'],
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dateList,
                axisLine:{
                    lineStyle:{
                        color:"#ffffff",
                    }
                },
                axisLabel: {
                    fontSize: setFont(12)
                }
            },
            yAxis: {
                axisLine:{
                    lineStyle:{
                        color:"#ffffff",
                    }
                },
                axisLabel: {
                    fontSize: setFont(12)
                }
            },
            series: [
                {
                    name: '上行(M)',
                    type: 'line',
                    stack: 'Total',
                    data: upList,
                },
                {
                    name: '下行(M)',
                    type: 'line',
                    stack: 'Total',
                    data: downList,
                    itemStyle : {
                        normal : {
                            lineStyle:{
                                color:'#00FF00'
                            }
                        }
                    },
                }
            ]
        };
        myChart.clear();
        myChart.setOption(option)
    }

    function initDeviceInfoTable() {
        let header = [
            {text:deviceText.alias,field:"alias"},
            {text:deviceText.position,field:"position"},
            {text:"IP",field:"trueIp",style:"width:7rem"},
            {text:"信号强度",field:"signal5g"},
            {text:"设备状态",field:"status",format: function (row){
                    return row.status == 0 ? "正常" : "报警";
                }},
        ];
        let offlineHeader = [
            {text:"离线时间",field:"lastHbTime",format:function (row){
                return Frame.Time.formatDate(new Date(row.lastHbTime),"yyyy-MM-dd hh:mm");
            }},
            {text:"别名",field:"alias"},
            {text:deviceText.position,field:"position"},
            {text:"IP",field:"trueIp",style:"width:7rem"},
            {text:"持续时长",field:"lastHbTime",format:function (row){
                    return Frame.Time.timeInterval(new Date(row.lastHbTime),new Date());
                }},
        ];
        let onlineTable = [];
        let offlineTable = [];
        let deviceNum = 0;
        let onlineNum = 0;
        let onlineRate = "0%";
        $.ajax({
            url: "device/onlineClass",
            type: "post",
            success: function (data) {
                if (data.code = 200) {
                    let dataMap = data.data;
                    offlineTable = dataMap.offline;
                    onlineTable = dataMap.online;
                    onlineNum = dataMap.onlineNum;
                    deviceNum = dataMap.allNum;
                    appendTable($("#deviceTable")[0],header,onlineTable);
                    appendTable($("#offlineDeviceTable")[0],offlineHeader,offlineTable);
                    if (deviceNum == 0) {
                        onlineRate = "0%";
                    } else {
                        let s =  onlineNum / deviceNum * 100;
                        s =  Math.floor(s * 100) / 100;
                        onlineRate = s+"%";
                    }
                } else {
                    onlineTable = [];
                    offlineTable = [];
                    appendTable($("#deviceTable")[0],header,onlineTable);
                    appendTable($("#offlineDeviceTable")[0],offlineHeader,offlineTable);
                }
                $("#online").html(onlineTable.length);
                $("#offline").html(offlineTable.length);
                var a = parseInt(onlineNum % 10); // 个位数
                var b = parseInt((onlineNum % 100) / 10);  // 十位数
                var c = parseInt((onlineNum % 1000) / 100); // 百位数
                var d = parseInt((onlineNum % 10000) / 1000); // 千位数
                var e = parseInt((onlineNum % 10000) / 1000); // 万位数
                $("#numSpan1").html(e);
                $("#numSpan2").html(d);
                $("#numSpan3").html(c);
                $("#numSpan4").html(b);
                $("#numSpan5").html(a);
                $("#numSpan6").html(onlineRate);
            }
        })

    }

    function initAlarm(){
        let header = [
            {
               text: "别名",
               field: "acsUserDevice.alias",
               format: function(row) {
               if (row && row["acsUserDevice"] && row["acsUserDevice"]["alias"]) {
                     return row["acsUserDevice"]["alias"];
               } else {
                     return ""; // 若属性不存在，则返回一个默认值或空字符串
                 }
               }
            },
            {text:"类别",field:"parameterContent"},
            {text:"当前值",field:"currentValue"},
            {text:"参考值",field:"parameterValue"},
            {text:"状态",field:"alarmStatus", format: function (row){
                    return Frame.INFO.alarm.statusObj[row.alarmStatus];
                }},
        ];
        maxBarWidth = maxBarWidth == 0 ? $("#cpuBar").width() : maxBarWidth;
        let cpuAlarm = 0;
        let memoryAlarm = 0;
        let temperatureAlarm = 0;
        let rssiAlarm = 0; // 信号强度
        let sinrAlarm = 0; // 信噪比  信号质量
        let todayAlarm = 0;
        let today = Frame.Time.formatDate(new Date(),"yyyy-MM-dd");
        $.ajax({
            url: `alarm/kanban`,
            type: "post",
            data:{start:today,"alarmStatus":null},
            success: function (data) {
                if (data.code = 200) {
                    let tableArr = data.data.latest;
                    cpuAlarm = data.data.CpuRation;
                    memoryAlarm = data.data.ram;
                    temperatureAlarm = data.data.DevTemp;
                    rssiAlarm = data.data.rssi;
                    sinrAlarm = data.data.SignalQuality;
                    todayAlarm =  data.data.todayNum;
                    appendTable($("#alarmTable")[0],header,tableArr);
                }

                $("#cpuSpan").html(cpuAlarm+"条");
                $("#memorySpan").html(memoryAlarm+"条");
                $("#signalQuaSpan").html(sinrAlarm+"条");
                $("#signalStrengthSpan").html(rssiAlarm+"条");
                $("#DeviceTempSpan").html(temperatureAlarm+"条");
                $("#alarmNum").html(todayAlarm+"条");

                let maxAlarm = cpuAlarm;
                maxAlarm = maxAlarm >= memoryAlarm ? maxAlarm : memoryAlarm;
                maxAlarm = maxAlarm >= sinrAlarm ? maxAlarm : sinrAlarm;
                maxAlarm = maxAlarm >= rssiAlarm ? maxAlarm : rssiAlarm;
                maxAlarm = maxAlarm >= temperatureAlarm ? maxAlarm : temperatureAlarm;
                $("#cpuBar").width(maxBarWidth * cpuAlarm/maxAlarm);
                $("#memoryBar").width(maxBarWidth * memoryAlarm/maxAlarm);
                $("#signalQuaBar").width(maxBarWidth * sinrAlarm/maxAlarm);
                $("#signalStrengthBar").width(maxBarWidth * rssiAlarm/maxAlarm);
                $("#DeviceTempBar").width(maxBarWidth * temperatureAlarm/maxAlarm);

            }
        })
    }

    function initFlowAlarm() {
        let header = [
            {text:"别名",field:"alias"},
            {text:"IP",field:"ip"},
            {text:"套餐流量(M)",field:"flow"},
            {text:"当月流量(M)",field:"currentFlow"},
        ]
        $.ajax({
            url: "device/flow/flowEarlyWarn",
            type: "post",
            success: function (data) {
                if (data.code = 200) {
                    let arr = data.data;
                    appendTable($("#flowTable")[0],header,arr);
                } else {
                    appendTable($("#flowTable")[0],header,[]);
                }
            }
        })
    }

    function appendTable(document,head,data) {
        let tableHead = document.id+"Head";
        let table2 = document.id +"2";
        $("#"+tableHead).empty();
        $(document).empty();
        $("#"+table2).empty();
        let $thTemp = $("<thead></thead>");
        for( let i = 0; i < head.length; i++ ) {
            //动态创建一个tr行标签,并且转换成jQuery对象
            //+"<th>"+ head[i].text +"</th>"
            $thTemp.append(`<th style="${head[i].style == null ? "" : head[i].style}">${head[i].text}</th>`);
        }
        $thTemp.appendTo("#"+tableHead);

        let $tbTemp = $("<tbody></tbody>");
        for( let i = 0; i < data.length; i++ ) {
            //动态创建一个tr行标签,并且转换成jQuery对象
            var $trTemp = $("<tr></tr>");
            let row = data[i];
            for (let j = 0; j < head.length; j++) {
                //往行里面追加 td单元格
                let field = head[j].field;
                let value = "";
                if (head[j].format != null && head[j].format != undefined) {
                    value = head[j].format(row);
                } else {
                    value = row[field];
                }
                value = value == null ? "" : value;
                $trTemp.append(`<td style="${head[j].style == null ? "" : head[j].style}">${value}</td>`);
            }
            $trTemp.appendTo($tbTemp);
        }
        $tbTemp.appendTo(document);
    }

    function initTitle() {
        const fpPromise = import('./plugin/fingerprintJS.js').then(FingerprintJS => FingerprintJS.load());
        fpPromise
            .then(fp => fp.get())
            .then(result => {
                visitorId = result.visitorId
                $.ajax({
                    url: "device/flow/query/kanbanTitle",
                    data:{visitorId:visitorId},
                    type: "post",
                    success: function (data) {
                        if (data.code = 200 && data.data != null) {
                            $("#titleInput").val(data.data.title);
                        }
                    }
                })
            })
    }

    function init() {
        initTitle();
        initModelTable();
        initDeviceInfoTable();
        setTimeout(function (){
            // 等待屏幕绘制完成，确定chartDiv宽高，在初始化。
            initFlowChart();
            initAlarm();
            tableScroll('alarmTableParent','alarmTable','alarmTable2');
        },1000)
        initFlowAlarm();

        for (let i = 0; i < intervalArr.length; i++) {
            clearInterval(intervalArr[i]);
        }
        tableScroll('modelTableParent','modelTable','modelTable2');
        tableScroll('deviceParent','deviceTable','deviceTable2');
        tableScroll('offlineDeviceParent','offlineDeviceTable','offlineDeviceTable2');
        tableScroll('flowTableParent','flowTable','flowTable2');
    }


    function tableScroll(parentId,table1Id,table2Id) {
        setTimeout(function (){
            let parent = document.getElementById(parentId);
            let child1 =  document.getElementById(table1Id);
            let child2 =  document.getElementById(table2Id);
            let height1 = $(parent).height();
            let height2 = $(child1).height();
            if (height1 >= height2){
                return ;
            }
            child2.innerHTML = child1.innerHTML;
            let interval1 = setInterval(function () {
                if(parent.scrollTop >= child1.scrollHeight) {
                    parent.scrollTop = 0;
                } else {
                    parent.scrollTop++;
                }
            }, 300);
            intervalArr.push(interval1)
        },1000);
    }

    init();

    let interval = setInterval(init,60 * 1000)


    $("#titleInput").on("blur",function () {
        let val = $("#titleInput").val();
        $.ajax({
            url: "device/flow/save/kanbanTitle",
            type: "post",
            data:{"visitorId":visitorId,"title":val},
            success: function (data) {
                if (data.code = 200) {
                }
            }
        })
    })
    Frame.destory = function () {
        for (let i = 0; i < intervalArr; i++) {
            clearInterval(intervalArr[i]);
        }
        clearInterval(interval);
        intervalArr = null;
    };
}(echarts);







