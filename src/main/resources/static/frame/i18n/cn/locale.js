Frame.INFO = {
    menu: {
        kanban: "大屏看板",
        dashboard: "设备看板",
        userStatistics: "管理看板",
        device: "设备管理",
        record: "录入设备",
        alarm: "报警管理",
        account: "账号管理",
        profile: "配置管理",
        firmware: "固件管理",
        driver: "驱动管理",
        point: "设备点表",
        syslog: "系统日志",
        personal: "个人信息",
        license: "授权管理",
        editpass: "密码管理",
        sysedit: "系统管理",
        devicelog: "设备日志",
        remote: "远程控制",
        headscale: "Headscale管理",
        home: "首页",
        flow: "流量统计",
        batchConfig: "批量配置",
    },
    table: {
        operation: "操作",
        empty: "暂无数据",
        perPage: "条/页，",
        pageInfo: "此页显示{from} - {to}，共{total}条"
    },
    msg: {
        ok: "确认",
        cancel: "取消",
        hint: "提示",
        success: "操作成功",
        error: "操作失败",
        confirmDelete:"确认删除？",
        deleteDateTips: "请选择删除时间区间",
        deleteDateWarn: "不可以删除最近30天的数据",
        radioUser: "只能选择一个用户",
        selectDevice: "请选择设备",
    },
    icon: {
        edit: "编辑",
        remove: "删除",
        upload: "导入",
        download: "导出",
        refresh: "刷新",
        add: "添加",
        restart: "重启",
        mqtt: "MQTT",
        network: "网络诊断",
        onOffLine: "上下线",
        reset: "重置",
        portMappingAdd: "向网关新增",
        portMappingDel: "从网关删除",
    },
    time: {
        hour: "时",
        minute: "分",
        second: "秒",
        day: "天",
        hours: "时",
        minutes: "分",
        seconds: "秒",
        days: "天"
    },
    alarm:{
        alarmRank:"等级",
        parameterValue:"参数值",
        currentValue:"当前值",
        alarmStatus:"状态",
        parameterKey:"型号",
        parameterContent:"类别",
        insertTime:"时间",
        company:"公司",
        ip:"IP",
        sn: "序列号",
        edit: "编辑",
        rankObj:{1:"警告",2:"次要",3:"主要",3:"严重"},
        statusObj:{1:"激活",2:"确认",3:"清除"},
        alias: "别名",
        position: "位置",
    },
    sysedit:{
        dict:{
            type:"类型",
            key:"键",
            value:"值",
        },
        params:{
            company :"公司",
            parameterKey :"设备型号",
            parameterContent :"参数内容",
            parameterCode :"参数代码",
            parameterValue :"值（最小值）",
            parameterValueMax :"最大值",
            parameterOperator :"参数运算符",
            insertTime :"时间",
            addWarn: "管理员账号不可以增加报警参数",
            operatorEnum:[
                //{key:0,value:0,content:"其中需一个符合要求"},
                //{key:1,value:1,content:"所有参数均需满足"},
                {key:2,value:2,content:"实际值大于参数值异常"},
                {key:3,value:3,content:"实际值小于参数值异常"},
                {key:4,value:4,content:"实际值等于参数值，异常"},
                {key:5,value:5,content:"实际值不等于参数值，异常"},
                {key:6,value:6,content:"实际值不在范围内，异常"},
            ]
        }
    },
    device: {
        devname: "网关名称",
        type: "型号",
        pwd:'密码',
        sn: "序列号",
        subnet: "子网地址",
        vip: "虚拟IP",
        status: "在线状态",
        version: "软件版本",
        company: "所属公司",
        username: "所属用户",
        name: "名称",
        companyinfo: "厂家信息",
        describ: "描述",
        offline: "离线",
        online: "在线",
        uploadLog: "同步日志",
        downloadLog: "下载日志",
        onlineTime: "在线时长",
        hversion: "硬件版本",
        ip: "SIM卡IP",
        signal5G: "信号强度RSRP",
        signalNum: "移动通信号码",
        gps: "地址",
        allType: "所有型号",
        addDevice: "添加设备",
        removeDevice: "是否要删除该设备？",
        configName: "配置名称",
        configId: "配置id",
        firmwareName: "固件名称",
        gmtCreate: "创建时间",
        uploadConfig: "批量配置",
        uploadFirm: "批量升级",
        importFile: "导入文件",
        share: "分享",
        success: "成功",
        fail: "失败",
        download_fail: "下载失败",
        check_fail: "校验失败",
        timeout_upgrade: "升级超时",
        waiting: "等待",
        executing: "执行中",
        addConfig: "添加配置文件",
        removeConfig: "是否要删除该配置？",
        shareConfig: "是否要分享该配置？",
        addFirmware: "添加固件",
        removeFirmware: "是否要删除该固件？",
        downFlow: "下行",
        upFlow: "上行",
        uploadFile: "上传文件",
        reset : "恢复出厂设置",
        restart : "重启",
        passwd : "密码",
        downloadTemplate : "导出模板",
        allocateDevice:"分配设备",
        cannotDelete : "有设备未执行完成，无法删除",
        forceComplete : "强制完成",
        forceCompleteConfirm : "确定强制完成？",
        cellId : "小区号",
        temperature :"设备温度",
        imei : "ICCID",
        networkType :"网络类型",
        deviceStatus :"设备状态",
        normal :"正常",
        alarm :"报警",
        imsi :"IMSI",
        sinr :"信噪比SINR",
        delSubGrid :"删除记录",
        delConfigUpgradeWarn: "是否要删除该配置导入记录？",
        delFirmwareUpgradeWarn: "是否要删除该固件升级记录？",
        noRecord: "无记录",
        offLineFail: "设备离线，操作失败",
        alias: "别名",
        position: "位置描述",
        quality: "信号质量RSRQ",
        ram: "内存（剩余内存）",
        flow: "流量套餐(M)",
        villageNum: "物理小区号",
        column: "显示列",
        networkSetUp: "网关设置",
        pubBool: "是否有idPub",
        subBool: "是否有idSub",
        subTopicNum: "idSub主题数",
        topicList: "所有订阅主题",
        connectTimeList: "连接时间",
        advise: "修复建议",
        temperature: "温度",
        online:"上线",
        offline:"下线",
        uploadPoint:"上传点表",
        upgradeDriver:"升级驱动",
        point:"点表",
        downPoint:"下载点表",
        forceComplete:"强制完成",
        driverUpgradeStatus:"驱动升级状态",
        uptime: "开机时长",
        lanip: "LANIP",
        lockCellEnable: "启用锁小区",
        lock4GCellEnable: "启用锁4G小区",
        rsrq: "信号质量RSRQ",
        lanIp: "LAN口IP",
        lon: "经度",
        lat: "纬度",
    },
    users: {
        username: "用户名",
        password: "密码",
        company: "公司",
        lastLoginIp: "上次登录IP",
        lastLoginTime: "上次登录时间",
        lastLogoutTime: "上次登出时间",
        gmtCreate: "创建时间",
        status: "状态",
        del: "删除",
        allStatus: "所有状态",
        freeze: "冻结",
        normal: "正常",
        resetPass: "重置密码",
        recovery: "恢复",
        confirmPass: "新密码是%pass，是否要重置密码？",
        addUser: "添加用户",
        editUser: "编辑用户",
        mqttUser:"mqtt账户",
        removeUser: "是否要删除该用户？",
        recoveryUser: "是否要恢复该用户？",
        careMdf: "谨慎修改",
        careAdd: "谨慎添加",
        describ: "描述",
        vpnHost: "VPN主机",
        vpnPort: "VPN端口",
        email: "用户名即邮箱",
        content: "内容",
        time: "时间",
        repeatPasswd: "重复密码",
        oldPasswd: "旧密码",
        newPasswd: "新密码",
        systemManager: "系统管理员",
        accountManager: "客户管理员",
        customerOperator: "客户操作员",
        role: "角色",
        personalInfo: "个人信息",
        logout: "退出登录",
        download: "下载客户端",
        againPass: "请输入密码",
        rePass: "确认密码",
        editOk: "编辑密码成功，需重新登录。",
        mqttPassword: "mqtt密码",
        multilayer: "是否二层"
    },
    log: {
        content:"内容",
        num:"数量",
        addFile: "添加文件",
    },
    drive: {
        protocol: "协议",
    },
    validate: {
        repeat: "密码不一致，请重新输入",
        required: "不能为空",
        number: "请输入数字",
        email: "邮箱格式不正确",
        en: "不支持中文",
        ip: "IP格式不正确",
        password: "大写字母、小写字母、数字、特殊字符至少三种以上的8-16位字符",
        range: "请输入{min}~{max}的数字",
        maxlength: "不能超过{maxlength}个字符",
        size: "文件大小不能超过{size}字节",
        format: "文件格式不正确",
        file: "请选择文件",
        empty: "文件内容为空",
        "default": "格式不正确"
    },
    echarts: {
        title: "流量统计",
        line: "切换为折线图",
        bar: "切换为柱状图",
        saveImage: "保存为图片"
    },
    search: {
        username: "请输入用户名",
        company: "请输入公司名",
        configName: "请输入配置名称",
        firmwareName: "请输入固件名称",
        driveName: "请输入驱动名称",
        protocol: "请输入协议",
        repeatPasswd: "请输入重复密码",
        oldPasswd: "请输入旧密码",
        newPasswd: "请输入新密码",
        owners: "所属用户",
        dicKey: "请输入键名",
        sn: "请输入序列号",
        alias: "请输入别名",
        position: "请输入位置",
        trueIp: "请输入SIM卡IP",
        ICCID: "请输入ICCID",
        content: "请输入内容",
    },
    license: {
        expireDate: "过期时间"
    },
    error: {
        400: "非法参数",
        500: "服务器内部错误",
        600: "上传文件超过最大限制",
        1007: "会话已失效，请重新登录",
        1002: "密码错误",
        1001: "重试已超过最大次数，请联系管理员或24小时后重试",
        1003: "用户已存在",
        1004: "该公司已存在客户管理理员",
        1005: "操作员个数已达最⼤（10个）",
        1006: "用户不存在",
        1008: "删除用户失败",
        2e3: "设备已录入",
        2001: "文件格式不支持",
        2002: "文件解析出错",
        2003: "导入设备条数超过最大值",
        2004: "设备重复",
        2005: "设备不存在",
        2006: "设备已被其它客户激活",
        2007: "设备日志文件上传失败",
        2008: "设备日志文件下载失败",
        2009: "⼀次升级不能超过100台",
        2010: "设备认证失败",
        2011: "设备日志不存在",
        2012: "设备已在使用中",
        3e3: "⽂件校验失败",
        3001: "同名固件已存在",
        3002: "固件上传失败",
        3003: "固件不存在",
        4e3: "配置不存在",
        4001: "同名配置已存在",
        4002: "配置上传失败",
        4003: "配置下载失败",
        5001: "消息发送失败",
        6001: "文件不存在",
        notFound: "Web 服务器找不到您所请求的文件或脚本。请检查URL 以确保路径正确。",
        forbidden: "权限不足，禁止访问"
    },
    text: {
        company: "公司",
        name : "名字",
        file : "文件",
        passwd : "密码",
        gmtCreate: "创建时间",
        describ : "描述",
        upgrade : "升级",
        confirm : "确认",
        unselected : "未选择",
        table : "表格",
        sn : "序列号",
        monthFlow: "月流量(M)",
        date: "日期",
        updatDate: "更新时间",
        sourcePort: "源端口",
        destinationIP: "目的网络地址",
        destinationPort: "目的端口",
        add: "添加",
        edit: "编辑",
        protocol: "协议",
        model: "型号",
        alias: "别名",
    },
    editText:{
        add: "添加",
        edit: "编辑",
        remove: "确认删除？",
        editColumn:"编辑显示列"
    },
    queryText:{
        sn: "序列号",
        alias: "别名",
        position: "位置",
    }
};