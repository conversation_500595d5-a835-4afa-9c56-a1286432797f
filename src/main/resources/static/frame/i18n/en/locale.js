Frame.INFO = {
    menu: {
        kanban: "Kanban",
        dashboard: "Dashboard",
        userStatistics: "Management Kanban",
        device: "Device",
        record: "Record",
        alarm: "Alarm",
        account: "Account",
        profile: "Profile",
        firmware: "Firmware",
        drive: "Driver",
        point: "Point",
        syslog: "Syslog",
        personal: "Personal",
        license: "License",
        editpass: "Setting",
        sysedit:"Sysedit",
        devicelog: "Device Log",
        remote: "Remote",
        headscale: "Headscale Management",
        home: "Home",
        flow: "Monthly Flow",
        batchConfig: "Batch Config",
    },
    table: {
        operation: "Operation",
        empty: "Data is null",
        perPage: "Article/Page,",
        pageInfo: "This page shows {from}-{to} of {total}"
    },
    msg: {
        ok: "Submit",
        cancel: "Cancel",
        hint: "Tips",
        success: "Operate successfully",
        error: "Operation failure",
        confirmDelete:"Confirm delete？",
        deleteDateTips: "Please select the deletion time interval",
        deleteDateWarn: "Data of the last 30 days cannot be deleted",
        radioUser: "Only one user can be selected",
        selectDevice: "Please select a device",
    },
    icon: {
        edit: "Edit",
        remove: "Remove",
        upload: "Upload",
        download: "Download",
        refresh: "Refresh",
        add: "Add",
        restart: "Restart",
        mqtt: "MQTT",
        network: "Network diag",
        onOffLine: "On off line",
        reset: "Reset",
        portMappingAdd: "Let gateway do add",
        portMappingDel: "Let gateway do delete",
    },
    time: {
        hour: " hour",
        minute: " minute",
        second: " second",
        day: " day",
        hours: " hour",
        minutes: " minutes",
        seconds: " seconds",
        days: " days"
    },
    alarm:{
        alarmRank:"Rank",
        parameterValue:"Parameter value",
        currentValue:"Current value",
        alarmStatus:"Status",
        parameterKey:"Model",
        parameterContent:"Type",
        insertTime:"Time",
        company:"Company",
        ip: "IP",
        sn: "Serial",
        edit: "Edit",
        rankObj:{1:"alarm",2:"secondary",3:"main",3:"serious"},
        statusObj:{1:"activation",2:"confirm",3:"clean up"},
        alias: "Alias",
        alias: "Position",
    },
    sysedit:{
        dict:{
            type:"type",
            key:"key",
            value:"value",
        },
        params:{
            company :"Company",
            parameterKey :"Model",
            parameterContent :"Parameter Content",
            parameterCode :"Parameter Code",
            parameterValue :"Value（Minimum Value）",
            parameterValueMax :"Maximum",
            parameterOperator :"Parameter Operator",
            insertTime :"Time",
            addWarn : "The administrator cannot add alarm parameters",
            operatorEnum:[
                //{key:0,value:0,content:"One meets the requirements"},
                //{key:1,value:1,content:"All parameters shall meet"},
                {key:2,value:2,content:"Greater than parameter value"},
                {key:3,value:3,content:"Less than parameter value"},
                {key:4,value:4,content:"Equal to parameter value"},
                {key:5,value:5,content:"Not equal to parameter value"},
                {key:6,value:6,content:"Out of range"},
            ]
        }
    },
    device: {
        devname: "Name",
        type: "Model",
        pwd:'Password',
        sn: "Serial",
        subnet: "Subnet",
        vip: "Virtual IP",
        status: "Status",
        version: "Version",
        company: "Company",
        username: "Username",
        name: "Name",
        companyinfo: "Company",
        describ: "Describe",
        offline: "Offline",
        online: "Online",
        uploadLog: "synchronous log",
        downloadLog: "download log",
        onlineTime: "Online time",
        hversion: "Hardware version",
        ip: "IP address",
        signal5G: "5G signal strength(RSRP)",
        signalNum: "Mobile number",
        gps: "GPS",
        allType: "All Model",
        addDevice: "Add Device",
        removeDevice: "Do you want to delete the device?",
        configName: "Config Name",
        configId: "Config Id",
        firmwareName: "Firmware Name",
        gmtCreate: "Creation time",
        uploadConfig: "Batch configuration",
        uploadFirm: "Batch update",
        importFile: "Import File",
        share: "Share",
        success: "success",
        fail: "fail",
        download_fail: "Download failed",
        check_fail: "Check failure",
        timeout_upgrade: "Timeout upgrade",
        waiting: "waiting",
        executing: "executing",
        addConfig: "Add config",
        removeConfig: "Do you want to delete the Config?",
        shareConfig: "Do you want to share the Config?",
        addFirmware: "Add Firmware",
        removeFirmware: "Do you want to delete the Firmware?",
        downFlow: "down flow",
        upFlow: "up flow",
        uploadFile: "Upload File",
        reset : "Restore factory settings",
        restart : "Restart",
        passwd : "Password",
        downloadTemplate : "Download template",
        allocateDevice:"Allocate Device",
        cannotDelete : "There are devices that have not been completed and cannot be deleted！",
        forceComplete : "Force compelte",
        forceCompleteConfirm : "Do you want to force complete？",
        cellId : "Cell id",
        temperature :"Device temperature",
        imei : "IMEI",
        networkType :"Network type",
        deviceStatus :"Device status",
        normal :"Normal",
        alarm :"Alarm",
        imsi :"Imsi",
        sinr :"Signal intensity(SINR)",
        delSubGrid :"Delete record",
        delConfigUpgradeWarn: "Do you want to delete the configuration import record？",
        delFirmwareUpgradeWarn: "Do you want to delete this firmware upgrade record？",
        noRecord: "No record",
        offLineFail: "Device offline, operation failed",
        alias: "Alias",
        position: "Position",
        quality: "Signal quality(RSRQ)",
        ram: "Memory",
        flow: "Package traffic(M)",
        villageNum: "Village num",
        column: "Column",
        networkSetUp: "Gateway settings",
        pubBool: "Have idPub",
        subBool: "Have idSub",
        subTopicNum: "Number of idSub topic",
        topicList: "All topic",
        connectTimeList: "Connection time",
        advise: "Advise",
        temperature: "Temperature",
        online:"Online",
        offline:"Offline",
        uploadPoint:"Upload Point",
        upgradeDriver:"upgrade Driver",
        point:"Point Table",
        downPoint:"Download Point",
        forceComplete:"Forced Completion",
        driverUpgradeStatus:"Driver Upgrade Status",
        uptime: "Boot Time",
        lanip: "LANIP",
        lockCellEnable: "Enable Lock Cell",
        lock4GCellEnable: "Enable Lock 4G Cell",
        rsrq: "Signal Quality(RSRQ)",
        lanIp: "LAN Ip",
        lon: "Longitude",
        lat: "latitude",
    },
    users: {
        username: "UserName",
        password: "Password",
        company: "Company",
        lastLoginIp: "Last Login Ip",
        lastLoginTime: "Last Login Time",
        lastLogoutTime: "Last Logout Time",
        gmtCreate: "Creation time",
        status: "Status",
        del: "Delete",
        allStatus: "All Status",
        freeze: "Freeze",
        normal: "Normal",
        resetPass: "Reset Password",
        recovery: "Recovery",
        confirmPass: "The new password is %pass. Do you want to reset your password?",
        addUser: "Add User",
        editUser: "Edit User",
        mqttUser: "mqtt User",
        removeUser: "Do you want to delete the user?",
        recoveryUser: "Do you want to recovery the user?",
        careMdf: "Careful modification",
        careAdd: "Careful addition",
        describ: "Describ",
        vpnHost: "VPN Host",
        vpnPort: "VPN Port",
        email: "The username is the mailbox",
        content: "Content",
        time: "Time",
        repeatPasswd: "Repeat Passwd",
        oldPasswd: "Old Passwd",
        newPasswd: "New Passwd",
        systemManager: "System administrator",
        accountManager: "Account manager",
        customerOperator: "Customer operator",
        role: "Role",
        personalInfo: "Personal Infomation",
        logout: "Logout",
        download: "Download Client",
        againPass: "Please Enter Password",
        rePass: "Confirm Password",
        editOk: "Edit Password successfully, you need to login again.",
        mqttPassword: "Mqtt Password"
    },
    log: {
        content:"Content",
        num:"Number",
        addFile: "Add File",
    },
    drive: {
        protocol: "Protocol",
    },
    validate: {
        repeat: "The password is inconsistent, please re-enter it",
        required: "can not be empty",
        number: "Must be a number",
        email: "The email format is incorrect",
        en: "Chinese is not supported",
        ip: "The IP format is incorrect",
        password: "Capital letters, lowercase letters, Numbers, special characters at least three kinds of 8-16 bit characters",
        range: "Please enter the Numbers {min} to {max}",
        maxlength: "Cannot exceed {maxlength} characters",
        size: "The file size cannot exceed {size} bytes",
        format: "The file format is not correct",
        file: "Please select file",
        empty: "The file contents are empty",
        "default": "Incorrect format"
    },
    echarts: {
        title: "Traffic statistics",
        line: "switch to line",
        bar: "switch to bar",
        saveImage: "save as image"
    },
    search: {
        username: "Please enter user name",
        company: "Please enter company",
        configName: "Please enter config name",
        firmwareName: "Please enter firmware name",
        driveName: "请输入驱动名称",
        protocol: "请输入协议",
        repeatPasswd: "Please repeat password",
        oldPasswd: "Please enter old password",
        newPasswd: "Please enter new password",
        owners: "Please enter the owners",
        dicKey: "Please enter the key",
        sn: "Please enter serial",
        alias: "Please enter alias",
        position: "Please enter position",
        trueIp: "Please enter SIM IP",
        ICCID: "Please enter ICCID",
        content: "Please enter content",
    },
    license: {
        expireDate: "expire date"
    },
    error: {
        400: "Illegal parameter",
        500: "Server internal error",
        600: "File uploads exceed the maximum limit",
        1007: "The session has expired, please login again",
        1002: "Password mistake",
        1001: "The maximum number of retries has been exceeded, please contact the manager or try again after 24 hours",
        1003: "User already exists",
        1004: "The company already has a customer management manager",
        1005: "Longest number of operators (10)",
        1006: "User does not exist",
        1008: "Delete user failed",
        2e3: "The equipment has been recorded.",
        2001: "File format not supported",
        2002: "File parsing error",
        2003: "The number of imported devices exceeds the maximum",
        2004: "Equipment to repeat",
        2005: "The device does not exist",
        2006: "The device has been activated by another customer",
        2007: "Device log file upload failed",
        2008: "Device log file download failed",
        2009: "With the spirit of nature, one couldn't have more than 100",
        2010: "Equipment certification failed",
        2011: "The device log does not exist",
        2012: "The equipment is in use",
        3e3: "File check failed",
        3001: "The firmware with the same name already exists",
        3002: "Firmware upload failed",
        3003: "Firmware does not exist",
        4e3: "Configuration does not exist",
        4001: "The configuration with the same name already exists",
        4002: "Configuration upload failed",
        4003: "Configuration download failed",
        5001: "Message sending failed",
        6001: "File not exist",
        notFound: "The Web server could not find the file or script you requested. Please check the URL to make sure the path is correct.",
        forbidden: "Insufficient permissions to prohibit access"
    },
    text: {
        company: "Company",
        name : "Name",
        file : "File",
        passwd : "Password",
        gmtCreate: "Create Time",
        describ : "Describe",
        upgrade : "Upgrade",
        confirm : "Confirm",
        unselected : "Unselected",
        table : "Table",
        sn : "SN",
        monthFlow: "Monthly Flow(M)",
        date: "Date",
        updatDate: "Update Date",
        sourcePort: "Source Port",
        destinationIP: "Destination IP",
        destinationPort: "Destination Port",
        add: "Add",
        edit: "Edit",
        protocol: "Protocol",
        model: "Model",
        alias: "Alias",
    },
    editText:{
        add: "Add",
        edit: "Edit",
        remove: "Confirm deletion？",
        editColumn:"Edit column"
    },
    queryText:{
        sn: "Sn",
        alias: "Alias",
        position: "Position",
    }
};