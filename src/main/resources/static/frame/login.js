!function () {
    var a,licenseForm , s = {
        cn: {
            username: "用户名",
            password: "密码",
            login: "登录",
            nameErr: "用户名不能为空",
            passErr: "密码不能为空",
            loginErr: "用户名或密码错误，剩余%data次。",
            lock: "密码5次错误，账号已被锁，请联系管理员。",
            exist: "其他用户在用该系统，请稍后再试。",
            importFile: "导入文件",
            uploadFile: "上传",
            tips: "请选择文件",
            info: "请上传 license 文件",

        },
        en: {
            username: "UserName",
            password: "Password",
            login: "Login",
            nameErr: "Please input UserName",
            passErr: "Please input Password",
            loginErr: "UserName or Password error, remaining %data times.",
            lock: "Locked",
            exist: "Other users are using the system. Please try again later.",
            importFile: "import file",
            uploadFile: "upload",
            tips: "Please select a file",
            info: "Please upload license file",
        }
    };

    function e() {
        $(".dropdown-menu a").on("click", function () {
            return function a(e, n) {
                var r = n.closest(".dropdown-menu").siblings("a.dropdown-toggle");
                "cn" == e ? r.find("span").text("中文") : r.find("span").text("English");
                return function o(e, n) {
                    var r = new Date;
                    r.setTime(r.getTime() + 864e5), document.cookie = e + "=" + escape(n) + ";expires=" + r.toGMTString()
                }("lang", e || "cn"), n.closest(".dropdown-menu").hide(), void window.location.reload()
            }("English" == $(this).text() ? "en" : "cn", $(this)), !1
        }), $("#username").on("focusout", function () {
            if (!r($(this))) return !1
        }), $("#password").on("focusout", function () {
            if (!r($(this))) return !1
        }),$(".btn-login").on("click", function () {
            if (!r($("#username")) || !r($("#password"))) return !1;
            o($("#username,#password"), ""), function n() {
                var e = {username: $("#username").val(), password: md5($("#password").val())};
                $.ajax({
                    url: "/account/login",
                    loading: !1,
                    type: "POST",
                    data: e,
                    dataType: "json",
                    success: function (e) {
                        if (1100 == e.code) {
                            $('#myModal').modal();
                            return ;
                        }
                        if (200 == e.code) window.location.href = "index.html";
                        else if (1e3 == e.code) {
                            var n = s[a].loginErr.replace("%data", e.data);
                            o($("#username,#password"), n)
                        } else 1001 == e.code ? o($("#username,#password"), s[a].lock) : 1007 == e.code ? o($("#username,#password"), s[a].exist) : o($("#username,#password"), e.message || "error")
                    },
                    error: function (e, n, r) {
                        alert(n)
                    }
                })
            }()
        });
    }

    function o(e, n) {
        var r = e.closest("div.form-group"), o = $(".error-info"), a = r.find("span.form-control-feedback");
        if ("" == n) return r.removeClass("has-error"), a.removeClass("fa-close"), void o.empty().hide();
        r.addClass("has-error"), a.addClass("fa fa-close"), o.html(n).show()
    }

    function r(e) {
        var n = e.val(), r = "";
        return r = "username" == e.attr("id") ? s[a].nameErr : s[a].passErr, n ? (o(e, ""), !0) : (o(e, r), !1)
    }

    function n() {
        $("body").height(function () {
            return window.innerHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight)
        }), "en" == (a = function o(e) {
            var n, r = new RegExp("(^| )" + e + "=([^;]*)(;|$)");
            return (n = document.cookie.match(r)) ? unescape(n[2]) : null
        }("lang") || "cn") ? $(".dropdown-toggle>span").text("English") : $(".dropdown-toggle>a>span").text("中文"),
            $("#username").attr("placeholder", s[a].username),
            $("#password").attr("placeholder", s[a].password),
            $(".btn-login-wrap a").text(s[a].login),
            $(".btn-login-wrap a").text(s[a].login),
            e();
        let fileDiv = $("#licenseDiv").html();
        let text = {
            "{file}": s[a].importFile,
            "{uploadFile}": s[a].uploadFile,
            "{tips}": s[a].tips,
            "{info}": s[a].info,
        };
        for (var arrtribute in text) fileDiv = fileDiv.replace(arrtribute, text[arrtribute]);
        $("#licenseDiv").html(fileDiv);
        licenseForm = $(fileDiv);
    }

    window.onresize = function () {
        window.location.reload()
    }, $(function (e) {
        n()
    })
}();
function uploadLicenece () {
    var form = document.getElementById('licenseForm');
    var fileInput = $('#file').get(0).files[0];
    if (fileInput == undefined){
        $("#tips").show();
        return ;
    }else {
        $("#tips").hide();
    }
    formData = new FormData(form);
    $.ajax({
        url: "/register/importLicense",
        type: "post",
        data: formData,
        processData: false,
        contentType: false,
        success: function (res) {
            $('#myModal').modal("hide");
            $(".btn-login").click()
            if (res.code != 200) {
                alert(res.msg);
            }
        },
        error: function (err) {
            alert("网络连接失败,稍后重试", err);
        }
    })
}