<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsSysConfigMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsSysConfig">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="describ" jdbcType="VARCHAR" property="describ" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, content, describ, gmt_create, gmt_modify
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_sys_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_sys_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsSysConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_sys_config (`name`, content, describ, 
      gmt_create, gmt_modify)
    values (#{name,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}, #{describ,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsSysConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_sys_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="describ != null">
        describ,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="describ != null">
        #{describ,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsSysConfig">
    <!--@mbg.generated-->
    update acs_sys_config
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="describ != null">
        describ = #{describ,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsSysConfig">
    <!--@mbg.generated-->
    update acs_sys_config
    set `name` = #{name,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR},
      describ = #{describ,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="getByName" resultMap="BaseResultMap">
    select
       <include refid="Base_Column_List" />
    from acs_sys_config
    where name=#{name}
  </select>
  <select id="getAll" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from acs_sys_config
  </select>
</mapper>