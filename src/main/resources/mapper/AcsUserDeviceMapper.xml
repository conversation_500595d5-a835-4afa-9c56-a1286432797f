<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsUserDeviceMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsUserDevice">
        <!--@mbg.generated-->
        <!--@Table acs_user_device-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="device_passwd" jdbcType="VARCHAR" property="devicePasswd"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="last_hb_time" jdbcType="TIMESTAMP" property="lastHbTime"/>
        <result column="online_time" jdbcType="INTEGER" property="onlineTime"/>
        <result column="belong_to" jdbcType="INTEGER" property="belongTo"/>
        <result column="sub_ip" jdbcType="VARCHAR" property="subIp"/>
        <result column="dum_ip" jdbcType="VARCHAR" property="dumIp"/>
        <result column="true_ip" jdbcType="VARCHAR" property="trueIp"/>
        <result column="ver_no" jdbcType="VARCHAR" property="verNo"/>
        <result column="hard_ver_no" jdbcType="VARCHAR" property="hardVerNo"/>
        <result column="signal_num" jdbcType="VARCHAR" property="signalNum"/>
        <result column="signal_5g" jdbcType="VARCHAR" property="signal5g"/>
        <result column="gps" jdbcType="VARCHAR" property="gps"/>
        <result column="describ" jdbcType="VARCHAR" property="describ"/>
        <result column="base_dev_num" jdbcType="INTEGER" property="baseDevNum"/>
        <result column="base_dev" jdbcType="LONGVARCHAR" property="baseDev"/>
        <result column="upgrade_status" jdbcType="INTEGER" property="upgradeStatus"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="host_prefix" jdbcType="VARCHAR" property="hostPrefix"/>
        <result column="del" jdbcType="INTEGER" property="del"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify"/>
        <result column="imei" jdbcType="VARCHAR" property="imei"/>
        <result column="imsi" jdbcType="VARCHAR" property="imsi"/>
        <result column="rssi" jdbcType="VARCHAR" property="rssi"/>
        <result column="network_type" jdbcType="VARCHAR" property="networkType"/>
        <result column="temperature" jdbcType="VARCHAR" property="temperature"/>
        <result column="cell_id" jdbcType="VARCHAR" property="cellId"/>
        <result column="sinr" jdbcType="VARCHAR" property="sinr"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="flow" jdbcType="DOUBLE" property="flow"/>
        <result column="village_num" jdbcType="VARCHAR" property="villageNum"/>
        <result column="bind_ip" jdbcType="VARCHAR" property="bindIp"/>
        <result column="iccid" jdbcType="VARCHAR" property="iccid"/>
        <result column="login_date" jdbcType="TIMESTAMP" property="loginDate"/>
        <result column="signal_quality" jdbcType="VARCHAR" property="signalQuality"/>
        <result column="uptime" jdbcType="INTEGER" property="uptime"/>
        <result column="lock_cell_enable" jdbcType="VARCHAR" property="lockCellEnable"/>
        <result column="lan_ip" jdbcType="VARCHAR" property="lanIp"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="iface" jdbcType="VARCHAR" property="iface"/>
        <result column="upgrade_device" jdbcType="BOOLEAN" property="upgradeDevice"/>
        <association property="user" javaType="com.jetron.nb.dal.po.AcsUser">
            <!-- id:声明主键，表示 college_id 是关联查询对象的唯一标识-->
            <result property="company" column="company"/>
            <result property="name" column="username"/>
        </association>
        <association property="point" javaType="com.jetron.nb.dal.po.AcsPoint">
            <!-- id:声明主键，表示 college_id 是关联查询对象的唯一标识-->
            <result property="id" column="pointId"/>
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        a.id, a.`name`, a.sn, a.device_passwd, a.model, a.last_hb_time, a.online_time, a.belong_to, a.sub_ip,
        a.dum_ip, a.true_ip, a.ver_no, a.hard_ver_no, a.signal_num, a.signal_5g, a.gps, a.describ, a.base_dev_num,
        a.base_dev, a.upgrade_status, a.`path`, a.host_prefix, a.del, a.gmt_create, a.gmt_modify
        , a.imei, a.network_type, a.temperature, a.cell_id,a.status,a.imsi,a.rssi,a.sinr,a.alias,a.position,a.flow,a.village_num
        ,a.bind_ip,a.iccid,a.login_date,a.uptime,a.lock_cell_enable,a.lock_cell_4g_enable
        ,a.lan_ip, a.lon, a.lat, a.iface, a.upgrade_device
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from acs_user_device
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsUserDevice"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_user_device (`name`, sn, device_passwd,
        model, last_hb_time, online_time,
        belong_to, sub_ip, dum_ip,
        true_ip, ver_no, hard_ver_no,
        signal_num, signal_5g, gps,
        describ, base_dev_num, base_dev,
        upgrade_status, `path`, host_prefix,
        del, gmt_create, gmt_modify
        , cell_id, network_type, imei
        , temperature , status,imsi
        ,rssi,sinr,alias,position
        ,flow,village_num,bind_ip,iccid,login_date
        ,a.uptime,lock_cell_enable,lock_cell_4g_enable
        )
        values (#{name,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{devicePasswd,jdbcType=VARCHAR},
        #{model,jdbcType=VARCHAR}, #{lastHbTime,jdbcType=TIMESTAMP}, #{onlineTime,jdbcType=INTEGER},
        #{belongTo,jdbcType=INTEGER}, #{subIp,jdbcType=VARCHAR}, #{dumIp,jdbcType=VARCHAR},
        #{trueIp,jdbcType=VARCHAR}, #{verNo,jdbcType=VARCHAR}, #{hardVerNo,jdbcType=VARCHAR},
        #{signalNum,jdbcType=VARCHAR}, #{signal5g,jdbcType=VARCHAR}, #{gps,jdbcType=VARCHAR},
        #{describ,jdbcType=VARCHAR}, #{baseDevNum,jdbcType=INTEGER}, #{baseDev,jdbcType=LONGVARCHAR},
        #{upgradeStatus,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, #{hostPrefix,jdbcType=VARCHAR},
        #{del,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP}
        ,#{cellId,jdbcType=VARCHAR},#{networkType,jdbcType=VARCHAR},#{imei,jdbcType=VARCHAR}
        ,#{temperature,jdbcType=VARCHAR} ,#{status,jdbcType=INTEGER} ,#{imsi,jdbcType=VARCHAR}
        ,#{rssi,jdbcType=VARCHAR},#{sinr,jdbcType=VARCHAR},#{alias,jdbcType=VARCHAR},#{position,jdbcType=VARCHAR}
        ,#{position,jdbcType=DOUBLE},#{villageNum,jdbcType=VARCHAR},#{bindIp,jdbcType=VARCHAR}
        ,#{iccid,jdbcType=VARCHAR}, #{loginDate,jdbcType=TIMESTAMP}
        ,#{uptime,jdbcType=VARCHAR},#{lockCellEnable,jdbcType=VARCHAR}
        ,#{lock4GCellEnable,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsUserDevice"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_user_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="sn != null">
                sn,
            </if>
            <if test="devicePasswd != null">
                device_passwd,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="lastHbTime != null">
                last_hb_time,
            </if>
            <if test="onlineTime != null">
                online_time,
            </if>
            <if test="belongTo != null">
                belong_to,
            </if>
            <if test="subIp != null">
                sub_ip,
            </if>
            <if test="dumIp != null">
                dum_ip,
            </if>
            <if test="trueIp != null">
                true_ip,
            </if>
            <if test="verNo != null">
                ver_no,
            </if>
            <if test="hardVerNo != null">
                hard_ver_no,
            </if>
            <if test="signalNum != null">
                signal_num,
            </if>
            <if test="signal5g != null">
                signal_5g,
            </if>
            <if test="gps != null">
                gps,
            </if>
            <if test="describ != null">
                describ,
            </if>
            <if test="baseDevNum != null">
                base_dev_num,
            </if>
            <if test="baseDev != null">
                base_dev,
            </if>
            <if test="upgradeStatus != null">
                upgrade_status,
            </if>
            <if test="path != null">
                `path`,
            </if>
            <if test="hostPrefix != null">
                host_prefix,
            </if>
            <if test="del != null">
                del,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModify != null">
                gmt_modify,
            </if>
            <if test="cellId != null">
                cell_id,
            </if>
            <if test="imei != null">
                imei,
            </if>
            <if test="temperature != null">
                temperature,
            </if>
            <if test="networkType != null">
                network_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="imsi != null">
                imsi,
            </if>
            <if test="rssi != null">
                rssi,
            </if>
            <if test="sinr != null">
                sinr,
            </if>
            <if test="alias != null">
                alias,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="flow != null">
                flow,
            </if>
            <if test="villageNum != null">
                village_num,
            </if>
            <if test="bindIp != null">
                bind_ip,
            </if>
            <if test="iccid != null">
                iccid,
            </if>
            <if test="loginDate != null">
                login_date,
            </if>
            <if test="uptime != null">
                uptime,
            </if>
            <if test="lockCellEnable != null  and lockCellEnable != ''">
                lock_cell_enable,
            </if>
            <if test="lock4GCellEnable != null  and lock4GCellEnable != ''">
                lock_cell_4g_enable,
            </if>
            <if test="upgradeDevice != null">
                upgrade_device
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sn != null">
                #{sn,jdbcType=VARCHAR},
            </if>
            <if test="devicePasswd != null">
                #{devicePasswd,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="lastHbTime != null">
                #{lastHbTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=INTEGER},
            </if>
            <if test="belongTo != null">
                #{belongTo,jdbcType=INTEGER},
            </if>
            <if test="subIp != null">
                #{subIp,jdbcType=VARCHAR},
            </if>
            <if test="dumIp != null">
                #{dumIp,jdbcType=VARCHAR},
            </if>
            <if test="trueIp != null">
                #{trueIp,jdbcType=VARCHAR},
            </if>
            <if test="verNo != null">
                #{verNo,jdbcType=VARCHAR},
            </if>
            <if test="hardVerNo != null">
                #{hardVerNo,jdbcType=VARCHAR},
            </if>
            <if test="signalNum != null">
                #{signalNum,jdbcType=VARCHAR},
            </if>
            <if test="signal5g != null">
                #{signal5g,jdbcType=VARCHAR},
            </if>
            <if test="gps != null">
                #{gps,jdbcType=VARCHAR},
            </if>
            <if test="describ != null">
                #{describ,jdbcType=VARCHAR},
            </if>
            <if test="baseDevNum != null">
                #{baseDevNum,jdbcType=INTEGER},
            </if>
            <if test="baseDev != null">
                #{baseDev,jdbcType=LONGVARCHAR},
            </if>
            <if test="upgradeStatus != null">
                #{upgradeStatus,jdbcType=INTEGER},
            </if>
            <if test="path != null">
                #{path,jdbcType=VARCHAR},
            </if>
            <if test="hostPrefix != null">
                #{hostPrefix,jdbcType=VARCHAR},
            </if>
            <if test="del != null">
                #{del,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModify != null">
                #{gmtModify,jdbcType=TIMESTAMP},
            </if>
            <if test="cellId != null">
                #{cellId,jdbcType=VARCHAR},
            </if>
            <if test="imei != null">
                #{imei,jdbcType=VARCHAR},
            </if>
            <if test="temperature != null">
                #{temperature,jdbcType=VARCHAR},
            </if>
            <if test="networkType != null">
                #{networkType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="imsi != null">
                #{imsi,jdbcType=VARCHAR},
            </if>
            <if test="rssi != null">
                #{rssi,jdbcType=VARCHAR},
            </if>
            <if test="sinr != null">
                #{sinr,jdbcType=VARCHAR},
            </if>
            <if test="alias != null">
                #{alias,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="flow != null">
                #{flow,jdbcType=DOUBLE},
            </if>
            <if test="villageNum != null">
                #{villageNum,jdbcType=VARCHAR},
            </if>
            <if test="bindIp != null">
                #{bindIp,jdbcType=VARCHAR},
            </if>
            <if test="iccid != null">
                #{iccid,jdbcType=VARCHAR},
            </if>
            <if test="loginDate != null">
                #{loginDate,jdbcType=TIMESTAMP},
            </if>
            <if test="uptime != null">
                #{uptime,jdbcType=VARCHAR},
            </if>
            <if test="lockCellEnable != null and lockCellEnable != ''">
                #{lockCellEnable,jdbcType=VARCHAR},
            </if>
            <if test="lock4GCellEnable != null and lock4GCellEnable != ''">
                #{lock4GCellEnable,jdbcType=VARCHAR},
            </if>
            <if test="upgradeDevice != null">
                #{upgradeDevice,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsUserDevice">
        <!--@mbg.generated-->
        update acs_user_device
        <set>
            <if test="name != null and name != '' ">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sn != null and sn != '' ">
                sn = #{sn,jdbcType=VARCHAR},
            </if>
            <if test="devicePasswd != null and devicePasswd != '' ">
                device_passwd = #{devicePasswd,jdbcType=VARCHAR},
            </if>
            <if test="model != null and model != '' ">
                model = #{model,jdbcType=VARCHAR},
            </if>
            <if test="lastHbTime != null">
                last_hb_time = #{lastHbTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=INTEGER},
            </if>
            <if test="belongTo != null">
                belong_to = #{belongTo,jdbcType=INTEGER},
            </if>
            <if test="subIp != null and subIp != '' ">
                sub_ip = #{subIp,jdbcType=VARCHAR},
            </if>
            <if test="dumIp != null and dumIp != '' ">
                dum_ip = #{dumIp,jdbcType=VARCHAR},
            </if>
            <if test="trueIp != null and trueIp != '' ">
                true_ip = #{trueIp,jdbcType=VARCHAR},
            </if>
            <if test="verNo != null and verNo != '' ">
                ver_no = #{verNo,jdbcType=VARCHAR},
            </if>
            <if test="hardVerNo != null and hardVerNo != '' ">
                hard_ver_no = #{hardVerNo,jdbcType=VARCHAR},
            </if>
            <if test="signalNum != null and signalNum != '' ">
                signal_num = #{signalNum,jdbcType=VARCHAR},
            </if>
            <if test="signal5g != null and signal5g != '' ">
                signal_5g = #{signal5g,jdbcType=VARCHAR},
            </if>
            <if test="gps != null and gps != '' ">
                gps = #{gps,jdbcType=VARCHAR},
            </if>
            <if test="describ != null and describ != '' ">
                describ = #{describ,jdbcType=VARCHAR},
            </if>
            <if test="baseDevNum != null">
                base_dev_num = #{baseDevNum,jdbcType=INTEGER},
            </if>
            <if test="baseDev != null">
                base_dev = #{baseDev,jdbcType=LONGVARCHAR},
            </if>
            <if test="upgradeStatus != null">
                upgrade_status = #{upgradeStatus,jdbcType=INTEGER},
            </if>
            <if test="path != null and path != '' ">
                `path` = #{path,jdbcType=VARCHAR},
            </if>
            <if test="hostPrefix != null and hostPrefix != '' ">
                host_prefix = #{hostPrefix,jdbcType=VARCHAR},
            </if>
            <if test="del != null">
                del = #{del,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModify != null">
                gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
            </if>
            <if test="cellId != null and cellId != '' ">
                cell_id = #{cellId,jdbcType=VARCHAR},
            </if>
            <if test="imei != null and imei != '' ">
                imei = #{imei,jdbcType=VARCHAR},
            </if>
            <if test="networkType != null and networkType != '' ">
                network_type = #{networkType,jdbcType=VARCHAR},
            </if>
            <if test="temperature != null and temperature != '' ">
                temperature = #{temperature,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="imsi != null and imsi != '' ">
                imsi = #{imsi,jdbcType=VARCHAR},
            </if>
            <if test="rssi != null and rssi != '' ">
                rssi = #{rssi,jdbcType=VARCHAR},
            </if>
            <if test="sinr != null and sinr != '' ">
                sinr = #{sinr,jdbcType=VARCHAR},
            </if>
            <if test="alias != null and alias != '' ">
                alias = #{alias,jdbcType=VARCHAR},
            </if>
            <if test="position != null and position != '' ">
                position = #{position,jdbcType=VARCHAR},
            </if>
            <if test="flow != null">
                flow = #{flow,jdbcType=DOUBLE},
            </if>
            <if test="villageNum != null and villageNum != '' ">
                village_num = #{villageNum,jdbcType=VARCHAR},
            </if>
            <if test="bindIp != null and bindIp != '' ">
                bind_ip = #{bindIp,jdbcType=VARCHAR},
            </if>
            <if test="iccid != null and iccid != '' ">
                iccid = #{iccid,jdbcType=VARCHAR},
            </if>
            <if test="offLine != null and offLine == 1">
                login_date = #{loginDate,jdbcType=TIMESTAMP},
            </if>
            <if test="uptime != null">
                uptime = #{uptime,jdbcType=INTEGER},
            </if>
            <if test="lockCellEnable != null and lockCellEnable!= ''">
                lock_cell_enable = #{lockCellEnable,jdbcType=VARCHAR},
            </if>
            <if test="lock4GCellEnable != null and lock4GCellEnable!= ''">
                lock_cell_4g_enable = #{lock4GCellEnable,jdbcType=VARCHAR},
            </if>
            <if test="lanIp != null and lanIp != '' ">
                lan_ip = #{lanIp,jdbcType=VARCHAR},
            </if>
            <if test="lon != null ">
                lon = #{lon,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=VARCHAR},
            </if>
            <if test="iface != null and iface != '' ">
                iface = #{iface,jdbcType=VARCHAR},
            </if>
            <if test="upgradeDevice != null">
                upgrade_device = #{upgradeDevice,jdbcType=BOOLEAN},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsUserDevice">
        <!--@mbg.generated-->
        update acs_user_device
        set `name` = #{name,jdbcType=VARCHAR},
        sn = #{sn,jdbcType=VARCHAR},
        device_passwd = #{devicePasswd,jdbcType=VARCHAR},
        model = #{model,jdbcType=VARCHAR},
        last_hb_time = #{lastHbTime,jdbcType=TIMESTAMP},
        online_time = #{onlineTime,jdbcType=INTEGER},
        belong_to = #{belongTo,jdbcType=INTEGER},
        sub_ip = #{subIp,jdbcType=VARCHAR},
        dum_ip = #{dumIp,jdbcType=VARCHAR},
        true_ip = #{trueIp,jdbcType=VARCHAR},
        ver_no = #{verNo,jdbcType=VARCHAR},
        hard_ver_no = #{hardVerNo,jdbcType=VARCHAR},
        signal_num = #{signalNum,jdbcType=VARCHAR},
        signal_5g = #{signal5g,jdbcType=VARCHAR},
        gps = #{gps,jdbcType=VARCHAR},
        describ = #{describ,jdbcType=VARCHAR},
        base_dev_num = #{baseDevNum,jdbcType=INTEGER},
        base_dev = #{baseDev,jdbcType=LONGVARCHAR},
        upgrade_status = #{upgradeStatus,jdbcType=INTEGER},
        `path` = #{path,jdbcType=VARCHAR},
        host_prefix = #{hostPrefix,jdbcType=VARCHAR},
        del = #{del,jdbcType=INTEGER},
        cell_id = #{cellId,jdbcType=VARCHAR},
        imei = #{imei,jdbcType=VARCHAR},
        temperature = #{temperature,jdbcType=VARCHAR},
        imsi = #{imsi,jdbcType=VARCHAR},
        rssi = #{rssi,jdbcType=VARCHAR},
        sinr = #{sinr,jdbcType=VARCHAR},
        network_type = #{networkType,jdbcType=VARCHAR},
        status = #{status,jdbcType=INTEGER},
        alias = #{alias,jdbcType=VARCHAR},
        position = #{position,jdbcType=VARCHAR},
        flow = #{flow,jdbcType=DOUBLE},
        village_num = #{villageNum,jdbcType=VARCHAR},
        bind_ip = #{bindIp,jdbcType=VARCHAR},
        iccid = #{iccid,jdbcType=VARCHAR},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
        login_date = #{loginDate,jdbcType=TIMESTAMP}
        ,uptime = #{uptime,jdbcType=INTEGER}
        ,lock_cell_enable = #{lockCellEnable,jdbcType=VARCHAR}
        ,lock_cell_4g_enable = #{lock4GCellEnable,jdbcType=VARCHAR}
        ,lan_ip = #{lanIp,jdbcType=VARCHAR}
        ,lon = #{lon,jdbcType=VARCHAR}
        ,lat = #{lat,jdbcType=VARCHAR}
        , iface = #{iface,jdbcType=VARCHAR}
        , upgrade_device = #{upgradeDevice,jdbcType=BOOLEAN}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <sql id="Where">
        WHERE a.`del`=0
        <if test="belongTo != null and belongTo != '' ">
            AND a.`belong_to`=#{belongTo}
        </if>
        <if test="belongToList != null and belongToList.size() != 0">
            AND a.`belong_to` in
            <foreach collection="belongToList" open="(" item="belongTo" close=")" separator=",">
                #{belongTo}
            </foreach>
        </if>
        <if test="model != null and model != '' ">
            AND a.model=#{model}
        </if>
        <if test="sn != null and sn != ''">
            AND a.sn=#{sn}
        </if>
        <if test="snEq != null and snEq != ''">
            AND a.sn = #{snEq}
        </if>
        <if test="snLike != null and snLike != ''">
            AND a.sn like concat('%', #{snLike}, '%')
        </if>
        <if test="noEqSn != null  and noEqSn != '' ">
            AND a.sn != #{noEqSn}
        </if>
        <if test="verNo != null  and verNo != ''">
            AND a.ver_no=#{verNo}
        </if>
        <if test="alias != null  and alias != ''">
            AND a.alias like concat('%', #{alias}, '%')
        </if>
        <if test="position != null  and position != ''">
            AND a.position like concat('%', #{position}, '%')
        </if>
        <if test="deviceStatus != null and deviceStatus != '' ">
            AND a.status = #{deviceStatus}
        </if>
        <if test="trueIp != null  and trueIp != ''">
            AND a.true_ip like concat('%', #{trueIp}, '%')
        </if>
        <if test="imei != null  and imei != ''">
            AND a.imei like concat('%', #{imei}, '%')
        </if>
        <if test="iccid != null  and iccid != ''">
            AND a.iccid like concat('%', #{iccid}, '%')
        </if>
        <if test="online != null and online != 1">
            AND a.last_hb_time &lt; DATE_ADD(NOW(), INTERVAL -90 SECOND)
        </if>
        <if test="online == 1 ">
            AND a.last_hb_time &gt;= DATE_ADD(NOW(), INTERVAL -90 SECOND)
        </if>
        <if test="havePath == 1">
            AND a.path is not null
        </if>
        <if test="userId != null and userId != '' ">
            AND a.belong_to=#{userId}
        </if>
        <if test="snList != null and snList.size() &gt; 0">
            AND a.sn in
            <foreach close=")" collection="snList" item="sn" open="(" separator=",">
                #{sn}
            </foreach>
        </if>
        <if test="upgradeStatus != null and upgradeStatus != ''">
            AND a.upgrade_status=#{upgradeStatus}
        </if>
        <if test="deviceIds != null and deviceIds.size() &gt; 0">
            AND a.id in
            <foreach close=")" collection="deviceIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="getByFilter" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        <include refid="Where"/>
        <if test="orderByColumn != null and orderByColumn != ''">
            order by #{orderByColumn}
        </if>
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>


    <select id="findListWithUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        b.name as username,
        b.company as company
        from acs_user_device a
        left join acs_user b on a.belong_to = b.id and b.del = 0
        <include refid="Where"/>
        <if test="company != null">
            AND b.company = #{company}
        </if>
        <if test="orderByColumn != null and orderByColumn != ''">
            order by #{orderByColumn}
        </if>
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findListWithPoint" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        b.company as company,
        c.id as pointId
        from acs_user_device a
        left join acs_user b on a.belong_to = b.id and b.del = 0
        left join acs_point c on a.id = c.device_id and a.del = 0
        <include refid="Where"/>
        <if test="company != null">
            AND b.company = #{company}
        </if>
        <if test="orderByColumn != null and orderByColumn != ''">
            order by #{orderByColumn}
        </if>
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="countByFilter" resultType="int">
        select count(1)
        from acs_user_device a
        <include refid="Where"/>
    </select>

    <select id="getByDeviceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del`=0 and a.id in
        <foreach close=")" collection="deviceIds" item="id" open="(" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="getBySn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del`=0 and a.sn = #{sn}
    </select>

    <select id="getByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del`=0 and a.belong_to=#{userId}
    </select>
    <update id="setUpgradeStatus">
        update acs_user_device set upgrade_status=#{status}, gmt_modify = now()
        where `del`=0 and id in
        <foreach close=")" collection="deviceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>
    <select id="getSnList" resultType="java.lang.String">
    select sn
    from acs_user_device
    where `del`=0
    limit #{offset},#{limit}
  </select>

    <select id="getVernoList" resultType="java.lang.String">
    select distinct ver_no
    from acs_user_device
    where `del`=0
  </select>

    <select id="getBaseDev" resultType="java.lang.String">
    select base_dev
    from acs_user_device
    where `del`=0 and id=#{id}
    </select>

    <update id="deleteByUserId">
    update acs_user_device set `del`=`id` where belong_to=#{userId}
    </update>
    <update id="undoDeleteByUserId">
    update acs_user_device set `del`= '0' where belong_to=#{userId} and `del`!= '0'
  </update>
    <select id="getIncreasedDevice" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del`=0 and a.gmt_create &gt;= #{begin} and a.gmt_create &lt;= #{end}
    </select>
    <select id="getDeletedDevice" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del`&gt;0 and a.gmt_modify &gt;= #{begin} and a.gmt_modify &lt;= #{end}
    </select>

    <select id="getByHostPrefix" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del` = 0 and a.host_prefix=#{prefix}
    </select>

    <update id="releaseUpgradeStatus">
        update acs_user_device set upgrade_status=0, gmt_modify = now()
        where upgrade_status=1 and id in
        <foreach collection="deviceIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </update>

    <select id="selectAllNotDelete" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from acs_user_device a
        where a.`del` = 0
    </select>
    <select id="selectModelGroup" resultType="com.alibaba.fastjson.JSONObject">
        select
        model,count(id) as num
        from acs_user_device
        where `del` = 0
        <trim>
            <if test="belongTo != null">
                and belong_to = #{belongTo}
            </if>
            <if test="online != null and online != 1">
                AND last_hb_time &lt; DATE_ADD(NOW(), INTERVAL -90 SECOND)
            </if>
            <if test="online == 1 ">
                AND last_hb_time &gt;= DATE_ADD(NOW(), INTERVAL -90 SECOND)
            </if>
        </trim>
        group by model
    </select>
</mapper>