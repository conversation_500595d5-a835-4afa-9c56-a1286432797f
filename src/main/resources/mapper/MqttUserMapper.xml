<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.mqtt.MqttUserMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.mqtt.MqttUser">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="is_superuser" jdbcType="BOOLEAN" property="isSuperuser" />
        <result column="created" jdbcType="TIMESTAMP" property="created" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, username,password,is_superuser,created
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.mqtt.MqttUser" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into mqtt_user (username,password,
        created
        )
        values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
        #{created,jdbcType=TIMESTAMP}
        )
    </insert>

    <select id="findByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from mqtt_user
        where username = #{username,jdbcType=VARCHAR}
    </select>

    <update id="updateById" parameterType="com.jetron.nb.dal.po.mqtt.MqttUser">
        <!--@mbg.generated-->
        update mqtt_user
        set password = #{password,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateUsernameById" parameterType="com.jetron.nb.dal.po.mqtt.MqttUser">
        <!--@mbg.generated-->
        update mqtt_user
        set username = #{username,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <delete id="delByUsername"  parameterType="java.util.List" >
        delete from mqtt_user where username in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>