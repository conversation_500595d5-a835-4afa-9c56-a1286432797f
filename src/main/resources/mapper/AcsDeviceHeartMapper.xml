<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDeviceHeartMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDeviceHeart">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime"/>
        <association property="userDevice" javaType="com.jetron.nb.dal.po.AcsUserDevice" >
            <!-- id:声明主键，表示 college_id 是关联查询对象的唯一标识-->
            <result property="alias" column="alias" />
            <result property="position" column="position" />
            <result property="id" column="userDeviceId" />
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        a.id, a.sn,a.status,a.insert_time
    </sql>

    <sql id="Where">
        WHERE 1 = 1
        and
        <!--心跳中存在，错误的重复数据。用此排除-->
        not exists
        (select 1 from acs_device_heart where a.sn=sn and a.status=status and a.insert_time = insert_time and a.id &lt; id)
        <if test="sn != null">
            AND a.sn=#{sn}
        </if>
        <if test="start != null">
            and a.insert_time >= #{start,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null ">
            and a.insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_device_heart a
        <include refid="Where"/>
        order by a.insert_time desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findListWithUserDevice" resultMap="BaseResultMap">
        select
        d.alias,
        d.position,
        d.id as userDeviceId,
        <include refid="Base_Column_List"/>
        from acs_device_heart a
        left join acs_user_device d on a.sn = d.sn and d.del = '0'
        <include refid="Where"/>
        order by a.insert_time desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findCount" resultType="int">
        select
        count(a.id)
        from acs_device_heart a
        <include refid="Where"/>
        order by insert_time desc
    </select>

    <select id="findLatest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_device_heart a
        where a.sn = #{sn} and a.insert_time &lt;= #{start,jdbcType=TIMESTAMP}
        order by a.insert_time desc
        limit 0,1
    </select>

    <select id="findAllLatest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_device_heart b
        inner join (
            select max(id) as id from acs_device_heart a
            where a.insert_time &lt;= #{start,jdbcType=TIMESTAMP}
            group by sn
        ) a on b.id = a.id
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDeviceHeart"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_device_heart (sn,status,insert_time
        )
        values (#{sn,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{insertTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDeviceHeart"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_device_heart (sn,status,insert_time)
        values
        <foreach collection="list" item="data" separator=",">
            (
            #{data.sn,jdbcType=VARCHAR},
            #{data.status,jdbcType=INTEGER},
            #{data.insertTime,jdbcType=TIMESTAMP}
            )
        </foreach>

    </insert>
</mapper>