<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsNatMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsNatRule">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
        <result column="enable" jdbcType="INTEGER" property="enable" />
        <result column="display_name" jdbcType="VARCHAR" property="displayName" />
        <result column="target" jdbcType="VARCHAR" property="target" />
        <result column="protocol" jdbcType="VARCHAR" property="protocol" />
        <result column="src_ip" jdbcType="VARCHAR" property="srcIP" />
        <result column="src_port_s" jdbcType="INTEGER" property="srcPortS" />
        <result column="src_port_e" jdbcType="INTEGER" property="srcPortE" />
        <result column="dest_ip" jdbcType="VARCHAR" property="destIP" />
        <result column="dest_port_s" jdbcType="INTEGER" property="destPortS" />
        <result column="dest_port_e" jdbcType="INTEGER" property="destPortE" />
        <result column="snat_ip" jdbcType="VARCHAR" property="snatIP" />
        <result column="snat_port_s" jdbcType="INTEGER" property="snatPortS" />
        <result column="snat_port_e" jdbcType="INTEGER" property="snatPortE" />
        <result column="net_interface" jdbcType="VARCHAR" property="netInterface" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into acs_nat_rule (sn, enable, display_name, target, protocol, src_ip, src_port_s, src_port_e, dest_ip, dest_port_s, dest_port_e, snat_ip, snat_port_s, snat_port_e, net_interface, gmt_create, status)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.sn, jdbcType=VARCHAR},
            #{item.enable, jdbcType=INTEGER},
            #{item.displayName, jdbcType=VARCHAR},
            #{item.target, jdbcType=VARCHAR},
            #{item.protocol, jdbcType=VARCHAR},
            #{item.srcIP, jdbcType=VARCHAR},
            #{item.srcPortS, jdbcType=INTEGER},
            #{item.srcPortE, jdbcType=INTEGER},
            #{item.destIP, jdbcType=VARCHAR},
            #{item.destPortS, jdbcType=INTEGER},
            #{item.destPortE, jdbcType=INTEGER},
            #{item.snatIP, jdbcType=VARCHAR},
            #{item.snatPortS, jdbcType=INTEGER},
            #{item.snatPortE, jdbcType=INTEGER},
            #{item.netInterface, jdbcType=VARCHAR},
            #{item.gmtCreate, jdbcType=TIMESTAMP},
            #{item.status, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update acs_nat_rule
            set
            enable = #{item.enable, jdbcType=INTEGER},
            display_name = #{item.displayName, jdbcType=VARCHAR},
            target = #{item.target, jdbcType=VARCHAR},
            protocol = #{item.protocol, jdbcType=VARCHAR},
            src_ip = #{item.srcIP, jdbcType=VARCHAR},
            src_port_s = #{item.srcPortS, jdbcType=INTEGER},
            src_port_e = #{item.srcPortE, jdbcType=INTEGER},
            dest_ip = #{item.destIP, jdbcType=VARCHAR},
            dest_port_s = #{item.destPortS, jdbcType=INTEGER},
            dest_port_e = #{item.destPortE, jdbcType=INTEGER},
            snat_ip = #{item.snatIP, jdbcType=VARCHAR},
            snat_port_s = #{item.snatPortS, jdbcType=INTEGER},
            snat_port_e = #{item.snatPortE, jdbcType=INTEGER},
            net_interface = #{item.netInterface, jdbcType=VARCHAR}
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

</mapper>