<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsKanbanTitleMapper">



    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsKanbanTitle">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="visitor_id" jdbcType="VARCHAR" property="visitorId" />
        <result column="title" jdbcType="VARCHAR" property="title" />
    </resultMap>


    <sql id="Base_Column_List">
        id,gmt_create,visitor_id,title
    </sql>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_kanban_title
        <include refid="Where"/>
    </select>

    <select id="findOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_kanban_title
        <include refid="Where"/>
    </select>

    <sql id="Where">
        WHERE 1 = 1
        <if test="visitorId != null">
            AND `visitor_id`=#{visitorId}
        </if>
        <if test="id != null">
            AND `id`=#{id}
        </if>
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsVpn" useGeneratedKeys="true">
        insert into acs_kanban_title (gmt_create,visitor_id,title)
        values(
        #{gmtCreate,jdbcType=TIMESTAMP},
        #{visitorId,jdbcType=VARCHAR},
        #{title,jdbcType=VARCHAR})
    </insert>

    <update id="update" parameterType="com.jetron.nb.dal.po.AcsVpn">
        update acs_kanban_title
        <set>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


</mapper>