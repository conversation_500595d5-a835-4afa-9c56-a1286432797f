<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsVpnMapper">



    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsVpn">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="static_ip" jdbcType="VARCHAR" property="staticIp" />
        <result column="remote_ip" jdbcType="VARCHAR" property="remoteIp" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
        <result column="insert_user" jdbcType="VARCHAR" property="insertUser" />
    </resultMap>


    <sql id="Base_Column_List">
        id,username,static_ip,remote_ip,insert_date,insert_user
    </sql>




    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsVpn" useGeneratedKeys="true">
        insert into acs_vpn (username,static_ip,remote_ip,
        insert_date,insert_user)
        values(
        #{username,jdbcType=VARCHAR},
        #{staticIp,jdbcType=VARCHAR},
        #{remoteIp,jdbcType=VARCHAR},
        #{insertDate,jdbcType=TIMESTAMP},
        #{insertUser,jdbcType=VARCHAR})
    </insert>


    <update id="update" parameterType="com.jetron.nb.dal.po.AcsVpn">
        update acs_vpn
        <set>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="staticIp != null">
                static_ip = #{staticIp,jdbcType=VARCHAR},
            </if>
            <if test="remoteIp != null">
                remote_ip = #{remoteIp,jdbcType=VARCHAR},
            </if>
            <if test="insertDate != null">
                insert_date = #{insertDate,jdbcType=TIMESTAMP},
            </if>
            <if test="insertUser != null">
                insert_user = #{insertUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}


    </update>



    <delete id="delete" parameterType="java.lang.Integer">
        delete from acs_vpn
        where id = #{id,jdbcType=INTEGER}
    </delete>





    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_vpn
    </select>


    <select id="findByUserName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_vpn
        where username = #{username,jdbcType=VARCHAR}
    </select>



</mapper>