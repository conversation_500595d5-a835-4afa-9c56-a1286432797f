<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsVpnConfigMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsVpnConfig">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="vpn_host" jdbcType="VARCHAR" property="vpnHost" />
    <result column="vpn_port" jdbcType="INTEGER" property="vpnPort" />
    <result column="vpn_config" jdbcType="LONGVARCHAR" property="vpnConfig" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
    <result column="multilayer" jdbcType="VARCHAR" property="multilayer" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, vpn_host, vpn_port, vpn_config, gmt_create, gmt_modify, multilayer
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_vpn_config
    where `del`=0 and id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsVpnConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_vpn_config (user_id, vpn_host, vpn_port, 
      vpn_config, gmt_create, gmt_modify
      )
    values (#{userId,jdbcType=INTEGER}, #{vpnHost,jdbcType=VARCHAR}, #{vpnPort,jdbcType=INTEGER}, 
      #{vpnConfig,jdbcType=LONGVARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsVpnConfig" useGeneratedKeys="true">
    insert into acs_vpn_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="vpnHost != null">
        vpn_host,
      </if>
      <if test="vpnPort != null">
        vpn_port,
      </if>
      <if test="vpnConfig != null">
        vpn_config,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
      <if test="multilayer != null">
        multilayer,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="vpnHost != null">
        #{vpnHost,jdbcType=VARCHAR},
      </if>
      <if test="vpnPort != null">
        #{vpnPort,jdbcType=INTEGER},
      </if>
      <if test="vpnConfig != null">
        #{vpnConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
      <if test="multilayer != null">
        #{multilayer, jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsVpnConfig">
    <!--@mbg.generated-->
    update acs_vpn_config
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="vpnHost != null">
        vpn_host = #{vpnHost,jdbcType=VARCHAR},
      </if>
      <if test="vpnPort != null">
        vpn_port = #{vpnPort,jdbcType=INTEGER},
      </if>
      <if test="vpnConfig != null">
        vpn_config = #{vpnConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
      <if test="multilayer != null">
        multilayer = #{multilayer, jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsVpnConfig">
    <!--@mbg.generated-->
    update acs_vpn_config
    set user_id = #{userId,jdbcType=INTEGER},
      vpn_host = #{vpnHost,jdbcType=VARCHAR},
      vpn_port = #{vpnPort,jdbcType=INTEGER},
      vpn_config = #{vpnConfig,jdbcType=LONGVARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      multilayer = #{multilayer, jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="getByUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from acs_vpn_config
    where `del`=0 and user_id=#{userId}
  </select>
  <select id="getByUserIds" resultMap="BaseResultMap">
   select id, user_id, vpn_host, vpn_port, gmt_create, gmt_modify, multilayer
   from acs_vpn_config
   where `del`=0 and user_id in
   <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
     #{userId}
   </foreach>
  </select>

  <update id="deleteByUserId">
    update acs_vpn_config set `del`=`id`
    where user_id=#{userId}
  </update>
  <update id="undoDeleteByUserId">
    update acs_vpn_config set `del`= '0'
    where user_id=#{userId} and `del` != '0'
  </update>
</mapper>