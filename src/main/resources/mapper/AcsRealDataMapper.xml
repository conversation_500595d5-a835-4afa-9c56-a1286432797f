<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsRealDataMapper">


    <insert id="insertList" parameterType="java.util.ArrayList">
        insert into acs_real_data
        (sn,company,dev_temp,rssi,signal_quality,ram,cpu_ration
        ,insert_time,update_time,rsrq)
        values
        <foreach collection="list" separator="," item="data">
            (#{data.sn,jdbcType=VARCHAR},#{data.company,jdbcType=VARCHAR}
            ,#{data.devTemp,jdbcType=VARCHAR},#{data.rssi,jdbcType=VARCHAR}
            ,#{data.signalQuality,jdbcType=VARCHAR},#{data.ram,jdbcType=VARCHAR}
            ,#{data.cpuRation,jdbcType=VARCHAR},#{data.insertTime,jdbcType=TIMESTAMP}
            ,#{data.updateTime,jdbcType=TIMESTAMP},#{data.rsrq,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>