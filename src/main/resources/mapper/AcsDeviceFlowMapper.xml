<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDeviceFlowMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDeviceFlow">
    <!--@mbg.generated-->
    <!--@Table acs_device_flow-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="up_flow" jdbcType="BIGINT" property="upFlow" />
    <result column="down_flow" jdbcType="BIGINT" property="downFlow" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sn, up_flow, down_flow, `date`, gmt_create, gmt_modify
  </sql>
  <select id="getDeviceFlow" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device_flow
    <include refid="Where"/>
  </select>
  <sql id="Where">
    WHERE 1 = 1
    <if test="sn != null">
      AND `sn`=#{sn}
    </if>
    <if test="start != null">
      AND gmt_create&gt;=#{start}
    </if>
    <if test="end != null">
      AND gmt_create &lt; #{end}
    </if>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_device_flow
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectWeeklyFlow"  resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select `date`,sn,ROUND(sum(up_flow+down_flow)/1024) as down_flow
    from acs_device_flow
    where  `date` &gt;= #{date}
        and sn in
        <foreach collection="snList" open="(" item="sn" close=")" separator=",">
          #{sn}
        </foreach>
    group by `date`,sn
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_device_flow
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDeviceFlow" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_device_flow (sn, up_flow, down_flow, 
      `date`, gmt_create, gmt_modify
      )
    values (#{sn,jdbcType=VARCHAR}, #{upFlow,jdbcType=BIGINT}, #{downFlow,jdbcType=BIGINT}, 
      #{date,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDeviceFlow" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_device_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sn != null">
        sn,
      </if>
      <if test="upFlow != null">
        up_flow,
      </if>
      <if test="downFlow != null">
        down_flow,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="upFlow != null">
        #{upFlow,jdbcType=BIGINT},
      </if>
      <if test="downFlow != null">
        #{downFlow,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsDeviceFlow">
    <!--@mbg.generated-->
    update acs_device_flow
    <set>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="upFlow != null">
        up_flow = #{upFlow,jdbcType=BIGINT},
      </if>
      <if test="downFlow != null">
        down_flow = #{downFlow,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        `date` = #{date,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsDeviceFlow">
    <!--@mbg.generated-->
    update acs_device_flow
    set sn = #{sn,jdbcType=VARCHAR},
      up_flow = #{upFlow,jdbcType=BIGINT},
      down_flow = #{downFlow,jdbcType=BIGINT},
      `date` = #{date,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="getTodayRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device_flow
    where sn=#{sn} and `date`=#{date}
  </select>


  <select id="getUserFlow" resultType="com.jetron.nb.dal.po.UserFlow">
    select a.`date`, sum(a.up_flow+a.down_flow) as flow
    from acs_device_flow as a join acs_user_device as b on a.sn=b.sn
    where b.belong_to=#{userId} and a.gmt_create&gt;=#{start} and a.gmt_create &lt;#{end}
    group by a.`date`
  </select>

  <select id="getUserFlowWithDate" resultMap="BaseResultMap">
    select
    a.id, a.sn, a.up_flow, a.down_flow, a.`date`, a.gmt_create, a.gmt_modify
    from acs_device_flow as a
    left join acs_user_device as b on a.sn = b.sn
    <trim prefix="where" prefixOverrides="and">
        <if test="start != null">
          and a.gmt_create &gt;= #{start}
        </if>
        <if test="end != null">
          and a.gmt_create &lt;= #{end}
        </if>
      <if test="userId != null">
          and b.belong_to = #{userId}
      </if>
    </trim>
    order by a.gmt_create asc
  </select>


  <select id="findLastFlow"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device_flow a
    inner join (
      select sn as bsn,max(gmt_create) as bdate from acs_device_flow
      where 1 = 1
      <if test="start != null">
        AND gmt_create &lt;= #{start}
      </if>
      GROUP BY sn
    ) b on a.sn = b.bsn and a.gmt_create = b.bdate
    where 1 = 1
    <if test="sn != null">
      AND a.`sn`=#{sn}
    </if>
    <if test="userId != null">
      and a.belong_to = #{userId}
    </if>
  </select>

  <insert id="insertMonthList"  parameterType="java.util.ArrayList">
    insert into acs_device_flow_month
    (id,sn, company, up_flow, `date`, gmt_create, gmt_modify)
    values
    <foreach collection="list" separator="," item="data">
      (#{data.id,jdbcType=VARCHAR},#{data.sn,jdbcType=VARCHAR},#{data.company,jdbcType=VARCHAR}
      ,#{data.upFlow,jdbcType=BIGINT},#{data.date,jdbcType=VARCHAR}
      ,#{data.gmtCreate,jdbcType=TIMESTAMP},#{data.gmtModify,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>