<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDriverUpgradeMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDriverUpgrade">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="dev_name" jdbcType="VARCHAR" property="devName" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    a.id, a.company, a.driver_id, a.device_id,a.dev_name,a.path
    ,a.`status`, a.gmt_create, a.gmt_modify
  </sql>

    <sql id="Where">
        where 1=1
        <if test="id != null">
            and a.id = #{id}
        </if>
        <if test="company != null">
            and a.company=#{company}
        </if>
        <if test="driverId != null">
            and a.driver_id=#{driverId}
        </if>
        <if test="deviceId != null">
            and a.device_id=#{deviceId}
        </if>
        <if test="devName != null">
            and a.dev_name=#{devName}
        </if>
    </sql>

    <select id="selectCurrentStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_driver_upgrade a
        inner join (
            select max(id) as id from acs_driver_upgrade
            group by device_id
        ) b on a.id = b.id
        <include refid="Where" />

    </select>

</mapper>