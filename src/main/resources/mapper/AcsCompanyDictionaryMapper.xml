<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsCompanyDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsCompanyDictionary">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
        <result column="dict_label" jdbcType="VARCHAR" property="dictLabel" />
        <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        a.id,a.company,a.gmt_create,a.gmt_modify,a.remark,a.dict_type,a.dict_label,a.dict_value
    </sql>

    <select id="findList" parameterType="com.jetron.nb.dal.po.AcsCompanyDictionary" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_company_dict a
        <include refid="Where" />
    </select>

    <sql id="Where">
        where 1=1
        <if test="company != null">
            and a.company = #{company}
        </if>
        <if test="dictType != null">
            and a.dict_type = #{dictType}
        </if>
        <if test="dictLabel != null">
            and a.dict_label = #{dictLabel}
        </if>
    </sql>

    <select id="findCount" parameterType="com.jetron.nb.dal.po.AcsCompanyDictionary" resultType="int">
        select
        count(a.id)
        from acs_company_dict a
        <include refid="Where" />
    </select>

    <insert id="insertList" parameterType="java.util.ArrayList">
        insert into acs_company_dict (company,gmt_create,remark,dict_type,dict_label, dict_value) values
        <foreach collection="list" separator="," item="data">
            (#{data.company,jdbcType=VARCHAR},#{data.gmtCreate,jdbcType=TIMESTAMP},#{data.remark,jdbcType=VARCHAR},
            #{data.dictType,jdbcType=VARCHAR},#{data.dictLabel,jdbcType=VARCHAR}, #{data.dictValue,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="delete" >
        delete from acs_company_dict where id in
        <foreach collection="list" open="(" separator="," close=")" item="temp">
            #{temp}
        </foreach>
    </delete>

    <update id="update" parameterType="com.jetron.nb.dal.po.AcsCompanyDictionary">
        <!--
        company = #{company,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        dict_type = #{dictType,jdbcType=VARCHAR},
        dict_label = #{dictLabel,jdbcType=VARCHAR},
        -->
        update acs_company_dict
        <set>
            <if test="dictValue != null">
                `dict_value` = #{dictValue,jdbcType=VARCHAR},
            </if>
            <if test="gmtModify != null">
                `gmt_modify` = #{gmtModify,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>