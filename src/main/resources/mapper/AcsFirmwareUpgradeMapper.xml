<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsFirmwareUpgradeMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsFirmwareUpgrade">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="firmware_id" jdbcType="INTEGER" property="firmwareId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="belong_to" jdbcType="INTEGER" property="belongTo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    a.id, a.firmware_id, a.device_id, a.belong_to, a.`status`, a.gmt_create, a.gmt_modify
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_firmware_upgrade a
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_firmware_upgrade
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsFirmwareUpgrade" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_firmware_upgrade (firmware_id, device_id, belong_to, 
      `status`, gmt_create, gmt_modify
      )
    values (#{firmwareId,jdbcType=INTEGER}, #{deviceId,jdbcType=INTEGER}, #{belongTo,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsFirmwareUpgrade" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_firmware_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firmwareId != null">
        firmware_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="belongTo != null">
        belong_to,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firmwareId != null">
        #{firmwareId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="belongTo != null">
        #{belongTo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsFirmwareUpgrade">
    <!--@mbg.generated-->
    update acs_firmware_upgrade
    <set>
      <if test="firmwareId != null">
        firmware_id = #{firmwareId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="belongTo != null">
        belong_to = #{belongTo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsFirmwareUpgrade">
    <!--@mbg.generated-->
    update acs_firmware_upgrade
    set firmware_id = #{firmwareId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=INTEGER},
      belong_to = #{belongTo,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="getByFirmwareId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_firmware_upgrade a
    where firmware_id=#{firmwareId}
    order by gmt_create desc
  </select>

  <insert id="batchInsert">
    insert into acs_firmware_upgrade (firmware_id, device_id, belong_to, status) values
    <foreach collection="upgradeList" item="record" separator=",">
      (#{record.firmwareId}, #{record.deviceId}, #{record.belongTo}, #{record.status})
    </foreach>
  </insert>
  <delete id="deleteByFirmwareId">
    delete from acs_firmware_upgrade where firmware_id=#{firmwareId}
  </delete>

  <select id="getLatestUpgrade" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_firmware_upgrade a
    where device_id=#{deviceId}
    order by id desc
    limit 1
  </select>

  <sql id="Where">
    where 1=1
    <if test="id != null">
      and a.firmware_id=#{id}
    </if>
     <if test="belongTo != null">
       and a.belong_to=#{belongTo}
     </if>
  </sql>
  <select id="getByFilter" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
   from acs_firmware_upgrade a
    <if test="valid != null">
        inner join acs_user_device b on a.device_id = b.id and b.del = 0
    </if>
   <include refid="Where" />
    <if test="orderByDesc != null and orderByDesc != ''">
      order by gmt_create desc
    </if>
    <if test="limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="count" resultType="int">
    select
    count(a.id)
    from acs_firmware_upgrade a
    inner join acs_user_device b on a.device_id = b.id and b.del = 0
    <include refid="Where" />
  </select>

  <select id="getTimeout" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from acs_firmware_upgrade a
    where status=0 and gmt_create &lt; #{deadline}
  </select>

  <update id="timeout">
    update acs_firmware_upgrade set status=4
    where status=0 and device_id in
    <foreach collection="deviceIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </update>


</mapper>