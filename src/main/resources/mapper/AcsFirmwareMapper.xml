<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsFirmwareMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsFirmware">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="belong_to" jdbcType="INTEGER" property="belongTo" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="describ" jdbcType="VARCHAR" property="describ" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="config_id" jdbcType="INTEGER" property="configId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, model, version, belong_to, `type`, describ, `path`, gmt_create, gmt_modify, company, config_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_firmware
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_firmware
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsFirmware" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_firmware (`name`, model, version, 
      belong_to, `type`, describ, 
      `path`, gmt_create, gmt_modify, company, config_id
      )
    values (#{name,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{belongTo,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{describ,jdbcType=VARCHAR}, 
      #{path,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP},
      #{company,jdbcType=VARCHAR},#{configId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsFirmware" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_firmware
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="belongTo != null">
        belong_to,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="describ != null">
        describ,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="configId != null">
        config_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="belongTo != null">
        #{belongTo,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="describ != null">
        #{describ,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsFirmware">
    <!--@mbg.generated-->
    update acs_firmware
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="belongTo != null">
        belong_to = #{belongTo,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="describ != null">
        describ = #{describ,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsFirmware">
    <!--@mbg.generated-->
    update acs_firmware
    set `name` = #{name,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      belong_to = #{belongTo,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      describ = #{describ,jdbcType=VARCHAR},
      `path` = #{path,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      config_id = #{configId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <sql id="Where">
    where 1=1
    <if test="name != null">
      and `name` like concat('%', #{name}, '%')
    </if>
    <if test="model != null">
      and model=#{model}
    </if>
    <choose>
      <when test="belongTo != null and includeSys">
        and (belong_to=#{belongTo} or `type`=0)
      </when>
       <when test="belongTo != null and !includeSys">
         and belong_to=#{belongTo}
       </when>
       <when test="belongTo == null and includeSys">
         and `type` = 0
       </when>
    </choose>
  </sql>
  <select id="getByFilter" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_firmware
    <include refid="Where" />
    order by id desc
    <if test="limit != null">
      limit #{offset}, #{limit}
    </if>
  </select>

  <select id="countByFilter" resultType="int">
    select
    count(1)
    from acs_firmware
    <include refid="Where" />
  </select>

</mapper>