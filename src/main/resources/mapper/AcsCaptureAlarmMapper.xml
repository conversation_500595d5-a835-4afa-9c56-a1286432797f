<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.mqtt.AcsCaptureAlarmMapper">



    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsCaptureAlarm">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
        <result column="parameter_code" jdbcType="VARCHAR" property="parameterCode" />
        <result column="parameter_value" jdbcType="VARCHAR" property="parameterValue" />
        <result column="current_value" jdbcType="VARCHAR" property="currentValue" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="alarm_rank" jdbcType="VARCHAR" property="alarmRank" />
        <result column="alarm_status" jdbcType="VARCHAR" property="alarmStatus" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="parameter_key" jdbcType="VARCHAR" property="parameterKey" />
        <result column="parameter_content" jdbcType="VARCHAR" property="parameterContent" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <association property="acsUserDevice" javaType="com.jetron.nb.dal.po.AcsUserDevice" >
            <!-- id:声明主键，表示 college_id 是关联查询对象的唯一标识-->
            <result property="alias" column="alias" />
            <result property="position" column="position" />
            <result property="id" column="userDeviceId" />
        </association>

    </resultMap>


    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,sn,parameter_code,parameter_value,current_value,insert_time,alarm_rank,alarm_status,company
        ,parameter_key,parameter_content,ip
    </sql>

    <sql id="Where">
        WHERE 1 = 1
        <if test="start != null and end != null ">
            and insert_time >= #{start,jdbcType=TIMESTAMP} and insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
        <if test="company != null">
            and company = #{company,jdbcType=VARCHAR}
        </if>
        <if test="parameterKey != null">
            and parameter_key = #{parameterKey,jdbcType=VARCHAR}
        </if>
        <if test="alarmStatus != null">
            and alarm_status = #{alarmStatus,jdbcType=VARCHAR}
        </if>
        <if test="sn != null">
            and sn  like concat('%', #{sn}, '%')
        </if>
        <if test="snEq != null">
            and sn  = #{snEq,jdbcType=VARCHAR}
        </if>
        <if test="statusNe != null and statusNe != '' ">
            and alarm_status != #{statusNe,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="Where2">
        WHERE 1 = 1
        <if test="start != null">
            and a.insert_time >= #{start,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null ">
            and  a.insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
        <if test="company != null">
            and a.company = #{company,jdbcType=VARCHAR}
        </if>
        <if test="parameterCode != null">
            and a.parameter_code = #{parameterCode,jdbcType=VARCHAR}
        </if>
        <if test="parameterKey != null">
            and a.parameter_key = #{parameterKey,jdbcType=VARCHAR}
        </if>
        <if test="parameterContent != null">
            and a.parameter_content = #{parameterContent,jdbcType=VARCHAR}
        </if>
        <if test="sn != null">
            and a.sn like concat('%', #{sn}, '%')
        </if>
        <if test="snEq != null">
            and a.sn  = #{snEq,jdbcType=VARCHAR}
        </if>
        <if test="acsUserDevice != null and acsUserDevice.alias != null ">
            and d.alias like concat('%', #{acsUserDevice.alias}, '%')
        </if>
        <if test="acsUserDevice != null and acsUserDevice.position != null ">
            and d.position like concat('%', #{acsUserDevice.position}, '%')
        </if>
        <if test="alarmStatus != null and alarmStatus != '' ">
            and a.alarm_status = #{alarmStatus,jdbcType=VARCHAR}
        </if>
        <if test="statusNe != null and statusNe != '' ">
            and a.alarm_status != #{statusNe,jdbcType=VARCHAR}
        </if>
    </sql>


    <select id="findCount"  resultType="int">
        select
         count(a.id)
        from acs_capture_alarm a
        <include refid="Where2" />
    </select>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_alarm
        <include refid="Where" />
        order by id desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findWithDeviceList" resultMap="BaseResultMap">
        select
        d.alias,
        d.position,
        d.id as userDeviceId,
        a.*
        from acs_capture_alarm a
        inner join acs_user_device d on a.sn = d.sn and d.del = 0
        <include refid="Where2" />
        order by a.insert_time desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findWithDeviceCount"  resultType="int">
        select
        count(a.id)
        from acs_capture_alarm a
        inner join acs_user_device d on a.sn = d.sn and d.del = 0
        <include refid="Where2" />
    </select>

    <select id="findCurrentActiveAlarmBySn" resultMap="BaseResultMap">
        select
        d.alias,
        d.position,
        d.id as userDeviceId,
        a.*
        from acs_capture_alarm a
	    inner join (
            select sn,parameter_code,max(insert_time) as insert_time  from acs_capture_alarm
            GROUP BY sn,parameter_code
        ) b
	    on a.parameter_code = b.parameter_code and a.insert_time = b.insert_time and a.sn = b.sn
        inner join acs_user_device d on a.sn = d.sn and d.del = 0
        <include refid="Where2" />
         and a.alarm_status = 1
    </select>

    <select id="findCurrentActiveAlarm" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_alarm
        <include refid="Where" />
        order by id desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findBySnAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_alarm
        where sn = #{sn,jdbcType=VARCHAR}
        and insert_time >= #{beginDate,jdbcType=TIMESTAMP} and insert_time &lt; #{endDate,jdbcType=TIMESTAMP}
    </select>

    <select id="findByDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_alarm
        where insert_time >= #{beginDate,jdbcType=TIMESTAMP} and insert_time &lt; #{endDate,jdbcType=TIMESTAMP}
    </select>

    <select id="findByDateAndCompany" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_alarm
        where company = #{company,jdbcType=VARCHAR}
        and insert_time >= #{beginDate,jdbcType=TIMESTAMP} and insert_time &lt; #{endDate,jdbcType=TIMESTAMP}
    </select>

    <select id="findActiveAlarm" resultMap="BaseResultMap">
        select  a.* from acs_capture_alarm a
        inner join (
            select max(id) as id from  acs_capture_alarm
            GROUP BY sn,parameter_code
        ) b on a.id = b.id
        <include refid="Where2" />
    </select>

    <update id="updateAlarmStatus" parameterType="com.jetron.nb.dal.po.AcsCaptureAlarm">
        update acs_capture_alarm
        <set>
            <if test="sn != null">
                sn = #{sn,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="alarmRank != null">
                alarm_rank = #{alarmRank,jdbcType=VARCHAR},
            </if>
            <if test="alarmStatus != null">
                alarm_status = #{alarmStatus,jdbcType=VARCHAR},
            </if>
            <if test="parameterCode != null">
                parameter_code = #{parameterCode,jdbcType=VARCHAR},
            </if>
            <if test="parameterValue != null">
                parameter_value = #{parameterValue,jdbcType=VARCHAR},
            </if>
            <if test="currentValue != null">
                current_value = #{currentValue,jdbcType=VARCHAR},
            </if>
            <if test="insertTime != null">
                insert_time = #{insertTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}


    </update>

    <update id = "updateStatusList" >
        update acs_capture_alarm set alarm_status =  #{status,jdbcType=VARCHAR}
        where id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsCaptureAlarm" useGeneratedKeys="true">
        insert into acs_capture_alarm (sn, parameter_code, parameter_value,
        current_value, insert_time, alarm_rank, alarm_status, company,
        parameter_key,ip,parameter_content)
        values(
        #{sn,jdbcType=VARCHAR},
        #{parameterCode,jdbcType=VARCHAR},
        #{parameterValue,jdbcType=VARCHAR},
        #{currentValue,jdbcType=VARCHAR},
        #{insertTime,jdbcType=TIMESTAMP},
        #{alarmRank,jdbcType=VARCHAR},
        #{alarmStatus,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{parameterKey,jdbcType=VARCHAR},
        #{ip,jdbcType=VARCHAR},
        #{parameterContent,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsCaptureAlarm" useGeneratedKeys="true">
        insert into acs_capture_alarm (sn, parameter_code, parameter_value,
        current_value, insert_time, alarm_rank, alarm_status, company,
        parameter_key,ip ,parameter_content)
        values
        <foreach collection="list" item="data" separator=",">(
            #{data.sn,jdbcType=VARCHAR},
            #{data.parameterCode,jdbcType=VARCHAR},
            #{data.parameterValue,jdbcType=VARCHAR},
            #{data.currentValue,jdbcType=VARCHAR},
            #{data.insertTime,jdbcType=TIMESTAMP},
            #{data.alarmRank,jdbcType=VARCHAR},
            #{data.alarmStatus,jdbcType=VARCHAR},
            #{data.company,jdbcType=VARCHAR},
            #{data.parameterKey,jdbcType=VARCHAR},
            #{data.ip,jdbcType=VARCHAR},
            #{data.parameterContent,jdbcType=VARCHAR})
        </foreach>

    </insert>

    <delete id="delList" >
        delete from acs_capture_alarm
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")" >
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="delByDate" >
    delete from acs_capture_alarm
    where insert_time between  #{startDate,jdbcType=TIMESTAMP} and  #{endDate,jdbcType=TIMESTAMP}
  </delete>

    <update id="clearAlarm" >
      update acs_capture_alarm set alarm_status = '3'
      where sn = #{sn,jdbcType=VARCHAR} and alarm_status = '1'
    </update>

</mapper>