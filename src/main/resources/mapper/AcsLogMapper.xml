<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsLogMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsLog">
    <!--@mbg.generated-->
    <!--@Table acs_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="log_type" jdbcType="INTEGER" property="logType" />
    <result column="log_version" jdbcType="INTEGER" property="logVersion" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, user_id, content, login_ip, log_type, log_version, gmt_create, gmt_modify
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_log (`name`, user_id, content, 
      login_ip, log_type, log_version, 
      gmt_create, gmt_modify)
    values (#{name,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR}, 
      #{loginIp,jdbcType=VARCHAR}, #{logType,jdbcType=INTEGER}, #{logVersion,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="loginIp != null">
        login_ip,
      </if>
      <if test="logType != null">
        log_type,
      </if>
      <if test="logVersion != null">
        log_version,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="loginIp != null">
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="logVersion != null">
        #{logVersion,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsLog">
    <!--@mbg.generated-->
    update acs_log
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="loginIp != null">
        login_ip = #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        log_type = #{logType,jdbcType=INTEGER},
      </if>
      <if test="logVersion != null">
        log_version = #{logVersion,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsLog">
    <!--@mbg.generated-->
    update acs_log
    set `name` = #{name,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR},
      login_ip = #{loginIp,jdbcType=VARCHAR},
      log_type = #{logType,jdbcType=INTEGER},
      log_version = #{logVersion,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <sql id="Where">
    where 1=1
    <if test="name != null">
      and `name` like concat('%',#{name},'%')
    </if>
    <if test="content != null">
      and `content` like concat('%',#{content},'%')
    </if>
    <if test="start != null">
        and gmt_create &gt;= #{start, jdbcType=TIMESTAMP}
    </if>
    <if test="end != null">
        and gmt_create &lt;= #{end, jdbcType=TIMESTAMP}
    </if>
     <if test="userIdList != null and userIdList.size() != 0">
       and user_id in
       <foreach close=")" collection="userIdList" item="uid" open="(" separator=",">
         #{uid}
       </foreach>
     </if>
    <if test="logType != null">
      and log_type=#{logType}
    </if>
  </sql>

  <select id="getByFilter" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
   from acs_log
   <include refid="Where" />
   order by id desc
   <if test="limit != null">
     limit #{offset}, #{limit}
   </if>
  </select>

  <select id="countByFilter" resultType="int">
    select count(1)
    from acs_log
    <include refid="Where" />
  </select>
  <update id="appendLog">
    update `acs_log` set log_version=#{logVersion}+1, content=#{logContent}
    where id=#{logId} and log_version=#{logVersion}
  </update>

  <delete id="delList" >
    delete from acs_log
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")" >
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>

  <delete id="delByDate" >
    delete from acs_log
    where gmt_create between  #{startDate,jdbcType=TIMESTAMP} and  #{endDate,jdbcType=TIMESTAMP}
  </delete>
</mapper>