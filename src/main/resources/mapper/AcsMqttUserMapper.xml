<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.mqtt.AcsMqttUserMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.mqtt.AcsMqttUser">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="mqtt_user" jdbcType="VARCHAR" property="mqttUser" />
        <result column="acs_user_id" jdbcType="INTEGER" property="acsUserId" />
        <result column="created" jdbcType="TIMESTAMP" property="created" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, mqtt_user,acs_user_id,created
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.mqtt.AcsMqttUser" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_mqtt_user (mqtt_user,acs_user_id,
            created
        )
        values (#{mqttUser,jdbcType=VARCHAR}, #{acsUserId,jdbcType=INTEGER},
                #{created,jdbcType=TIMESTAMP}
        )
    </insert>

    <select id="selectByAcsUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_mqtt_user
        where acs_user_id = #{userId,jdbcType=INTEGER}
    </select>

    <delete id="delByUserId" parameterType="int">
        delete from acs_mqtt_user where acs_user_id  = #{userId,jdbcType=INTEGER}
    </delete>

    <update id="updateByAcsUserId" parameterType="com.jetron.nb.dal.po.mqtt.AcsMqttUser">
        <!--@mbg.generated-->
        update acs_mqtt_user
        set mqtt_user = #{mqttUser,jdbcType=VARCHAR}
        where acs_user_id = #{acsUserId,jdbcType=INTEGER}
    </update>
</mapper>