<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsUserMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsUser">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="passwd" jdbcType="VARCHAR" property="passwd"/>
        <result column="device_passwd" jdbcType="VARCHAR" property="devicePasswd"/>
        <result column="role" jdbcType="INTEGER" property="role"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="describ" jdbcType="VARCHAR" property="describ"/>
        <result column="last_login_ip" jdbcType="VARCHAR" property="lastLoginIp"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="last_logout_time" jdbcType="TIMESTAMP" property="lastLogoutTime"/>
        <result column="curr_login_ip" jdbcType="VARCHAR" property="currLoginIp"/>
        <result column="curr_login_time" jdbcType="TIMESTAMP" property="currLoginTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="del" jdbcType="INTEGER" property="del"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, passwd, device_passwd, `role`, company, describ, last_login_ip, last_login_time,
        last_logout_time, curr_login_ip, curr_login_time, `status`, parent_id,del, gmt_create,
        gmt_modify
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user
        where `del`=0 and id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsUser"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_user (`name`, passwd, device_passwd,
        `role`, company, describ,
        last_login_ip, last_login_time, last_logout_time,
        curr_login_ip, curr_login_time, `status`,
        parent_id, gmt_create, gmt_modify
        )
        values (#{name,jdbcType=VARCHAR}, #{passwd,jdbcType=VARCHAR}, #{devicePasswd,jdbcType=VARCHAR},
        #{role,jdbcType=INTEGER}, #{company,jdbcType=VARCHAR}, #{describ,jdbcType=VARCHAR},
        #{lastLoginIp,jdbcType=VARCHAR}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{lastLogoutTime,jdbcType=TIMESTAMP},
        #{currLoginIp,jdbcType=VARCHAR}, #{currLoginTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER},
        #{parentId,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModify,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsUser"
            useGeneratedKeys="true">
        insert into acs_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="passwd != null">
                passwd,
            </if>
            <if test="devicePasswd != null">
                device_passwd,
            </if>
            <if test="role != null">
                `role`,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="describ != null">
                describ,
            </if>
            <if test="lastLoginIp != null">
                last_login_ip,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="lastLogoutTime != null">
                last_logout_time,
            </if>
            <if test="currLoginIp != null">
                curr_login_ip,
            </if>
            <if test="currLoginTime != null">
                curr_login_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModify != null">
                gmt_modify,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="passwd != null">
                #{passwd,jdbcType=VARCHAR},
            </if>
            <if test="devicePasswd != null">
                #{devicePasswd,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                #{role,jdbcType=INTEGER},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="describ != null">
                #{describ,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginIp != null">
                #{lastLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLogoutTime != null">
                #{lastLogoutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="currLoginIp != null">
                #{currLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="currLoginTime != null">
                #{currLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModify != null">
                #{gmtModify,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsUser">
        update acs_user
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="passwd != null">
                passwd = #{passwd,jdbcType=VARCHAR},
            </if>
            <if test="devicePasswd != null">
                device_passwd = #{devicePasswd,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                `role` = #{role,jdbcType=INTEGER},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="describ != null">
                describ = #{describ,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginIp != null">
                last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLogoutTime != null">
                last_logout_time = #{lastLogoutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="currLoginIp != null">
                curr_login_ip = #{currLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="currLoginTime != null">
                curr_login_time = #{currLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModify != null">
                gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsUser">
    update acs_user
    set `name` = #{name,jdbcType=VARCHAR},
      passwd = #{passwd,jdbcType=VARCHAR},
      device_passwd = #{devicePasswd,jdbcType=VARCHAR},
      `role` = #{role,jdbcType=INTEGER},
      company = #{company,jdbcType=VARCHAR},
      describ = #{describ,jdbcType=VARCHAR},
      last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      last_logout_time = #{lastLogoutTime,jdbcType=TIMESTAMP},
      curr_login_ip = #{currLoginIp,jdbcType=VARCHAR},
      curr_login_time = #{currLoginTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <sql id="Where">
        WHERE 1 = 1
        <if test="all != true">
            AND `del`=0
        </if>
        <choose>
            <when test="name != null and nameFuzzy">
                AND `name` like concat('%', #{name}, '%')
            </when>
            <when test="name != null">
                AND `name`= #{name}
            </when>
        </choose>
        <if test="nameList != null and nameList.size() &gt; 0">
            AND `name` in
            <foreach close=")" collection="nameList" item="name" open="(" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="company != null">
            AND company = #{company}
        </if>
        <if test="role != null">
            AND role=#{role}
        </if>
        <if test="parentId != null">
            AND parent_id=#{parentId}
        </if>
        <if test="status != null">
            AND status=#{status}
        </if>
        <if test="userIds != null and userIds.size() &gt; 0">
            AND id in
            <foreach close=")" collection="userIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="startLastLoginTime != null">
            AND last_login_time &gt;= #{startLastLoginTime}
        </if>
    </sql>

    <select id="getByFilter" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user
        <include refid="Where"/>
        <if test="limit != null">
            limit #{limit}, #{offset}
        </if>
    </select>

    <select id="getAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user
        where 1 = 1
        <choose>
            <when test="name != null and nameFuzzy">
                AND `name` like concat('%', #{name}, '%')
            </when>
            <when test="name != null">
                AND `name`= #{name}
            </when>
        </choose>
        <if test="nameList != null and nameList.size() &gt; 0">
            AND `name` in
            <foreach close=")" collection="nameList" item="name" open="(" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="company != null">
            AND company like concat('%', #{company}, '%')
        </if>
        <if test="role != null">
            AND role=#{role}
        </if>
        <if test="parentId != null">
            AND parent_id=#{parentId}
        </if>
        <if test="status != null">
            AND status=#{status}
        </if>
        <if test="userIds != null and userIds.size() &gt; 0">
            AND id in
            <foreach close=")" collection="userIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="limit != null">
            limit #{limit}, #{offset}
        </if>
    </select>

    <select id="countByFilter" resultType="int">
        select count(1)
        from acs_user
        <include refid="Where"/>
    </select>

    <update id="freeze">
    update acs_user set `status`=0 where `del`= 0 and name=#{name}
  </update>
    <select id="getAllUserNames" resultType="java.lang.String">
    select distinct name
    from acs_user
    where `del` = 0
  </select>
    <update id="deleteChildren">
    update acs_user set `del`=`id` where parent_id=#{userId}
  </update>
    <update id="undoDeleteChildren">
    update acs_user set `del`='0' where parent_id=#{userId} and `del` != '0'
  </update>
    <update id="deleteById">
    update acs_user set `del`=`id`, status = '0' where id=#{userId}
  </update>

    <update id="undoDel">
    update acs_user set `del`= 0  , status = '1' where id=#{userId} and `del` > 0
  </update>

    <select id="getDeletedUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user
        where `del` > 0 and gmt_modify >= #{begin} and gmt_modify &lt;= #{end}
    </select>

    <select id="getByUsername" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_user
        where `del`='0' and name=#{username}
    </select>

    <select id="company" resultType="string">
    select distinct company
    from acs_user
    where `del`='0'
  </select>


    <select id="userStatistics" resultType="com.jetron.nb.common.vo.UserStatisticsVo">
    select temp1.company,userNum,deviceNum
    from (
          select company,count(id) as userNum  from acs_user
          where del='0'
          <if test="company != null">
              AND company = #{company}
          </if>
          group by company
    ) temp1 inner join
    (
          select company,count(d.id) as deviceNum  from acs_user u
          left join acs_user_device d on u.id = d.belong_to and d.del ='0'
          where u.del='0'
          <if test="company != null">
            AND u.company = #{company}
          </if>
          group by company
    ) temp2 on temp1.company = temp2.company
  </select>

    <select id="countUserStatistics" resultType="int">
    select count(1)
    from (
          select company,count(id) as userNum  from 	acs_user
          where del='0'
            <if test="company != null">
                AND company = #{company}
            </if>
          group by company
    ) temp1 inner join
    (
          select company,count(d.id) as deviceNum  from acs_user u
          left join acs_user_device d on u.id = d.belong_to and d.del ='0'
          where u.del='0'
          <if test="company != null">
            AND u.company = #{company}
          </if>
          group by company
    ) temp2 on temp1.company = temp2.company
  </select>


</mapper>