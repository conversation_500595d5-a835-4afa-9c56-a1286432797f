<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsParameterConfigMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsParameterConfig">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="parameter_key" jdbcType="VARCHAR" property="parameterKey" />
        <result column="parameter_content" jdbcType="VARCHAR" property="parameterContent" />
        <result column="parameter_code" jdbcType="VARCHAR" property="parameterCode" />
        <result column="parameter_value" jdbcType="DOUBLE" property="parameterValue" />
        <result column="parameter_value_max" jdbcType="DOUBLE" property="parameterValueMax" />
        <result column="parameter_operator" jdbcType="INTEGER" property="parameterOperator" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, company, parameter_key, parameter_content, parameter_code, parameter_value, parameter_value_max, parameter_operator, insert_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsParameterConfig" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_parameter_config (company, parameter_key, parameter_content, parameter_value, parameter_value_max,
        parameter_operator, insert_time, parameter_code
        )
        values (#{company,jdbcType=VARCHAR}, #{parameterKey,jdbcType=VARCHAR},#{parameterContent,jdbcType=VARCHAR},
        #{parameterValue,jdbcType=DOUBLE},#{parameterValueMax,jdbcType=DOUBLE},#{parameterOperator,jdbcType=INTEGER},
        #{insertTime,jdbcType=TIMESTAMP},#{parameterCode,jdbcType=VARCHAR}
        )
    </insert>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_parameter_config
    </select>

    <sql id="Where">
        where 1=1
        <if test="parameterKey != null">
            and parameter_key = #{parameterKey,jdbcType=VARCHAR}
        </if>
        <if test="company != null">
            and company = #{company,jdbcType=VARCHAR}
        </if>
        <if test="parameterCode != null">
            and parameter_code = #{parameterCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_parameter_config
        <include refid="Where" />
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findGroupByCompanyModel"  resultMap="BaseResultMap">
        select
        id,company ,parameter_key
        from acs_parameter_config
        <include refid="Where" />
         and id  in (
            select min(id) from acs_parameter_config
            group by company ,parameter_key
        )
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findCount"  resultType="integer">
        select
        count(id)
        from acs_parameter_config
        <include refid="Where" />
    </select>



    <select id="findByCompany" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_parameter_config
        where company = #{company,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from acs_parameter_config where id = #{id}
    </delete>

    <select id="findByParameterKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_parameter_config
        where parameter_key = #{parameterKey,jdbcType=VARCHAR}
    </select>

    <select id="findById" parameterType="integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_parameter_config
        where id = #{id,jdbcType=INTEGER}
    </select>
    <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsParameterConfig">
        <!--@mbg.generated-->
        update acs_parameter_config
        set
        parameter_key = #{parameterKey,jdbcType=VARCHAR},
        parameter_content = #{parameterContent,jdbcType=VARCHAR},
        parameter_code = #{parameterCode,jdbcType=VARCHAR},
        parameter_value = #{parameterValue,jdbcType=DOUBLE},
        parameter_value_max = #{parameterValueMax,jdbcType=DOUBLE},
        parameter_operator = #{parameterOperator,jdbcType=INTEGER},
        insert_time = #{insertTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>