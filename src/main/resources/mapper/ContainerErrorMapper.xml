<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.ContainerErrorMapper">

    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.ContainerError">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="advise" jdbcType="VARCHAR" property="advise" />
        <result column="command" jdbcType="VARCHAR" property="command" />
    </resultMap>


    <sql id="Base_Column_List">
        id,code,advise,command
    </sql>


    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_container_error
    </select>

</mapper>