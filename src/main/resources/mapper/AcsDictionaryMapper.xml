<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDictionary">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dic_key" jdbcType="VARCHAR" property="dicKey" />
        <result column="dic_content" jdbcType="VARCHAR" property="dicContent" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dic_key, dic_content
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDictionary" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_dictionary (dic_key,dic_content
        )
        values (#{dicKey,jdbcType=VARCHAR}, #{dicContent,jdbcType=VARCHAR}
        )
    </insert>

    <select id="findByKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_dictionary
        where dic_key = #{key,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from acs_dictionary where id = #{id}
    </delete>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_dictionary
    </select>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_dictionary
        <trim prefix="where" prefixOverrides="and">
            <if test="dicKey != null">
                and dic_key = #{dicKey,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="findPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_dictionary
        <trim prefix="where" prefixOverrides="and">
            <if test="dicKey != null">
                and dic_key = #{dicKey,jdbcType=VARCHAR}
            </if>
        </trim>
        order by id asc
        <if test="limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="count" resultType="int">
        select
        count(id)
        from acs_dictionary
        <trim prefix="where" prefixOverrides="and">
            <if test="dicKey != null">
                and dic_key = #{dicKey,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>
</mapper>