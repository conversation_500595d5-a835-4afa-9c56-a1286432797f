<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsConfigShareMapper">



    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsConfigShare">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
    </resultMap>


    <sql id="Base_Column_List">
        id,company,config_id
    </sql>

    <select id="findList" parameterType="com.jetron.nb.dal.po.AcsConfigShare" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_config_share
        <trim prefix="where" prefixOverrides="and">
            <if test="company != null">
                and company = #{company,jdbcType=VARCHAR}
            </if>
            <if test="configId != null">
                and config_id = #{configId}
            </if>
        </trim>
    </select>

    <select id="findByCompany" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_config_share
        where company = #{company,jdbcType=VARCHAR}
    </select>



    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsConfigShare" useGeneratedKeys="true">
        insert into acs_config_share(
        company,config_id)
        values(
        #{company,jdbcType=VARCHAR},
        #{configId,jdbcType=INTEGER})
    </insert>


    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsConfigShare" useGeneratedKeys="true">
        insert into acs_config_share(
        company,config_id)
        values
        <foreach collection="list" item="data" separator=",">(
            #{data.company,jdbcType=VARCHAR},
            #{data.configId,jdbcType=INTEGER})
        </foreach>
    </insert>



</mapper>