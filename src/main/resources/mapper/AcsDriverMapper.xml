<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDriverMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDriver">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="protocol" jdbcType="VARCHAR" property="protocol" />
        <result column="path" jdbcType="VARCHAR" property="path" />
    </resultMap>

    <sql id="Where">
        where 1=1
        <if test="company != null">
            and a.company = #{company}
        </if>
        <if test="dictType != null">
            and a.dict_type = #{dictType}
        </if>
        <if test="dictLabel != null">
            and a.dict_label = #{dictLabel}
        </if>
    </sql>


    <insert id="insertList" parameterType="java.util.ArrayList">
        insert into acs_company_dict (company,gmt_create,remark,dict_type,dict_label, dict_value) values
        <foreach collection="list" separator="," item="data">
            (#{data.company,jdbcType=VARCHAR},#{data.gmtCreate,jdbcType=TIMESTAMP},#{data.remark,jdbcType=VARCHAR},
            #{data.dictType,jdbcType=VARCHAR},#{data.dictLabel,jdbcType=VARCHAR}, #{data.dictValue,jdbcType=VARCHAR})
        </foreach>
    </insert>


</mapper>