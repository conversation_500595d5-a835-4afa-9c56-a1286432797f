<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsCaptureDataMapper">



    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsCaptureData">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
        <result column="dev_temp" jdbcType="VARCHAR" property="devTemp" />
        <result column="rssi" jdbcType="VARCHAR" property="rssi" />
        <result column="signal_quality" jdbcType="VARCHAR" property="signalQuality" />
        <result column="ram" jdbcType="VARCHAR" property="ram" />
        <result column="cpu_ration" jdbcType="VARCHAR" property="cpuRation" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="company" jdbcType="VARCHAR" property="company" />
    </resultMap>


    <sql id="Base_Column_List">
        id,sn,dev_temp,rssi,signal_quality,ram,cpu_ration,insert_time,company,rsrq
    </sql>

    <sql id="Where">
        WHERE 1 = 1
        <if test="sn != null">
            and a.sn = #{sn}
        </if>
        <if test="start != null">
            and a.insert_time >= #{start,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null ">
            and  a.insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
        <if test="company != null">
            and a.company = #{company,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findBySnAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_data a
        <include refid="Where" />
    </select>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from acs_capture_data a
        <include refid="Where" />
        order by a.insert_time desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findLatestData" resultMap="BaseResultMap">
        select sn,signal_quality from acs_capture_data t1
        inner join (
            select max(insert_time) as insert_time,max(id) as id from acs_capture_data GROUP BY  sn
        ) t2 on t1.insert_time = t2.insert_time and t1.id = t2.id
    </select>

    <select id="findCount" resultType="int">
        select
        count(a.id)
        from acs_capture_data a
        <include refid="Where" />
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsCaptureData" useGeneratedKeys="true">
        insert into acs_capture_data (sn, dev_temp, rssi, signal_quality, ram, cpu_ration, insert_time, company
         ,rsrq)
        values(#{sn,jdbcType=VARCHAR},
        #{devTemp,jdbcType=VARCHAR},
        #{rssi,jdbcType=VARCHAR},
        #{signalQuality,jdbcType=VARCHAR},
        #{ram,jdbcType=VARCHAR},
        #{cpuRation,jdbcType=VARCHAR},
        #{insertTime,jdbcType=TIMESTAMP},
        #{company,jdbcType=VARCHAR})
         , #{data.rsrq,jdbcType=VARCHAR}
    </insert>


    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsCaptureData" useGeneratedKeys="true">
        insert into acs_capture_data (sn, dev_temp, rssi, signal_quality, ram, cpu_ration, insert_time, company
        ,rsrq)
        values
        <foreach collection="list" item="data" separator=",">(
        #{data.sn,jdbcType=VARCHAR},
        #{data.devTemp,jdbcType=VARCHAR},
        #{data.rssi,jdbcType=VARCHAR},
        #{data.signalQuality,jdbcType=VARCHAR},
        #{data.ram,jdbcType=VARCHAR},
        #{data.cpuRation,jdbcType=VARCHAR},
        #{data.insertTime,jdbcType=TIMESTAMP},
        #{data.company,jdbcType=VARCHAR}
        , #{data.rsrq,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <select id="findChartData" resultMap="BaseResultMap">
        select
            sn,dev_temp,rssi,signal_quality,ram,cpu_ration,insert_time,rank_sort as id
        from (
            select
                rank() over(order by insert_time) as rank_sort,sn,dev_temp,rssi,signal_quality,ram,cpu_ration,insert_time
            from acs_capture_data
            where
                company =  #{company,jdbcType=VARCHAR}
                and  insert_time  &gt;=  #{begin,jdbcType=TIMESTAMP} and insert_time &lt;=  #{end,jdbcType=TIMESTAMP}
        )
        as a
        where a.rank_sort %20 = 0 or a.rank_sort = 1
    </select>

    <delete id="delByDate" >
        delete from acs_capture_data
        where 1 = 1
            <if test="start != null">
                and insert_time >= #{start,jdbcType=TIMESTAMP}
            </if>
            <if test="end != null ">
                and  insert_time &lt; #{end,jdbcType=TIMESTAMP}
            </if>
  </delete>
</mapper>