<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDeviceFlowMonthMapper">

  <select id="findList" resultType="com.jetron.nb.dal.po.AcsDeviceFlowMonth">
        select t1.id,t1.company,t1.sn,t1.up_flow,t1.date,t1.gmt_create,t1.gmt_modify
        ,t2.alias,t2.true_ip,t2.model
        from acs_device_flow_month t1
        left join acs_user_device t2 on t1.sn = t2.sn and t2.del = 0
        where 1=1
        <if test="param.company != null and param.company != ''">
          and t1.company like concat('%', #{param.company}, '%')
        </if>
        <if test="param.sn != null  and param.sn != ''">
          and t1.sn like concat('%', #{param.sn}, '%')
        </if>
        <if test="param.alias != null  and param.alias != ''">
          and t2.alias = #{param.alias}
        </if>
        <if test="param.trueIp != null  and param.trueIp != ''">
          and t2.true_ip = #{param.trueIp}
        </if>
        <if test="param.model != null  and param.model != ''">
          and t2.model = #{param.model}
        </if>
        <if test="param.dateStart != null">
          and t1.date &gt;= #{param.dateStart}
        </if>
        <if test="param.dateEnd != null">
          and t1.date &lt;= #{param.dateEnd}
        </if>
        <if test="param.snList != null and param.snList.size() &gt; 0">
          and t1.sn in
          <foreach collection="param.snList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
  </select>
</mapper>