<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsDeviceMapper">
  <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.AcsDevice">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
    <result column="allocate_user_id" jdbcType="INTEGER" property="allocateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, model, sn, gmt_create, gmt_modify, allocate_user_id
  </sql>

  <sql id="Where">
    where 1=1
    <if test="model != null">
      and model=#{model}
    </if>
    <if test="sn != null">
      and sn=#{sn}
    </if>
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from acs_device
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from acs_device
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDevice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_device (model, sn, gmt_create, 
      gmt_modify)
    values (#{model,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModify,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.AcsDevice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into acs_device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="model != null">
        model,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModify != null">
        gmt_modify,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        #{gmtModify,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jetron.nb.dal.po.AcsDevice">
    <!--@mbg.generated-->
    update acs_device
    <set>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModify != null">
        gmt_modify = #{gmtModify,jdbcType=TIMESTAMP},
      </if>
      <if test="allocateUserId != null">
        allocate_user_id = #{allocateUserId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jetron.nb.dal.po.AcsDevice">
    <!--@mbg.generated-->
    update acs_device
    set model = #{model,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modify = #{gmtModify,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getBySnList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from acs_device
      where sn in
      <foreach collection="snList" open="(" separator="," close=")" item="sn">
        #{sn}
      </foreach>
  </select>

  <insert id="bachInsert">
    insert acs_device (`model`, `sn`) values
    <foreach collection="deviceList" separator="," item="device">
      (#{device.model}, #{device.sn})
    </foreach>
  </insert>

  <select id="getByFilter" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device
    <include refid="Where" />
    <if test="limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="countByFilter" resultType="int">
    select count(1)
    from acs_device
    <include refid="Where" />
  </select>
  <delete id="deleteBySn">
    delete from acs_device where sn=#{sn}
  </delete>

  <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device
    where id in
    <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="getUnregisteredDeviceInUse" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    d.id
    from acs_device d
    inner join acs_user_device ud on d.sn = ud.sn
    where ud.del = 0 and d.id in
    <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="updateByPrimaryKeys" parameterType="java.util.Map">
    update acs_device
    set allocate_user_id = #{allocateUserId, jdbcType=INTEGER}
    where id in
    <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="findDevicesWithAllocatedUsers" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device
    where allocate_user_id != -1
  </select>

  <select id="findDevicesByUserIds" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from acs_device
    where allocate_user_id in
    <foreach collection="userIds" index="index" item="userId" open="(" close=")" separator=",">
      #{userId}
    </foreach>
  </select>

</mapper>