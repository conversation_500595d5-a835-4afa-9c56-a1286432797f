<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsPointMapper">


    <insert id="insertList" parameterType="java.util.ArrayList">
        insert into acs_point
        (company,device_id,dev_name,point,remark
        ,gmt_create,gmt_modify)
        values
        <foreach collection="list" separator="," item="data">
            (#{data.company,jdbcType=VARCHAR}
            ,#{data.deviceId,jdbcType=VARCHAR},#{data.devName,jdbcType=VARCHAR}
            ,#{data.point,jdbcType=OTHER,typeHandler=com.jetron.nb.common.handler.MySqlJsonHandler}
            ,#{data.remark,jdbcType=VARCHAR}
            ,#{data.gmtCreate,jdbcType=TIMESTAMP},#{data.gmtModify,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>