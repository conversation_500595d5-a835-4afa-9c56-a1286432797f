<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.mqtt.AcsMqttOnlineMapper">
    <resultMap id="BaseResultMap" type="com.jetron.nb.dal.po.mqtt.AcsMqttOnline">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime"/>
        <association property="acsUserDevice" javaType="com.jetron.nb.dal.po.AcsUserDevice" >
            <!-- id:声明主键，表示 college_id 是关联查询对象的唯一标识-->
            <result property="alias" column="alias" />
            <result property="position" column="position" />
            <result property="id" column="userDeviceId" />
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        a.id, a.sn,a.status,a.insert_time
    </sql>

    <sql id="Where">
        WHERE 1 = 1
        <if test="sn != null">
            AND a.sn=#{sn}
        </if>
        <if test="start != null">
            and a.insert_time >= #{start,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null ">
            and a.insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <sql id="Where2">
        WHERE locate('idPub',a.sn) = 0
        <if test="start != null">
            and a.insert_time >= #{start,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null ">
            and a.insert_time &lt; #{end,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_mqtt_online a
        <include refid="Where"/>
    </select>

    <select id="findPage" resultMap="BaseResultMap">
        select
        d.alias,
        d.position,
        d.id as userDeviceId,
        a.id, a.sn,a.status,a.insert_time
        from (
            select
            (case when locate('idSub',a.sn) > 0 then  SUBSTRING(a.sn,6) else a.sn end ) as sn,
            a.id,a.status,a.insert_time
            from acs_mqtt_online a
            <include refid="Where2"/>
        )  as a
        left join acs_user_device d on a.sn = d.sn and d.del = '0'
        <include refid="Where"/>
        order by a.insert_time desc
        <if test="limit != null">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="findCount" resultType="int">
        select
        count(a.id)
        from (
            select
            (case when locate('idSub',a.sn) > 0 then  SUBSTRING(a.sn,6) else a.sn end ) as sn,
            a.id,a.status,a.insert_time
            from acs_mqtt_online a
            <include refid="Where2"/>
        )  as a
        <include refid="Where"/>
        order by insert_time desc
    </select>

    <select id="findLatest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acs_mqtt_online a
        where a.sn in (#{sn},concat('idSub', #{sn}))  and a.insert_time &lt;= #{start,jdbcType=TIMESTAMP}
        order by a.insert_time desc
        limit 0,1
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jetron.nb.dal.po.mqtt.AcsMqttOnline"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into acs_mqtt_online (sn,status,insert_time
        )
        values (#{sn,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{insertTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <delete id="delete" >
    delete from acs_mqtt_online
    where insert_time between  #{start,jdbcType=TIMESTAMP} and  #{end,jdbcType=TIMESTAMP}
    <trim>
        <if test="sn != null and sn != ''">
            and sn in (#{sn},concat('idPub', #{sn}),concat('idSub', #{sn}))
        </if>
    </trim>
  </delete>
</mapper>