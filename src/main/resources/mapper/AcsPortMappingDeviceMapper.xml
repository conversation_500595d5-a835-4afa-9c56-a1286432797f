<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jetron.nb.dal.dao.AcsPortMappingDeviceMapper">


    <insert id="insertList" >
       insert acs_port_mapping_device
       (id,sn,status,name,protocol,gateway_port,dev_ip,dev_port,gmt_create)
       values
       <foreach collection="list" item="item"  separator=",">
           (#{item.id},#{item.sn},#{item.status},#{item.name},#{item.protocol}
           ,#{item.gatewayPort},#{item.devIp},#{item.devPort},#{item.gmtCreate}
           )
       </foreach>

    </insert>

</mapper>